{"metadata": {"name": "<PERSON>, 54F", "scraped_at": "2025-09-03T00:32:22.403841", "script_url": "https://www.oscer.ai/pwf/script/AlfnNyxRzOe4"}, "tabs": {"doctor_information": {"Intro": "You are a medical student working in a General Practice clinic. You have been asked to see <PERSON>, a 54 year old female (she/her), presenting with chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["<PERSON> is a 54 year old woman.", "Her preferred pronouns are she/her.", "She is presenting to your GP practice complaining of recent episodes of chest pain."], "Persona": ["I am quite an anxious person.", "I have been having this pain in my chest for a couple of months and my google searches have been coming up with some concerning results!"], "HOPC": ["I have noticed a vague pressure in my chest when I go for a walk over the last few months.", "I seem to notice it only after about 1 km of walking at a moderate to high intensity.", "It seems to settle within 10 or so minutes of me stopping. It never comes on at rest but it happens every single time I walk more than a km.", "I feel some shortness of breath when I have the chest pain.", "At first I put it down to getting older and losing my fitness over the last few years as I have had less time for exercise since my work promotion. I recently googled it though and have become a little more concerned!", "The feeling is just in my chest and not anywhere else. I don’t have any neck, jaw or arm discomfort.", "I have never had palpitations, nausea or vomiting with this.", "I have never lost consciousness or felt very dizzy.", "I do not notice unusually profuse sweating when these symptoms come on.", "I have not had a cough, sore throat, runny nose or fever.", "I have not been feeling fatigued recently.", "I do not get any calf pain."], "PMHx": ["I have a bit of osteoarthritis in my knees from my volleyball days but I don’t have any real worries! I sometimes take a bit of panadol osteo on the cold days when I feel it the most.", "I have never been diagnosed with costochondritis, anxiety or panic attacks, kidney disease or diabetes.", "I have never really had my cholesterol checked and I can’t remember the last time my blood pressure was checked.", "I have never had any tests done on my heart before.", "I am on implanon and only get very light periods.", "I do not take any regular prescription or complementary medications.", "I do not have any allergies.", "I am not very good at remembering to get my flu vaccines - it has been a few years since my last one. All my other vaccines should be up to date though!"], "FMHx": ["My dad died of a heart attack when he was 63 which is one of the reasons I thought this was worth checking out.", "<PERSON> had Alzheimers dementia and died of pneumonia when she was 80.", "My mum had quite bad anxiety. I think that’s where I get some of my worrying traits from.", "I do not know of anyone in my family who has died suddenly or in a car accident."], "SHx": ["I live with my partner of 6 months who only just moved in with me.", "I identify as homosexual.", "I work as a criminal lawyer in public defence. I love my job but I could definitely have picked a less stressful career!", "I have been smoking since I was 18. I have had about 1 pack per day (so about 20 cigarettes each day) during this time.", "I have tried quitting a couple of times. I don’t feel like quitting as I use smoking to manage my stress levels at work.", "I drink probably more than I should! I usually have a glass of wine with dinner on weeknights and will go out with friends one night per week. On these nights I might have 5 or 6 glasses of wine.", "I could probably eat better. Breakfast is porridge and lunch is usually out somewhere near the courts.", "My partner and I both work very long hours and get takeaway 3-4 nights per week. The other nights I will make up quick stir-frys.", "I exercise by going for walks in the state forest near my house.", "I walk 2-3 kms 3-4 times per week, usually early in the morning before work."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Burning", "Ripping", "Cramping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 2, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Asking Generally", "Affecting Daily Life", "Requiring Hospitalisation", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Offset", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Resting", "Injuries", "Palpating", "Eating or Diet", "Context at Onset", "Physical Activity", "Emotional or Stress", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 7, "subcategories": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Arrhythmia", "Heart Disease", "Asking Generally", "Coronary Artery Disease", "Hypertrophic Cardiomyopathy"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}