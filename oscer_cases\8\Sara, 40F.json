{"metadata": {"name": "Sara, 40F", "scraped_at": "2025-09-05T12:05:53.103515", "script_url": "https://www.oscer.ai/pwf/script/0kynieWHeEvT"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 40 year old woman presenting to the ED with blood in her vomit. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I'm 40 years old and my pronouns are she/her.", "I'm presenting to the ED not feeling quite like myself, having noticed blood in my vomit."], "Persona": ["I'm normally very cheerful but I feel quite ill so my speech is a little slower than usual."], "HOPC": ["I have come in to the ED because I have vomited blood.", "The blood looks like coffee grounds and I vomited a couple teaspoons per vomit.", "The blood is mixed in with the food.", "I've vomited blood twice today, once in the morning and again in the waiting room.", "There is no bile in my vomit and very minimal digested food.", "I have never vomited blood before.", "I have had a gnawing pain in my stomach for about a week. It gets worse before meals, and is relieved by eating. I would rate it a 5/10.", "Sometimes I get reflux and I feel a burning pain in my chest after I eat. This does not feel like that.", "Since yesterday, my stool is black.", "My stool smells a whole lot more than usual and is rather sticky.", "I do not have a fever.", "I have not been sick recently.", "I do not feel dizzy, and I don't feel short of breath.", "I have not had any weight loss, loss of appetite or night sweats.", "I do not feel tired, but I definitely feel ill, unable to do most things."], "PMHx": ["I have endometriosis that I manage at home with ibuprofen. I cannot have any children because of it.", "I take ibuprofen until the pain is settled, I don't keep track of how much I use.", "The endometriosis is under control and not what I am here to talk about today.", "I do not have a history of cancer."], "FMHx": ["My parents are really healthy, nothing being passed down through the family as far as I'm aware."], "SHx": ["I live at home with my husband. We have a cocker spaniel, <PERSON>, together.", "I work as a full time town planner.", "I drink occasionally. I enjoy a couple cold beers on the weekend.", "I do not smoke. I used to smoke in my youth, maybe 5 cigarettes a day from my early 20s to late 20s.", "I have never done any drugs."]}, "marking_rubric": {"HOPC": [{"title": "Haematemesis", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Black", "Asking Generally"]}, {"title": "Character", "score": 1, "items": ["Painful", "Fresh Blood", "Coffee Grounds", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Blood", "Amount of Vomit"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Blood", "Frequency of Vomiting"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1}, {"title": "Painful Swallowing", "score": 1, "items": ["Painful Swallowing"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Black and Tarry Stools"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["St<PERSON>ch Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Helicobacter pylori"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Gastritis", "Asking Generally", "Peptic Ulcer Disease", "Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["NSAID", "Ibuprofen", "Bisphosphonates", "Iron Supplement", "Amount of NSAIDs", "Asking Generally", "Duration of NSAIDs"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Duration"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Carcinoma", "Asking Generally", "Helicobacter pylori", "Peptic Ulcer Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}