{"metadata": {"name": "<PERSON>, 20F", "scraped_at": "2025-09-03T00:03:11.067140", "script_url": "https://www.oscer.ai/pwf/script/felBXgtEuDos"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the emergency department. <PERSON> is a 20 year old female presenting with a headache. You are the first doctor to see <PERSON>. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 20 year old woman and I study marketing at university. My pronouns are she/her.", "I’ve come to the doctor today because I have a really bad headache."], "Persona": ["I’m in a lot of pain and I am not in good spirits.", "My patience is low and I wish the doctor would hurry up and fix my headache.", "I’m sick of getting these headaches."], "HOPC": ["I have a pain on the left side of my head.", "The pain does not radiate.", "The pain is throbbing in nature.", "The pain is very severe, a 9 out of 10.", "The pain began gradually yesterday morning, about 24 hours ago now, and hasn’t gotten any better.", "When the pain began I noticed some flashing lights in my peripheral vision first, and then the headache began.", "The flashing was like lightning around the edges of my vision. This has never happened before", "I haven’t been sleeping well recently and I have been stressed at university due to exams.", "It’s best if I do not move.", "I can’t stand bright lights and loud noises right now.", "I feel nauseous but I haven’t vomited.", "I have no fever and my neck is not stiff."], "PMHx": ["I have asthma and have been getting headaches all my life.", "I use a salbutamol inhaler for my asthma.", "I started the oral contraceptive pill 4 years ago.", "I have no allergies and my vaccinations are up to date."], "FMHx": ["My mother and maternal grandfather have <PERSON><PERSON><PERSON>-<PERSON><PERSON> syndrome.", "I have a sister who is healthy.", "I do not know of anyone else in the family with medical problems."], "SHx": ["I don’t smoke and I have never smoked.", "I like to have a few drinks on the weekend, about 5 standard drinks on a night out.", "I don’t take any recreational drugs.", "I study marketing at university and have my exams coming up which is very stressful.", "I do not have a job.", "I live at home with my parents and am happy there although I will be looking to move out soon.", "I have a pretty healthy and varied diet."]}, "marking_rubric": {"HOPC": [{"title": "Headache", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Tension", "Throbbing", "Thunderclap", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Sided", "Asking Generally", "Generalised or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Neck", "Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Affecting Work", "Ability to Sleep", "Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Darkness", "Quietness", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Stress", "Context at Onset", "Lifestyle Changes", "Infective Contacts", "Morning or Night-time", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Crying", "score": 1, "items": ["Crying"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Muscle Tension", "score": 1, "items": ["<PERSON><PERSON>", "Neck", "Asking Generally"]}, {"title": "Neck Stiffness", "score": 1, "items": ["Neck Stiffness"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Blurry Vision", "Double Vision", "Asking Generally"]}, {"title": "Nasal <PERSON>harge", "score": 1}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Speech Difficulty", "score": 1, "items": ["Comprehension", "Speech Difficulty"]}, {"title": "Sensitive to Light", "score": 1, "items": ["Sensitive to Light"]}, {"title": "Sensitive to Sound", "score": 1, "items": ["Sensitive to Sound"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Agitation", "Confusion", "Hallucinations", "Depression or Low Mood"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Stroke", "Migraine", "Asking Generally", "Cluster Headaches"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Headache", "Migraine", "Brain Cancer", "Asking Generally", "Cluster Headaches", "Neurological Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Caffeine", "Amount of Fluids", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Current Stress", "Life Stressors"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}