{"metadata": {"name": "<PERSON>, 68M", "scraped_at": "2025-09-02T23:52:27.882165", "script_url": "https://www.oscer.ai/pwf/script/Saj4kbMYZ64s"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 68 year old male. He is presenting to the emergency department complaining of chest pain. Please take a focused history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 68yr old male and a retired teacher. My pronouns are he/him.", "I have come to the doctor today because I have severe chest pain."], "Persona": ["I’m a relaxed person and a family man.", "I’m worried about the chest pain and fear that it is a heart problem."], "HOPC": ["The pain started this morning while I was watching TV.", "The pain started 30 minutes ago.", "I felt pain come on in the centre of my chest.", "I’m getting some pain in my left shoulder and left arm. I’m still experiencing the pain and I would say that it is getting worse.", "When I move and change my position the chest pain isn’t affected.", "The chest pain feels like a pressure pain. It feels kind of like there is an elephant sitting on my chest.", "The chest pain doesn’t feel sharp or tight.", "The pain seems to be getting so much worse that I can’t even walk.", "Breathing or lying down doesn’t make the chest pain worse.", "I feel exhausted because of it. I’ve never had this kind of pain before.", "I have been feeling short of breath since the chest pain started.", "I have noticed that my skin has been pale.", "I am sweating a lot.", "I also have been feeling my heart pounding strongly. This happened when the chest pain started. The rhythm is regular but very fast.", "I’m sweating a lot. I feel nauseous but I haven’t vomited.", "I don’t have a fever.", "I do not have a cough.", "I have not been especially stressed recently.", "I have noticed some chest pain on exertion over the last few months. It feels like some tightness in my chest. It is normally relieved by resting.", "I haven’t had any long haul flights or prolonged periods of immobility recently. I have also not had any recent surgeries."], "PMHx": ["My cholesterol isn’t great. I’ve been taking atorvastatin for the past couple of years. I am mostly good at taking it.", "My <PERSON> has mentioned a couple of times that I should lose weight.", "She has also said that my blood pressure is high. Last time I checked it was 150/90. I am not on any medications yet though. The GP is thinking about starting me on something.", "I have not had any tests performed on my heart before.", "I have not had any heart problems in the past.", "I don’t have any autoimmune diseases.", "I do not have asthma.", "I don’t have <PERSON><PERSON><PERSON>s syndrome.", "I do not have anxiety."], "FMHx": ["My father died of a heart attack in his 60s.", "My brother also struggles with his blood pressure and cholesterol.", "I have 2 adult sons, they are both well."], "SHx": ["My diet is not that great. I think I eat too much fat.", "I’ve been drinking 1-2 beers every night since my late teens. Sometimes I have 4 cans on the weekend. I don’t have any desire to reduce my alcohol consumption. I do not see it as a problem.", "I have been smoking a pack a day since my late teens. I’m not thinking about quitting at this age. I think that ship has sailed for me. I haven’t used any illicit drugs.", "I don’t exercise.", "I am happily married and have a bunch of family and friends I can always count on. I live with my wife. I’m pretty relaxed.", "I have 2 adult sons who live out of home.", "I am independent in all aspects of my life.", "I used to work as a teacher, but I am retired now."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Burning", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Ideas", "Injuries", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Pulmonary Embolism", "Deep Vein Thrombosis"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Lung Cancer", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Statins", "Antacids", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Patient Demographics", "score": 1, "items": ["Age", "Weight"]}]}], "Basics": [{"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}