{"metadata": {"name": "<PERSON>, 25F", "scraped_at": "2025-09-05T12:52:14.531742", "script_url": "https://www.oscer.ai/pwf/script/N604v6DqydCM"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement in the Emergency Department. <PERSON> is a 25 year old woman (she/her) presenting to the ED with a rash. Please take a detailed history from her with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm 25 years old.", "My pronouns are she/her.", "I've come in to the ED because of this concerning rash!"], "Persona": ["I am very stressed about the rash. What if all my skin falls off?", "I have been living with lupus for 6 years now."], "HOPC": ["The rash came on suddenly yesterday after I had breakfast.", "The rash is patchy all over my torso, arms and legs.", "The rash looks flat and red.", "I have also noticed a few blisters, that are making it look like my skin is falling off.", "The rash has been spreading rapidly.", "It is extremely painful to touch. If I had to put a number on it, it would be a 7-8/10. It's awful!", "I've had difficulty breathing for four days too.", "When I had the difficulty breathing I took my temperature and it was 39.6 degrees.", "I also had some general aches and joint pain around the same time I noticed the fever, which was a day or two before the rash came on.", "My lips look red and crusted and I have developed a few mouth ulcers.", "I think I might have pink eye as well. My eyes have looked red since this morning."], "PMHx": ["I was diagnosed with Systemic Lupus Erythematosus at age 19.", "I started taking cotrimoxazole for a severe UTI six days ago.", "I have been taking Plaquenil since I was diagnosed with lupus.", "I take a vitamin D supplement every day and I have done so for the past 6 years.", "I have never had an anaphylaxis reaction to anything.", "I have never been hospitalised and have never had any surgery.", "I don't have any allergies.", "My vaccinations are up to date."], "FMHx": ["Both my parents are healthy and well.", "I have a younger brother. He broke his leg once when he fell off his bike, but other than that he is well."], "SHx": ["I am studying my PhD in economics.", "I am in a 2 year relationship with my boyfriend, <PERSON>. We are sexually active.", "I make sure to avoid the sun ever since I got diagnosed with <PERSON><PERSON>.", "I have never smoked cigarettes, I think they're disgusting.", "I enjoy drinking tequila on ice a couple times a month.", "I like to have 2 drinks on one occasion, I never drink more than that.", "I did cocaine once on New Years Eve a few years ago, but have never done any other drugs or anything since.", "I try to eat a balanced diet with meats, vegies and fruits.", "I exercise regularly. I try to run everyday."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Black", "Colour"]}, {"title": "Character", "score": 2, "items": ["Flat", "Itch", "Pain", "<PERSON><PERSON>", "Sc<PERSON>", "Raised", "Bleeding", "Blistering", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally", "Location at Onset"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Number of Rashes"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 2, "items": ["Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Topical Creams", "Asking Generally", "Topical Steroids"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Patient Ideas", "Skin Products", "Context at Onset", "Lifestyle Changes", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "Medications", "score": 4, "subcategories": [{"title": "Medication Side Effects", "score": 1, "items": ["Allergies", "Medication Side Effects"]}, {"title": "Prescription Medications", "score": 2, "items": ["Duration", "Antibiotics", "Sulphonamides", "Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Duration", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 2, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Immunodeficiency History", "score": 1, "items": ["HIV"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion", "Patient Questions"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}