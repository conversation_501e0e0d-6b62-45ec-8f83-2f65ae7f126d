{"metadata": {"name": "<PERSON>, 66M", "scraped_at": "2025-09-03T00:24:37.643947", "script_url": "https://www.oscer.ai/pwf/script/0aCWDNKOuEIP"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 66 year old male presenting to outpatients with some abdominal distension. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I’m <PERSON>. I’m a 66 year old male.", "I’ve come to the emergency department because my doctor referred me. My stomach is bloated.", "I actually do not know what is happening."], "Persona": ["I am irritable today because I waited at the doctor’s too long.", "Also, no one has told me what is wrong with me so far.", "Overall, I am very frustrated."], "HOPC": ["I came in today because I’ve been a bit bloated over the past few weeks.", "The bloating is getting worse by the hour.", "The bloating is more to the top of my stomach.", "Because I am overweight my stomach looks a bit puffy usually, but lately it’s just been out of the ordinary.", "I’m feeling a bit nauseated and sweaty.", "I’m feeling a bit itchy.", "My skin and eyes are also looking a bit yellow.", "I noticed these 3 days ago.", "I have had no shortness of breath.", "I’m not experiencing any chest pain.", "I don’t have any leg swelling.", "I don’t have any tattoos.", "My urine and bowel habits have been fine.", "I don’t have a fever or chills.", "I have not had any night sweats."], "PMHx": ["I had a heart attack 5 years ago.", "I’m taking aspirin 100mg for it.", "I've also been prescribed Rosuvastatin 40mg and Candesartan 32mg as well.", "Otherwise the GP says the heart is fine.", "I had asthma when I was a kid.", "Sometimes my nerves work funny.", "My doctor said my sensation changes are because of some vitamin deficiency. So I’m taking supplements for it.", "I don’t have a history of hepatitis infection.", "I haven’t had any blood transfusions.", "I don’t have hypertension or diabetes."], "FMHx": ["My father was an alcoholic and he died at 70 with heart disease I think.", "My mum died of old age a couple of years ago. That’s when I started drinking."], "SHx": ["I’ve been dependent on alcohol for 10 years.", "I usually drink half a dozen beers each night.", "On the weekends I usually go for something stronger like vodka.", "I’ve tried and tried to quit but it’s just too hard.", "I haven’t used IV drugs. A few times I’ve taken coke though.", "I smoke cigarettes occasionally. I haven’t been able to smoke as much as I did because of how expensive cigarettes are.", "I don’t really do a lot of personal hygiene.", "I usually just shop from convenience stores which are easy and quick to buy.", "I am living in government-subsidized housing.", "I am on government pension.", "I don’t feel financially secure.", "I live by myself and have a pet dog."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Distension", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Soft or Hard", "Asking Generally", "Solid, Liquid or Gas"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally", "Defecating or Flatulating"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Patient Ideas", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally", "Increased Urination"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Constipation", "Ability to Flush", "Asking Generally", "Malodorous Stools", "Black and Tarry Stools", "Most Recent Bowel Movement"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Memory Loss"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Liver Cancer", "Liver Failure", "Asking Generally", "Pancreatic Cancer", "Chronic Liver Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Tattoos", "Piercings", "Cancer Screening", "Blood Transfusion"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Hepatitis", "Bowel Cancer", "Liver Cancer", "Heart Disease", "Liver Disease", "Asking Generally", "Alcohol Abuse Disorder"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Streetfood", "Fatty Foods", "Asking Generally", "Contaminated Food or Water"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["<PERSON>e Drinking", "Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline", "Reason for Drinking"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexual Practices", "Number of Sexual Partners"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}, {"title": "Sexually Transmitted Infections", "score": 1, "items": ["Hepatitis C", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}