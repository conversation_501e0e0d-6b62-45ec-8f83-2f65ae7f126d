{"metadata": {"name": "<PERSON><PERSON><PERSON>, 69F", "scraped_at": "2025-09-05T12:21:50.893398", "script_url": "https://www.oscer.ai/pwf/script/d1mFypFfCQES"}, "tabs": {"doctor_information": {"Intro": "You are a medical student working in the emergency department. Your next patient is <PERSON><PERSON><PERSON>, a 69 year old female (she/her) who presents with noisy breathing. Please take a focused history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON>.", "I am a 69-year-old female (she/her).", "I was brought into the emergency department by my husband."], "Persona": ["I am generally a relaxed and happy person.", "I'm used to having respiratory issues so I tend to have a high threshold for presenting to the doctors."], "HOPC": ["I've developed a wheeze and it is getting worse.", "My husband first noticed it 4 days ago, but it has since been getting more noisy.", "I also notice that I cannot walk as far as I normally can before needing to rest.", "Normally I can walk about 1km before feeling breathless, now I can only walk about 100m.", "When I breathe in, i get this sharp pain/discomfort in my chest which developed at the same time as the other symptoms. It mainly localises to the right hand side of my chest.", "I have had a cough for as long as I can remember, but I have noticed that it has gradually worsened in the last few days.", "Occassionally I would bring up this white coloured phlegm, but since this has started I have been coughing up about a teaspoon of thick yellow phlegm. There is no blood in it.", "I feel like a have a temperature and am quite clammy.", "I have no other constitutional symptoms."], "PMHx": ["Twice in the previous 2 years I have been brought to hospital for 'chest infections'. I was given steroids and some antibiotics and they cleared up both times.", "I have never had any surgeries.", "I was formally diagnosed with COPD when I was 64 years old after they conducted some respiratory tests on me.", "The only regular medication I have is an inhaler which I have been instructed by my GP to use if i ever feel breathless, I think it is called Salbutamol and I tend to use it three or four times a week.", "I was immunised as a child, yet I haven't had the pneumovax or influenza vaccinations. I have had the COVID-19 vaccine.", "I do not have any allergies."], "FMHx": ["My father passed away of a heart attack when he was 70-years-old.", "My mother is 93-years-old and lives in a nursing home, however she has begun showing signs of dementia.", "I have a 67-year-old brother and 72-year-old sister, all of whom are doing well as far as I know.", "I have no children."], "SHx": ["I live at home with my husband and our three dogs.", "I no longer work, yet previously I worked as a administrator for the local council. My husband is also retired.", "Oridinarily I try to walk the dogs most days as my exercise, however I haven't been able to do that since all these symptoms begun.", "My diet largely consists of meat and three vegetable styles, yet occassionally on friday nights we will have takeaway.", "I only drink the occassional glass of red wine, maybe once a week.", "I have smoked a packet a day for 50 years. I quit when i was diagnosed.", "I haven't done any travel in the last 5 years."]}, "marking_rubric": {"HOPC": [{"title": "Noisy Breathing", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Speak", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Temporal", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Time Course", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Inhalers", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Exercise", "Recent Illness", "Asking Generally", "Context at Onset"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Chest Tightness", "score": 1, "items": ["Chest Tightness"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Hayfever", "Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally", "Contact with Covid-19 Cases"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}