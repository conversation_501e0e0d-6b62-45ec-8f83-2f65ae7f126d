{"metadata": {"name": "Ismael, 45M", "scraped_at": "2025-09-05T13:03:47.931646", "script_url": "https://www.oscer.ai/pwf/script/ZeN8hAmzTImi"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on your Emergency Medicine rotation. <PERSON><PERSON><PERSON> is a 45 year old male (he/him) who has presented to the hospital with vomiting. Please take a history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON> and I'm a 45 year old male (he/him).", "I've come into the hospital today because I've been unable to stop vomiting."], "Persona": ["I can be a bit of an anxious and up and down person, so this vomiting has got me really worried.", "I'm concerned because I've never had a food poisoning last so long, I think something could be really wrong."], "HOPC": ["I've been vomiting for two days straight now.", "I think I had some bad sushi at work, because afterwards all the vomiting and diarrhoea started.", "The vomiting hasn't stopped at all.", "I was vomiting 2 or 3 times at the start, now I feel like I've vomited that much and it's only lunchtime.", "The vomit is a usual colour and consistency, I haven't noticed anything unusual about it.", "I think the vomiting has gotten worse today, I've definitely been vomiting more.", "Nothing helps or makes the vomiting worse.", "The vomiting is like a 7 out of 10.", "I think I've probably become dehydrated with all the vomiting and diarrhoea I've had. I think I'm urinating less.", "I've been feeling nauseous but that makes sense I guess with how much I've been vomiting.", "I've also had diarrhoea.", "The diarrhoea started this morning.", "I've started to develop these tremors today as well.", "I remember getting a bit of a tremor when I started taking lithium but this is much worse, it feels like my hands are shaking much more.", "I haven't been slurring my speech.", "I haven't lost consciousness or started feeling confused or drowsy.", "I haven't had a seizure.", "I haven't had any problems walking.", "It doesn't feel like my muscles have stiffened up and gone rigid.", "I haven't had any dizziness.", "I haven't had any fevers.", "I have not been urinating more, or waking up at night to go to the toilet."], "PMHx": ["I was diagnosed with bipolar disorder when I was 23 years old.", "Ever since my diagnosis, I've been taking Lithium to level me out.", "Other than the lithium, I'm not taking any other medications.", "I don't take any diuretics or heart medications.", "I had a bout of food poisoning two days ago after some dodgey sushi. I've been feeling off but have still managed to take my dose of Lithium each day."], "FMHx": ["My mother has suffered a lot of anxiety throughout her life since she was a teenager. Other than that, no one in my family has any medical conditions, they're all fine."], "SHx": ["I live with my partner <PERSON>, and our pet cat <PERSON>.", "Our families are supportive, we both have good relationships with them.", "I work at Coles full-time.", "We're doing okay financially. I mean we both don't have the best jobs in the world, but we've never had kids so we don't have all those expenses.", "I used to smoke cannabis on a daily basis when I was younger, I stopped when I was 26 years old and haven't touched it since then.", "I drink alcohol, but not too much. I might average five beers in an average week. I've never been a heavy drinker.", "I still smoke cigarettes. I normally smoke 10 a day, it's been like this since I was 17 years old."]}, "marking_rubric": {"HOPC": [{"title": "Vomiting", "score": 12, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Asking Generally", "Yellow, Brown or Green"]}, {"title": "Character", "score": 1, "items": ["Consistency", "Asking Generally"]}, {"title": "Substances", "score": 1, "items": ["Food", "Blood or Coffee Grounds"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Asking Generally", "Cups or Teaspoons"]}, {"title": "Clinical Markers", "score": 1, "items": ["Causes Thirst", "Decreased Urination"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Most Recent Episode", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Eating or Diet", "Context at Onset", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Altered Mental Status", "score": 2, "items": ["Confused", "Drowsiness", "Restlessness"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["Agitation"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Seizures", "score": 1, "items": ["Seizures"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Muscle Spasm", "score": 1, "items": ["Muscle Spasm"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Asking Generally"]}], "PMHx": [{"title": "Medications", "score": 3, "subcategories": [{"title": "Medication Side Effects", "score": 1, "items": ["Medication Side Effects"]}, {"title": "Prescription Medications", "score": 2, "items": ["Lithium", "Duration", "Adherence", "Antidepressants", "Asking Generally", "Past Medications"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Appendectomy", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Appendectomy", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Diet at Baseline"]}, {"title": "Suicidal Risk", "score": 1, "items": ["Suicidal Risk"]}, {"title": "Social Support", "score": 1, "items": ["Family Support"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["Illicit Drugs"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}