import time
import re
import os
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager


def extract_study_gap_patients():
    """Extract patient names and bottom strip colors from Study Gap section"""

    # Setup Chrome options with anti-detection measures
    options = Options()
    options.add_argument("--start-minimized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    driver = webdriver.Chrome(service=Service(
        ChromeDriverManager().install()), options=options)

    # Execute script to remove webdriver property
    driver.execute_script(
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        # 1. Open the sign-in page directly
        driver.get("https://www.oscer.ai/signin")

        # 2. Fill in credentials
        email_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "email"))
        )
        password_input = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.ID, "password"))
        )

        email_input.clear()
        email_input.send_keys("<EMAIL>")
        password_input.clear()
        password_input.send_keys("techrise01badoscer")

        # 3. Click the "Sign in" button
        login_button = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable(
                (By.XPATH, "//button[.//span[text()='Sign in']]"))
        )
        login_button.click()

        # 4. After login, wait for dashboard to load
        WebDriverWait(driver, 20).until(
            EC.url_contains("/dashboard")
        )

        time.sleep(5)  # Allow page to fully load

        # 5. Find the Study Gap section using text content
        study_gap_section = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//div[contains(text(), 'Study Gap')]/parent::div/parent::div"))
        )

        # 6. Find all patient cards in the Study Gap section using more stable selectors
        # Look for swiper slides that contain patient information
        patient_cards = study_gap_section.find_elements(
            By.XPATH, ".//div[contains(@class, 'swiper-slide')]//div[contains(@style, 'border-bottom-color')]")

        patients_data = []

        for card in patient_cards:
            try:
                # Look for patient name - it should be in a div that contains text like "Name, AgeGender"
                # We'll search for divs that contain a comma followed by a number and M/F
                name_elements = card.find_elements(
                    By.XPATH, ".//div[contains(text(), ',') and (contains(text(), 'M') or contains(text(), 'F'))]")

                if not name_elements:
                    continue

                patient_name = name_elements[0].text.strip()

                # Skip if this is the "Random Case" card or other non-patient entries
                if "Random Case" in patient_name or "Try your luck" in patient_name:
                    continue

                # Extract bottom strip color from the card's style attribute
                style_attr = card.get_attribute("style")

                # Extract RGB color from style attribute
                color_match = re.search(
                    r'border-bottom-color:\s*rgb\(([^)]+)\)', style_attr)
                if color_match:
                    rgb_values = color_match.group(1)
                    color_description = get_color_description(rgb_values)

                    patients_data.append({
                        'name': patient_name,
                        'color_rgb': f"rgb({rgb_values})",
                        'color_description': color_description
                    })

            except Exception as e:
                print(f"Error processing card: {e}")
                continue

        return patients_data

    finally:
        driver.quit()


def get_color_description(rgb_values):
    """Convert RGB values to color description"""
    # Parse RGB values
    r, g, b = map(int, rgb_values.split(', '))

    # Define color mappings based on the observed colors
    if r == 187 and g == 217 and b == 249:
        return "Light Blue"
    elif r == 251 and g == 167 and b == 153:
        return "Light Red/Orange"
    else:
        return f"Custom Color (R:{r}, G:{g}, B:{b})"


def extract_from_html_file(file_path="test.html"):
    """Extract patient names and colors from a static HTML file"""

    if not os.path.exists(file_path):
        print(f"HTML file {file_path} not found.")
        return []

    with open(file_path, 'r', encoding='utf-8') as file:
        html_content = file.read()

    soup = BeautifulSoup(html_content, 'html.parser')

    # Find the Study Gap section
    study_gap_text = soup.find(text=re.compile(r'Study Gap'))
    if not study_gap_text:
        print("Study Gap section not found in HTML file.")
        return []

    # Find the parent container of the Study Gap section
    study_gap_container = study_gap_text.find_parent()
    while study_gap_container and 'swiper' not in str(study_gap_container.get('class', [])):
        study_gap_container = study_gap_container.find_next_sibling()

    if not study_gap_container:
        print("Could not find Study Gap container.")
        return []

    patients_data = []

    # Find all elements with border-bottom-color style in the Study Gap section
    cards_with_colors = study_gap_container.find_all(
        'div', style=re.compile(r'border-bottom-color.*rgb\([^)]+\)'))

    for card in cards_with_colors:
        try:
            # Extract color from style
            style = card.get('style', '')
            color_match = re.search(
                r'border-bottom-color:\s*rgb\(([^)]+)\)', style)

            if not color_match:
                continue

            rgb_values = color_match.group(1)
            color_description = get_color_description(rgb_values)

            # Find patient name in the card - look for pattern "Name, AgeGender"
            patient_name = None

            # Search for text patterns that match patient names
            text_elements = card.find_all(
                text=re.compile(r'^[A-Za-z]+,\s*\d+[MF]$'))

            if text_elements:
                patient_name = text_elements[0].strip()

            if patient_name and "Random Case" not in patient_name:
                patients_data.append({
                    'name': patient_name,
                    'color_rgb': f"rgb({rgb_values})",
                    'color_description': color_description
                })

        except Exception as e:
            print(f"Error processing card: {e}")
            continue

    return patients_data


def main():
    """Main function to extract and display Study Gap patient data"""
    print("Study Gap Patient Extractor")
    print("=" * 50)

    # Check if test.html exists for offline testing
    if os.path.exists("test.html"):
        print("Found test.html file. Extracting from static HTML...")
        patients = extract_from_html_file("test.html")
    else:
        print("No test.html found. Extracting from live website...")
        try:
            patients = extract_study_gap_patients()
        except Exception as e:
            print(f"Error extracting from website: {e}")
            return

    if patients:
        print(f"\nFound {len(patients)} patients in Study Gap:")
        print()
        print(f"{'#':<3} {'Patient Name':<20} {'Color':<15} {'RGB Value'}")
        print("-" * 60)

        for i, patient in enumerate(patients, 1):
            print(
                f"{i:2d}. {patient['name']:<20} {patient['color_description']:<15} {patient['color_rgb']}")
    else:
        print("No patients found in Study Gap section.")


if __name__ == "__main__":
    main()
