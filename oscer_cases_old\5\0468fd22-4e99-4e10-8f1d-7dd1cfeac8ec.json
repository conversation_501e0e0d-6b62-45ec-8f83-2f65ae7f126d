{"metadata": {"name": "Qing, 60F", "scraped_at": "2025-09-03T00:31:48.489651", "script_url": "https://www.oscer.ai/pwf/script/BUs8IaRAwck9"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 60 year old female who has presented to the emergency department with shortness of breath. Take a detailed history from <PERSON> to establish a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 60 year old woman and I‘m now retired.", "I’ve come to the doctor today because I have been experiencing shortness of breath."], "Persona": ["I am a friendly person but English is my second language so I speak in simple sentences.", "I am concerned because I’ve been getting so tired too, I have to rely on my children and neighbours to help with the chores.", "I’m hoping the doctor will fix me up so I can stop feeling like this."], "HOPC": ["I have been experiencing breathlessness for the past 6 months.", "My breathlessness has been gradually getting more severe.", "I feel short of breath at least 4 to 5 times per day.", "The episodes of breathlessness usually last 1 to 2 minutes.", "The breathlessness is no so bad that I struggle to walk to the bathroom.", "The breathlessness gets worse whenever I am exerting myself like cleaning the house or walking to the supermarket.", "Lying down also makes the breathlessness worse.", "Resting and sitting up seem to improve my shortness of breath.", "I can barely walk 50m anymore, I used to be able to walk 1 kilometre to go and do my grocery shopping.", "At nights when I lie down, sometimes I suddenly gasp for air and need to sit up.", "I tend to cough at night as well, it doesn’t happen a lot but it is getting worse.", "My shoes don’t seem to fit in any more.", "I noticed the swelling in my ankles a couple of days ago.", "Both of my ankles are swollen.", "The swelling has been persistent since I noticed it.", "I have never had swelling like this before.", "My daughters said the other day that my ankles look bigger than they used to.", "I’m feeling tired and I can’t get enough sleep each night.", "I have no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats."], "PMHx": ["I have type 2 diabetes mellitus.", "I used to be on 500mg of Metformin to control my diabetes but I stopped a couple of months ago, because I was sick of taking so many tablets. I am currently managing my condition with diet. I would say my diabetes is well controlled.", "I have high blood pressure.", "I have had high blood pressure for 5 years.", "I have never been diagnosed with tuberculosis.", "I take 10mg of lisinopril and 25mg of hydrochlorothiazide for my hypertension.", "I usually take vitamin D and calcium supplements.", "I had a mild heart attack when I was 57 years old.", "I don’t have any allergies that I’m aware of.", "All my vaccinations are up to date."], "FMHx": ["I have had a family history of heart problems. My father and uncles all suffered from heart diseases."], "SHx": ["I am originally from mainland China.", "I used to work as a chemistry teacher but I am retired now.", "I live alone, my husband died a few years ago from a heart attack.", "I have 5 children.", "None of my children have any medical conditions.", "I am well supported, all my children come to visit me.", "When I was younger, I was a heavy drinker as I used to drink 2 bottles of wine a day but I stopped drinking in my early 30s.", "I have never smoked before.", "I usually try to have a healthy diet composed of a mix of meat, vegetables, and fruit, but getting to the supermarkets is such a challenge at the moment.", "My exercise is that I usually walk to the shops which are about a kilometre away from me.", "I'm a bit overweight, I have tried reducing my weight."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Positional", "Context at Onset", "Physical Activity", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1, "items": ["Legs", "<PERSON><PERSON>", "Asking Generally"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal Congestion"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Antibiotics", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}