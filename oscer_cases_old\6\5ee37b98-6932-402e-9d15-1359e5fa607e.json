{"metadata": {"name": "<PERSON>, 29F", "scraped_at": "2025-09-03T00:42:07.008935", "script_url": "https://www.oscer.ai/pwf/script/H6Hc3KHvKnTy"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based at a GP practice. <PERSON> is a 29 year old woman (she/her), who is presenting with a cough. Please take a history from her with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m a 29 year old female.", "My preferred pronouns are she/her.", "I work as a librarian in the city.", "I have come in today as I have a cough that will just not go away."], "Persona": ["I am not really too worried about the cough. There is no history of cancer or anything in the family but I am finding it very annoying!", "I am of Indian ethnicity."], "HOPC": ["I've had this cough for almost 3 months now. It is constant.", "The cough has gotten much worse over the last 2 weeks.", "My cough started off dry, but now it has mucous in it. I have had this for a couple of weeks.", "Nothing really seems to make the cough better or worse.", "I’m bringing up about a teaspoon of phlegm when I do cough stuff up.", "I've noticed some red streaks in the phlegm recently.", "I have felt a little hot from time to time over the last few months and felt a little feverish at times. I did check my tempurature a few times and the highest I recorded was 38 degrees. I'm not sure if that counts as a fever.", "I don't have scales at home but my pants are feeling a bit loose. I feel like I have lost a couple of kilos in the last month.", "I have had a few episodes of night sweats, although I can't remember how long ago they started.", "I've also been feeling more tired than usual over the last few months.", "I have not noticed any shortness of breath.", "I haven’t had a wheeze.", "I don’t have any chest pain.", "I haven’t had a runny nose or been sneezing."], "PMHx": ["I have not been diagnosed with any medical conditions.", "I have never had surgery or been hospitalised for anything.", "I do not take any prescription, over the counter or herbal medicines.", "I don’t have any allergies.", "I got all the usual Australian vaccines as a kid, I don’t think I have had any since."], "FMHx": ["There are no significant medical conditions in my family.", "My father has depression but sees a psychiatrist and is going well these days."], "SHx": ["I travelled to India 3 months ago to visit extended family.", "I've been smoking a pack of cigarettes for the last 12 years.", "I also drink alcohol, I probably drink 2 glasses of wine most nights with dinner.", "I smoked a little bit of weed in the past but havent touched it for a while now.", "I ride a road bike most mornings before work and try to do 20 or so kilometers with some longer rides on the weekends.", "My diet isn't that great. I often get dinner from the Italian place near work and eat out for lunch."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Character", "score": 1, "items": ["Coughing Fit", "Asking Generally", "Productive Cough"]}, {"title": "Sputum Amount", "score": 1, "items": ["Asking Generally", "Cups or Teaspoons", "Frequency of Sputum"]}, {"title": "Sputum Colour", "score": 1, "items": ["Red or Pink", "Green, Yellow or Brown"]}, {"title": "Sputum Character", "score": 3, "items": ["Blood", "Frothy", "<PERSON><PERSON><PERSON>", "Asking Generally", "Wet, Thick or Sticky"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Work", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Smoking", "Allergens", "Positional", "Being at Work", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Morning or Night-time", "Season or Temperature", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Dust", "Animals", "Hayfever", "Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>", "Tuberculosis Vaccination", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["ICU admission", "Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Infectious Disease History", "score": 1, "items": ["HIV"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Influenza", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["COPD", "Asthma", "<PERSON><PERSON><PERSON>", "Hayfever", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Previous Occupation"]}, {"title": "Occupational Exposure", "score": 1, "items": ["Dust", "Animals", "Asbestos", "Stone Dust", "General <PERSON><PERSON>s", "Chemicals or Toxins"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}