{"metadata": {"name": "Ben, 50M", "scraped_at": "2025-09-03T00:32:59.081078", "script_url": "https://www.oscer.ai/pwf/script/FGYEPYNwNYmj"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the emergency department. Your next patient <PERSON>, is a 50 year old male who is presenting with shortness of breath. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>", "I'm a 50 year old man and I work as an aerospace engineer. My pronouns are he/him.", "I've come to the emergency department today because I have been getting a bit short of breath lately and I really wanted to have it checked out."], "Persona": ["I'm a fairly intelligent but laid back person.", "I think that I may have caught some sort of virus from one of my work colleagues.", "I'm hoping that the doctor will be able to tell me what is going on and provide treatment if neccessary."], "HOPC": ["I have some breathlessness that has come on in the last 7 days. It has worsened slightly over the last week and hasn't gone away at all.", "I have had a productive cough for about a week as well. I am bringing up about a teaspoon of green sputum when I cough. I have not coughed up any blood.", "In the last few days I have developed some chest pain on the right side of my chest. I would describe it as a sharp pain and rate it as a 7/10.", "Coughing and deep breathing makes the chest pain worse.", "I have noticed I have been feeling feverish for the last week as well. I haven't taken my temperature but I have felt like I was running hot.", "I have not had any recent overseas travel.", "I have not taken any medication for any of my symptoms.", "I have never had anything like this happen to me."], "PMHx": ["I have Hypertension that was diagnosed 5 years ago. This is currently managed with Perindopril once daily.", "I had a Vasectomy a few years ago to prevent having any more children.", "I don't have a history of respiratory disease or previous lung infections.", "I don't take any over the counter medications or supplements.", "I don't have any allergies.", "My vaccinations are all up to date."], "FMHx": ["I don't have any medical conditions that run in my family.", "My mother and father are both alive and well in their mid 70s."], "SHx": ["I work as an aerospace engineer for the Defence Forces.", "I don’t spend much time on construction sites, but when I am on a site I always use the appropriate PPE.", "I have never worked with asbestos or any dangerous chemicals or fumes.", "I’ve been happily married to my wife <PERSON> for almost 20 years and live at home with my wife and our two young children aged 5 and 7 years old.", "I have never smoked cigarettes.", "I only drink alcohol at social events and even then I usually have only 2 drinks. On average this would occur about once per month.", "I have never used recreational or illicit drugs.", "I have a cat and dog at home.", "I am vegetarian and eat a well balanced diet.", "I exercise lightly several times per week, usually finding the time early in the morning or late in the evening to go for a walk around the block.", "I am not obese or overweight.", "I have not travelled overseas recently."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Allergies", "Positional", "Context at Onset", "Physical Activity", "Season or Temperature", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Swelling", "score": 1}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Malaise", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Tuberculosis Vaccination", "Current Vaccination Status"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Heart Disease", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally", "Contact with Covid-19 Cases"]}, {"title": "Infection Prevention", "score": 1, "items": ["Hand Hygiene", "Social Distancing", "Personal Protective Equipment Use"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}