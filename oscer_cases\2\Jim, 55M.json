{"metadata": {"name": "<PERSON>, 55M", "scraped_at": "2025-09-03T00:07:45.473821", "script_url": "https://www.oscer.ai/pwf/script/toToZ2Z0BAQ1"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 55 year old male presenting to the emergency department complaining of abdominal pain. Take a history from <PERSON> with the aim of developing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m 55 years old German living in Australia. My pronouns are he/him.", "I have come in with some abdominal pain."], "Persona": ["I’m a jovial person.", "I love my wife and my beer."], "HOPC": ["I’ve got abdominal pain, which is gradually getting worse and started 24 hours ago.", "The abdominal pain is a cramping pain that is located roughly in the centre of my stomach.", "I have not been able to poop for more than 3 days now.", "I’ve been vomiting pretty thin and watery vomit, which initially had food in it.", "My vomit at this point in time has a greenish yellow colour to it.", "I have been slightly bloated.", "I have not eaten anything off or suspicious.", "I have not had a fever.", "It does not hurt to hop.", "The pain did not get worse when we drove over bumps on the way here.", "The pain does not seem to get worse when I press on my stomach."], "PMHx": ["I take no medication.", "I had my gall bladder removed about 1 year ago. I also had my appendix out when I was 17.", "I have no allergies.", "All my vaccinations are up to date."], "FMHx": ["My father died of bowel cancer.", "My mother is still alive and seams pretty healthy.", "I have two older brothers who live overseas. They are both healthy as far as I am aware.", "I do not have any children."], "SHx": ["I drink a couple of beers a night.", "I have never smoked.", "I have never done recreational drugs.", "I work as an engineer who works in the design of medical devices. I work full-time and have been doing this for over 10 years.", "The last time I travelled was to Spain a year ago.", "I live with my wife. We do not have any children.", "I have a well balanced diet. I get plenty of fruit and vegetables and fibre and all that.", "For exercise, I go for a couple of km walk most days."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Cramping", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally", "Location at Onset"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Chest", "Flank", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Eating or Diet", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Injuries", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing"]}]}]}], "Assoc.": [{"title": "Altered Bowel Habits", "score": 2, "items": ["Constipation", "Asking Generally", "Defecating Blood", "Black and Tarry Stools", "Most Recent Bowel Movement"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Bowel Cancer", "Asking Generally", "Bowel Obstruction"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["NSAID", "Ibuprofen", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally", "Abdominal Surgery"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally", "Abdominal Surgery"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Cancer", "Bowel Cancer", "Asking Generally", "Gastrointestinal Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Expired Foods", "Asking Generally", "Most Recent Meal", "Contaminated Food or Water"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}