{"metadata": {"name": "Farak, 56M", "scraped_at": "2025-09-03T00:36:09.903233", "script_url": "https://www.oscer.ai/pwf/script/iwFEPkQBQO7T"}, "tabs": {"doctor_information": {"Intro": "<PERSON><PERSON> is a 56 year old male presenting to your general practice clinic complaining of leg pain. Please take a history from <PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON> and I am a 56 year old male (he/him).", "I am an immigrant from Libia and I work as a bank teller.", "I am presenting today to the GP because of some pain in my calf."], "Persona": ["I am a fairly relaxed guy, but I am worried about my leg.", "I trust doctors, but I don’t always act on the advice from my doctor."], "HOPC": ["I have been getting pain in my right calf.", "I’m not getting pain anywhere else.", "The pain is intermittent and I only experience the pain when I am walking around or exerting myself.", "I first noticed the pain about 18 months ago.", "The pain has become more frequent and more severe, the pain is about a 7 out of 10 now.", "I can't even walk to the end of the street anymore without having to sit down because of the pain.", "The pain comes on gradually.", "I can’t think of anything specifically that would have caused this. I have had no trauma to my leg.", "The pain doesn’t radiate anywhere else.", "The pain is not worse at night and resting relieves the pain.", "I have noticed some hair loss on my right leg, the skin also looks a bit paler. I have not noticed any redness.", "I don’t have any wounds or ulcers.", "I’ve been getting a cold right foot, even when I wear a sock.", "My calf is not swollen.", "I have not noticed a bulge or lump on my knee.", "I do not have any joint pain.", "I have not had a fever.", "I have not noticed any leg weakness, muscle wasting or fasciculations.", "I have not had any recent hospital stays, long flights or periods of immobilisation. I have also not had any recent surgeries."], "PMHx": ["I have been diagnosed with high blood pressure.", "I take a medication for my blood pressure but I can’t remember the name of it.", "My doctor tells me I have high cholesterol.", "I was prescribed a medication for my cholesterol about 6 months ago, but I haven’t started taking it yet.", "I don’t take any other medications, except for the occasional paracetamol or ibuprofen.", "I don’t take any herbal medications or supplements.", "I don’t have any allergies and my vaccinations are up to date.", "I’ve never had any surgeries or been admitted to hospital.", "I am overweight.", "I have never had any back injuries."], "FMHx": ["My father died of a heart attack in his 60s.", "My brother has had two heart attacks and a stroke.", "My mother is alive and well."], "SHx": ["I do not have a healthy diet, I mostly eat fast food, including burgers and chips.", "I don’t do any exercise because my job takes up most of my time.", "I work as a bank teller so my job involves sitting down for extended periods of time.", "I work 6 days a week to provide for my family.", "I am happily married with children.", "I haven’t travelled recently, I went to Bali with my family a few years ago.", "I don’t drink any alcohol and I never have, it’s not for me.", "I smoke cigarettes, about one and a half packets per day.", "I have been smoking the same amount for approximately 40 years.", "I don’t have any desire to quit.", "I have never used recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Leg <PERSON>", "score": 13, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON><PERSON>", "<PERSON>", "Burning", "Throbbing", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 2, "items": ["Lower Leg", "Upper Leg", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Worst Ever", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Palpating", "Immobility", "Night-time", "Positional", "Context at Onset", "Lifestyle Changes", "Physical Activity"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["Feet", "Legs", "<PERSON><PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Blue", "Purple", "Asking Generally"]}, {"title": "Hair Changes", "score": 1, "items": ["Hair Loss"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Skin Changes", "score": 1, "items": ["<PERSON><PERSON>", "Sc<PERSON>", "<PERSON>y", "Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Urinary Incontinence", "Hesitancy, Intermittency or Dribbling"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Clotting Disorders", "Deep Vein Thrombosis"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Atherosclerosis", "Asking Generally", "Venous Insufficiency"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally", "Clotting Disorders", "Deep Vein Thrombosis"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}