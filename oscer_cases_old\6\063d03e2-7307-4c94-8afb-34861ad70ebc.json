{"metadata": {"name": "Anastasia, 65F", "scraped_at": "2025-09-03T00:37:21.339234", "script_url": "https://www.oscer.ai/pwf/script/HolPq7tRT5CQ"}, "tabs": {"doctor_information": {"Intro": "You are a student in general practice, and you have been asked to see <PERSON>. <PERSON> is a 65 year old female, presenting with chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>", "I am a 65 year old woman (she/her).", "I work as a nurse.", "I’ve come in to see the doctor today because I’ve been getting some chest pain and it is starting to worry me."], "Persona": ["I am normally a very serious person.", "I don’t take particularly good care of my health, but I wouldn’t say I am super unhealthy either.", "As I have significant experience in the medical field I only expect the best in terms of care.", "I am rather concerned that the chest pain is something bad."], "HOPC": ["I have been experiencing this chest pain for about 6 months. It seems to be getting worse.", "The chest pain is central. It radiates to my left arm and jaw when it comes on.", "The chest pain comes on fairly suddenly.", "I only notice the chest pain when I am exerting myself, such as walking up a hill. Currently, even walking along the flat is causing the chest pain.", "Only exertion seems to exacerbate the chest pain.", "The chest pain goes away when I rest. Nothing else seems to help with the chest pain.", "I can’t think of anything that would have caused this chest pain. Nothing has changed in my life that would have caused this.", "I have also been experiencing some shortness of breath. I notice the shortness of breath when I am exerting myself.", "The shortness of breath happens at the same time as the chest pain. It comes on suddenly.", "The shortness of breath started at the same time as the chest pain, and has also been getting worse.", "Exertion makes the shortness of breath worse.", "Resting makes the shortness of breath go away.", "I have also been noticing some dizziness. Sometimes it feels like I am about to pass out.", "The dizziness comes on when I exert myself. It happens at the same time as the chest pain and shortness of breath."], "PMHx": ["I’m usually pretty healthy, I have no underlying medical conditions.", "I don’t have any allergies that I’m aware of.", "I don’t take any regular medications."], "FMHx": ["My father had aortic stenosis and required open-heart surgery to fix it.", "There are no other medical conditions that run in my family."], "SHx": ["I work as a nurse. My work is always busy and I occasionally have to work through the night.", "I live with my partner, <PERSON>. We have been together for 3 years now.", "I don’t have any pets.", "I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking as it takes away the stress of my job.", "I occasionally drink a glass of wine socially.", "I eat a relatively balanced diet.", "I don’t do much exercise. I feel too tired after work to do exercise. The only exercise I do is I play a round of golf with my partner once or twice a month.", "I wouldn’t say that I’m overweight."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 13, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Context at Onset", "Physical Activity", "Positional or Movements", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaw Pain", "score": 1, "items": ["Jaw Pain"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Dust", "<PERSON><PERSON>", "Animals", "Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Respiratory Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Asking Generally", "Vascular Disease", "Valvular Heart Disease", "Coronary Artery Disease", "Hypertrophic Cardiomyopathy"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Medication Side Effects", "score": 1, "items": ["Medication Side Effects"]}, {"title": "Prescription Medications", "score": 1, "items": ["Statins", "Inhalers", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asthma", "Anxiety", "Asking Generally", "Respiratory Disease", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}