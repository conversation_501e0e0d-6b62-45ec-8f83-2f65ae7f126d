{"metadata": {"name": "<PERSON><PERSON>, 17F", "scraped_at": "2025-09-05T12:21:09.033202", "script_url": "https://www.oscer.ai/pwf/script/2uOuYBVj575X"}, "tabs": {"doctor_information": {"Intro": "You are a junior doctor on your Emergency Department rotation. <PERSON><PERSON> is a 17-year-old woman presenting to the ED with noisy breathing. Please take a history from <PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON>, I am 17 years old, and my pronouns are she/her.", "I have come to the ED because I am worried about this weird noise that I am making when I breathe."], "Persona": ["I am usually a very fit and healthy 17-year-old. I don’t need to see the doctor too often.", "I am worried about the noise I am making when I breathe.", "I am an outgoing 17-year-old high school student.", "I am a very easy going person."], "HOPC": ["I have noticed that I’m making an odd noise when I breathe.", "It started early this morning when I went for my morning run.", "It is a high-pitched noise.", "Initially I only heard it when I was breathing out but I can hear it when I breathe in now too.", "I am also feeling quite short of breath.", "The shortness of breath came on at the same time as the weird breathing noise.", "The shortness of breath is getting worse.", "I just feel like I can’t get enough air in.", "I feel tight across my whole chest too.", "I have had a cough come on at the same time.", "It feels like a dry cough, but I am coughing up small amounts of white sputum.", "I have not coughed up any blood.", "Coming inside to the warm house made it a little bit better.", "It got worse again when I was walking to the car to come into the hospital.", "I have not been sick recently.", "No one I know has been sick recently.", "I do not have a runny nose or a sore throat.", "I have had one or two episodes like this in the past. It has always been on a cold morning when I have been out for a run, it has never been this bad before though.", "I have not had a fever.", "I do not have any chest pain, only chest tightness.", "I have not had any pain in my legs.", "I do not feel dizzy or confused.", "I have not eaten anything new.", "My lips are not swollen, I do not feel a tingling sensation in my throat or on my face.", "I can still speak in complete sentences.", "My heart does not feel like it is racing.", "I have come in because it is severe enough that I feel I cannot go about my day today."], "PMHx": ["I used to get eczema as a child, and I get hay fever occasionally, but I have no other medical conditions.", "I don’t take any medications or anything over the counter or supplements.", "I have never had an operation.", "I get hay fever, but I don’t have any other allergies.", "I am up to date with all of my vaccinations."], "FMHx": ["My sister had bad asthma when she was a bit younger, I think my mum had asthma too.", "Aside from that the rest of my family are well and I am not aware of any other medical conditions."], "SHx": ["I am in high school at the moment completing my year 12 studies.", "My favourite classes are history and geography.", "I work part-time at my dad’s photography studio.", "Nothing in my life stresses me out at the moment.", "I do a lot of exercise; I am a runner and tend to run for at least 40 minutes a day.", "I am quite health conscious, my diet is very good.", "I weigh 64 kilograms and I am 177 centimetres tall, my BMI is around 20.", "I live at home with my mum, dad, and older sister.", "We also have a dog called <PERSON>.", "I don't smoke cigarettes, I have never smoked.", "No one in the house smokes cigarettes.", "I only drink alcohol very occasionally, I have had a few 18th’s already this year.", "I have never tried recreational drugs, I have never injected anything.", "I feel very safe at home.", "I would say my mental health is good."]}, "marking_rubric": {"HOPC": [{"title": "Noisy Breathing", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Pitch", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Speak", "Asking Generally", "Ability to Exercise", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Time Course", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Inhalers", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Smoking", "Exercise", "Allergies", "Environment", "Cold Weather", "Recent Illness", "Asking Generally", "Context at Onset"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>", "Clear Sputum"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Swelling", "score": 1, "items": ["Throat"]}, {"title": "Chest Pain", "score": 1, "items": ["Tightness", "Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal <PERSON>harge", "Post-<PERSON><PERSON>", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Dust", "<PERSON><PERSON>", "Animals"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Anaphylaxis History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Past Medical History", "score": 1, "items": ["ICU admission", "Recent Illness"]}, {"title": "Mental Health History", "score": 1, "items": ["Asking Generally", "Depression or Low Mood"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Pneumonia", "Asking Generally"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Steroids", "Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asthma", "<PERSON><PERSON><PERSON>", "Hayfever", "Panic Attacks", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "<PERSON><PERSON><PERSON>"]}, {"title": "Conclusion", "score": 1, "items": ["Patient Questions"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}