{"metadata": {"name": "<PERSON>, 29M", "scraped_at": "2025-09-03T00:39:39.858704", "script_url": "https://www.oscer.ai/pwf/script/vjCQmVk8xqWM"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 29 year old male presenting to the general practice complaining of chest pain. Please take a history from <PERSON> with an aim to establish a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> (he/him).", "I’m a 29 year old man and I work as a software engineer in the city."], "Persona": ["I'm worried I could be having a heart attack.", "I’ve never experienced anything like this before."], "HOPC": ["I am currently having very bad chest pain.", "I first noticed the chest pain when I was reading in bed last night.", "I can't think of anything that would have caused it.", "The pain has been very constant, it's been about 12 hours or so now.", "The chest pain feels sharp, it feels like someone is stabbing me.", "The chest pain is in the centre of my chest.", "I am also getting some pain in my jaw and my shoulder.", "The chest pain is an 8 out of 10, it’s the worst pain I’ve ever had.", "I barely slept last night as the pain was so bad, I had to sleep with a few pillows to prop me up.", "Lying down makes the pain worse and sitting up and leaning forward makes the pain better.", "Taking a deep breath also makes the pain worse.", "The chest pain doesn’t seem to be related to exercise.", "I feel like I’ve been feverish the past day or two, it’s been coming and going.", "I haven't had any weight loss.", "I've been feeling a bit off the past day or so, I haven't had the best appetite but I'm still eating.", "I have not had any nausea or vomiting.", "I’ve been under the weather recently with a few aches and pains. I had a terrible sore throat the other day but that’s on the mend now.", "I don’t have any other symptoms.", "I have not injured myself recently or had any chest trauma.", "I don't have a cough.", "I do not have any palpitations.", "I have not experienced any night sweats."], "PMHx": ["I don't have any medical conditions, I'm usually healthy.", "I've never had to go to hospital and I’ve never had any surgery.", "I don’t take any prescription medications.", "I occasionally use a paracetamol or ibuprofen if I have any niggles.", "I don’t have any allergies that I am aware of.", "I am up to date on all of my vaccinations."], "FMHx": ["My Mother has an underactive thyroid, but my Father doesn’t have any medical problems.", "I don’t know of anyone in the family who had any heart problems.", "The rest of my family are very healthy."], "SHx": ["I’ve never smoked before, my family would kill me if I did.", "Mid week I occasionally have a few glasses of wine or beer. Every Friday night I head out with the office and can have 7 or 8 drinks, sometimes on Saturday too.", "I've experimented with a few recreational drugs.", "I used marijuana a few times in my University days.", "If there’s an event in the city sometimes I use ketamine with my friends.", "I've never injected drugs.", "My parents are both Chinese, but I was born in Australia.", "We travel to China often to visit family, my last trip was about 4 months ago.", "My software company has an office in the city.", "A few people at the firm have been sick with cold like symptoms, there must be something going around.", "Work is pretty busy, so I do a lot of meal prep and try to eat quite well.", "I normally go to the gym on my lunch break, but I couldn’t do that with this chest pain."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Burning", "Ripping", "Cramping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Resting", "Injuries", "Palpating", "Eating or Diet", "Context at Onset", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 2, "items": ["Hypertension", "<PERSON><PERSON><PERSON><PERSON>", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}