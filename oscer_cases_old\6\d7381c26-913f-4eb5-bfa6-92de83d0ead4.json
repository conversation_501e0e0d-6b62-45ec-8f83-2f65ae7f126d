{"metadata": {"name": "<PERSON>, 28M", "scraped_at": "2025-09-03T00:40:17.774625", "script_url": "https://www.oscer.ai/pwf/script/OXxcNzwXLAfv"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the emergency department. <PERSON>, a 28 year old male, presents complaining of chest pain. Please take a focused history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 28 year old male (he/him), and I work as a court reporter.", "I’ve come to the hospital today because I have chest pain."], "Persona": ["I’m a pretty relaxed and happy person.", "I normally like to have a joke, but I am really distressed by this pain.", "I am worried it is something serious."], "HOPC": ["I am experiencing chest pain.", "The chest pain is in the middle of my chest, behind my sternum. The chest pain radiates to my back.", "The pain is at least a 9 out of 10. It is stopping me from being able to move around.", "The chest pain came on suddenly about 1 hour ago. I was at work when it started. I wasn’t doing anything out of the ordinary at the time.", "The pain feels like tearing.", "The chest pain hasn’t gotten any worse or any better since it started.", "I don’t have any nausea, vomiting, palpitations or shortness of breath.", "Nothing seems to make the pain any better or worse.", "I’ve never had chest pain like this before."], "PMHx": ["I’m usually pretty healthy, I don’t have any allergies that I’m aware of. I don’t take any regular medications.", "My vaccinations are up to date.", "I have Marfan’s Syndrome.", "I have a cardiologist and a vascular surgeon that I see because of my <PERSON><PERSON> syndrome, but I have never needed any surgery or medications."], "FMHx": ["My father had diabetes and hypertension. He died in his 50s.", "I have no other medical conditions that run in the family."], "SHx": ["I work as a court reporter. I enjoy going to work and I don't find my job to be stressful.", "I am happy at my job.", "I like to exercise. I go for a run 5 times a week.", "I eat a healthy, balanced diet. I eat a lot of vegetables and white meat.", "I have a girlfriend. We live together. I am well supported by my girlfriend.", "I haven’t travelled recently.", "I drink alcohol socially. I usually only drink on the weekends, and I never drink more than 4 beers. I have been drinking since I turned 18.", "I will sometimes have a cigarette. It is only in very occasional social situations, and I never have more than 1.", "I have never used recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Burning", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Resting", "Palpating", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Marfan Syndrome", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Heart Surgery", "Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Heart Surgery", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Aortic Dissection", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}