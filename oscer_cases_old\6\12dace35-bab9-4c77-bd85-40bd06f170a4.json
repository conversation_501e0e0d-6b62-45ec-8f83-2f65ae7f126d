{"metadata": {"name": "<PERSON>, 70M", "scraped_at": "2025-09-03T00:36:47.638627", "script_url": "https://www.oscer.ai/pwf/script/GLp45RxTWMhA"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 70 year old male presenting to his <PERSON> with palpitations. Take a focused history from <PERSON>, with an aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I’m <PERSON>, a 70 year old gardener. My pronouns are he/him.", "I’ve come to visit the doctor because I’ve been getting episodes where my heart is racing."], "Persona": ["I’m quite relaxed and laid back.", "I don’t think that my heart racing is a major problem."], "HOPC": ["I’ve been experiencing episodes of strong and rapid heart beatings for the past two weeks.", "The palpitations seem irregular. The episodes seem to come out of nowhere and start suddenly.", "The episodes fluctuate in length but they generally vary between 15 and 20 minutes.", "Rest doesn’t alleviate the palpitations.", "I feel fatigue after getting the palpitations. Only time can relieves these symptoms.", "I do not get any chest, jaw or arm pain.", "I haven’t lost consciousness.", "I don't get any shortness of breath with these episodes.", "I have not had any nausea or vomiting with these episodes.", "I have no pain or fever.", "I have not had a cough recently.", "My weight hasn’t changed and my tolerance to heat has remained the same.", "I don’t feel overly stressed.", "I do not have any lumps or swelling around my neck.", "I have not had any problems with my sleep recently."], "PMHx": ["I’ve was diagnosed with type 2 diabetes about 30 years ago.", "I also have high blood pressure and cholesterol.", "I've been on Lisinopril, Sitagliptin and Metformin for the past 30 years. Sometimes I take Panadol for mild headaches.", "The doctor wants to put me on something for my cholesterol but I have just been trying to eat healthier. I feel like I am on enough drugs.", "I have no allergies.", "I have never been diagnosed with any mental health conditions. I have never had anxiety.", "I do not think anyone in my family has had any arrhythmias."], "FMHx": ["My parents died of heart disease, but they were in their 70's and they've had heart problems for a long time.", "I do not have any other siblings.", "I do not know of any diseases that run in the family.", "I do not have any family history of thyroid problems."], "SHx": ["I am a gardener. I manage and run a few city gardens which can be stressful at times but I have always loved it.", "I always make sure I wear full PPE when we are spraying chemicals on the gardens.", "I was previously married but my wife passed away in a car accident about 10 years ago.", "I live alone at the moment.", "I have about 3 cans of beer a night and have done so since I was a teenager.", "I've been smoking a pack a day for almost 50 years now.", "I do not do any specific exercise. The work on the gardens is very physical though and I have always thought that would be enough.", "I do not use any illicit drugs and have never used IV drugs.", "I am not currently sexually active."]}, "marking_rubric": {"HOPC": [{"title": "Palpitations", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 2, "items": ["Rate", "Fluttering", "Regularity", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Rate of Heartbeats"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Episodic", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 2, "items": ["Asking Generally", "Most Recent Episode"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "<PERSON><PERSON><PERSON>", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Stress", "Resting", "Caffeine", "Exertion", "Anxiousness", "Medications", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Bulging Eyes", "score": 1, "items": ["Bulging Eyes", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Thyroid Disease History", "score": 1, "items": ["Hyperthyroidism", "Thyroid Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Duration", "Asking Generally", "Past Medications", "Antihypertensives"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Arrhythmia", "Heart Disease", "Grave's Disease", "Hyperthyroidism", "Thyroid Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": []}}}