<body><noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WDV8FQD" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript><noscript><PERSON><PERSON><PERSON> uses Javascript to work! Please
        enable Javascript in your browser to use <PERSON>scer.</noscript><noscript><img height="1" width="1"
            style="display:none"
            src="https://www.facebook.com/tr?id=682691255636893&ev=PageView&noscript=1" /></noscript><noscript><iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-WDV8FQD" height="0" width="0"
            style="display:none;visibility:hidden"></iframe></noscript>
    <script>window.fbAsyncInit = function () { FB.init({ appId: "2155349584599222", autoLogAppEvents: !0, xfbml: !0, version: "v10.0" }) }</script>
    <script async="" defer="defer" crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js"></script>
    <div id="app">
        <div class="application">
            <div class="jss134" style="">
                <div class="dashboard"></div>
                <div class="jss247">
                    <div class="MuiContainer-root jss248 MuiContainer-disableGutters MuiContainer-maxWidthLg"
                        data-tut="reacttour__tab__nav">
                        <div class="jss251"><img src="/static/media/oscer-icon-solid.66a4ace1.svg" alt="oscer_logo"
                                class="jss249"><a class="jss255 jss256" data-tut="reactour__tab__home"
                                href="/dashboard/home" aria-current="page"><img
                                    src="/static/media/home-tab-icon.0c74313e.svg" alt="HOME__image" class="jss257">
                                <div class="jss258">HOME</div>
                                <div class="jss259"> <span></span> </div>
                            </a><a class="jss255" href="/dashboard/learn"><img
                                    src="/static/media/skills-hub-icon.4afce7fb.svg" alt="LEARN__image" class="jss257">
                                <div class="jss258">LEARN</div>
                                <div class="jss259"> <span></span> </div>
                            </a><a class="jss255" data-tut="reactour__tab__scripts" href="/dashboard/scripts"><img
                                    src="/static/media/scripts-tab-icon.82229006.svg" alt="SCRIPTS__image"
                                    class="jss257">
                                <div class="jss258">SCRIPTS</div>
                                <div class="jss259"> <span></span> </div>
                            </a><a class="jss255" data-tut="reactour__tab__explorer" href="/dashboard/explorer"><img
                                    src="/static/media/condition-explorer-tab-icon.2c2f56e6.svg"
                                    alt="CONDITION EXPLORER__image" class="jss257">
                                <div class="jss258">CONDITION EXPLORER</div>
                                <div class="jss259"> <span>(BETA)</span> </div>
                            </a>
                            <div></div>
                        </div>
                        <div class="jss253"><a class="jss255" data-tut="reactour__tab__search" href="/search"><svg
                                    class="MuiSvgIcon-root jss262" focusable="false" viewBox="0 0 24 24"
                                    aria-hidden="true">
                                    <path
                                        d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                                    </path>
                                </svg></a>
                            <div class="jss303" aria-haspopup="true" aria-hidden="true"><img
                                    src="/static/media/streak-stat-icon.4617ca0b.svg" alt="oscer_logo" class=""><svg
                                    class="MuiSvgIcon-root jss299 MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeLarge"
                                    focusable="false" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M7 14l5-5 5 5z"></path>
                                </svg></div>
                            <div class="jss303" aria-haspopup="true" aria-hidden="true"><img
                                    src="/static/media/specialty-stat-icon.475b72c1.svg" alt="oscer_logo" class=""><svg
                                    class="MuiSvgIcon-root jss299 MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeLarge"
                                    focusable="false" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M7 14l5-5 5 5z"></path>
                                </svg></div>
                            <div class="jss303" aria-haspopup="true" aria-hidden="true"><img
                                    src="/static/media/ranking-stats-icon.5181ccb5.svg" alt="oscer_logo" class=""><svg
                                    class="MuiSvgIcon-root jss299 MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeLarge"
                                    focusable="false" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M7 14l5-5 5 5z"></path>
                                </svg></div>
                            <div class="jss303" aria-haspopup="true" aria-hidden="true">
                                <div class="MuiAvatar-root MuiAvatar-circular jss263 MuiAvatar-colorDefault"><span
                                        class="jss265">T</span><img
                                        src="/static/media/prime-crown-icon-gold.98f284ac.svg" alt="crown"
                                        class="jss264"></div><svg
                                    class="MuiSvgIcon-root jss299 jss266 MuiSvgIcon-colorPrimary MuiSvgIcon-fontSizeLarge"
                                    focusable="false" viewBox="0 0 24 24" aria-hidden="true">
                                    <path d="M7 14l5-5 5 5z"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="jss135" id="mobile-cases-dashboard-scroll-id">
                    <div class="MuiContainer-root jss647 MuiContainer-disableGutters MuiContainer-maxWidthLg">
                        <div class="jss639">
                            <div class="jss640">
                                <nav class="MuiTypography-root MuiBreadcrumbs-root MuiTypography-body1 MuiTypography-colorTextSecondary"
                                    aria-label="breadcrumb">
                                    <ol class="MuiBreadcrumbs-ol jss645">
                                        <li class="MuiBreadcrumbs-li jss644">
                                            <p class="MuiTypography-root MuiTypography-body1">HOME</p>
                                        </li>
                                    </ol>
                                </nav>
                            </div>
                            <div class="jss548">
                                <div class="jss549">
                                    <div class="jss575" style="max-width: 47px;">
                                        <div class="MuiInputBase-root jss576 MuiInputBase-adornedStart">
                                            <div class="MuiInputAdornment-root MuiInputAdornment-positionStart"><svg
                                                    class="MuiSvgIcon-root jss579" focusable="false" viewBox="0 0 24 24"
                                                    aria-hidden="true">
                                                    <path
                                                        d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z">
                                                    </path>
                                                </svg></div><input placeholder="Search cases, conditions" type="text"
                                                aria-label="search a Patient"
                                                class="MuiInputBase-input MuiInputBase-inputAdornedStart" value="">
                                        </div>
                                    </div>
                                    <div class="jss554">
                                        <div>
                                            <div class="MuiInputBase-root jss588 MuiInputBase-adornedEnd"
                                                style="width: 150px;" inputprops="[object Object]"><input
                                                    placeholder="System" type="text"
                                                    class="MuiInputBase-input MuiInputBase-inputAdornedEnd" value="">
                                                <div class="MuiInputAdornment-root MuiInputAdornment-positionEnd"><svg
                                                        class="MuiSvgIcon-root jss594" focusable="false"
                                                        viewBox="0 0 24 24" aria-hidden="true">
                                                        <path d="M7 10l5 5 5-5z"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="jss585"></div>
                                        </div>
                                    </div>
                                    <div class="jss554">
                                        <div>
                                            <div class="MuiInputBase-root jss588 MuiInputBase-adornedEnd"
                                                style="width: 150px;" inputprops="[object Object]"><input
                                                    placeholder="Rotation" type="text"
                                                    class="MuiInputBase-input MuiInputBase-inputAdornedEnd" value="">
                                                <div class="MuiInputAdornment-root MuiInputAdornment-positionEnd"><svg
                                                        class="MuiSvgIcon-root jss594" focusable="false"
                                                        viewBox="0 0 24 24" aria-hidden="true">
                                                        <path d="M7 10l5 5 5-5z"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="jss585"></div>
                                        </div>
                                    </div>
                                    <div class="jss554">
                                        <div>
                                            <div class="MuiInputBase-root jss588 MuiInputBase-adornedEnd"
                                                style="width: 150px;" inputprops="[object Object]"><input
                                                    placeholder="Presentation" type="text"
                                                    class="MuiInputBase-input MuiInputBase-inputAdornedEnd" value="">
                                                <div class="MuiInputAdornment-root MuiInputAdornment-positionEnd"><svg
                                                        class="MuiSvgIcon-root jss594" focusable="false"
                                                        viewBox="0 0 24 24" aria-hidden="true">
                                                        <path d="M7 10l5 5 5-5z"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="jss585"></div>
                                        </div>
                                    </div>
                                    <div class="jss554">
                                        <div>
                                            <div class="MuiInputBase-root jss588 MuiInputBase-adornedEnd"
                                                style="width: 150px;" inputprops="[object Object]"><input
                                                    placeholder="Level" type="text"
                                                    class="MuiInputBase-input MuiInputBase-inputAdornedEnd" value="">
                                                <div class="MuiInputAdornment-root MuiInputAdornment-positionEnd"><svg
                                                        class="MuiSvgIcon-root jss594" focusable="false"
                                                        viewBox="0 0 24 24" aria-hidden="true">
                                                        <path d="M7 10l5 5 5-5z"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="jss585"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-tut="reactour__freeCases" class="jss671">
                            <div class="jss648">
                                <div style="display: flex; align-items: center;">Jump Back In<span
                                        class="jss649">(0/1)</span>
                                    <div class="jss686"
                                        title="The cases you've most recently attempted are here. You can give them a go again at anytime!"
                                        style=""><span>i</span></div>
                                </div>
                            </div>
                            <div class="jss689">
                                <div class="swiper swiper-initialized swiper-horizontal swiper-pointer-events jss691">
                                    <div class="swiper-button-prev swiper-button-disabled swiper-button-lock"></div>
                                    <div class="swiper-button-next swiper-button-disabled swiper-button-lock"></div>
                                    <div class="swiper-wrapper"
                                        style="transform: translate3d(0px, 0px, 0px); transition-duration: 0ms;">
                                        <div class="swiper-slide jss695 swiper-slide-active"
                                            style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723 jss697">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(187, 217, 249);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-cough.b03dcf9e.svg"
                                                                    class="jss708" alt="Cough"></div>
                                                            <div class="jss736">Ali, 29M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Cough</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-3__2eRc7 jss727">
                                                                <div>LEVEL</div>
                                                                <div>3</div>
                                                            </div>
                                                            <div class="jss751"
                                                                style="background-color: rgb(187, 217, 249);">50<span
                                                                    class="jss753">%</span></div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(187, 217, 249);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Ali Azizi is 29 y.o male
                                                                        presenting with Cough.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                                <div class="jss667"><button
                                                                        class="MuiButtonBase-root MuiButton-root jss664 jss668 MuiButton-contained MuiButton-containedSecondary"
                                                                        tabindex="0" type="button"><span
                                                                            class="MuiButton-label"><img
                                                                                src="/static/media/retry-icon-white.85eb4d27.svg"
                                                                                alt="icon_retry"
                                                                                class="jss669"></span><span
                                                                            class="MuiTouchRipple-root"></span></button>
                                                                    <div class="jss670 jss672">
                                                                        <div class="jss673">DIAGNOSIS DONE</div>
                                                                        <div class="jss674">&gt; 86%</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695 swiper-slide-next" style="margin-right: 20px;">
                                            <div data-tut="reactour__randomCase">
                                                <div class="jss722 jss723 jss730">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(187, 217, 249);">
                                                        <div class="jss732 jss733">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-character-randomise-case.e3026ac0.svg"
                                                                    class="jss708" alt="random"></div>
                                                            <div class="jss736">Random Case</div><span
                                                                class="jss737">Try your luck</span>
                                                            <div class="jss738">Symptoms Unknown</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-random__3OHMC jss727">
                                                                <div>LEVEL</div>
                                                                <div>?</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740 jss741">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(187, 217, 249);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">About</div>
                                                                    <div class="jss746">Test your skill and knowledge
                                                                        with a random case!</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711">
                                                                <div class="jss667" style="flex: 1 1 0%;"><button
                                                                        class="MuiButtonBase-root MuiButton-root jss665 MuiButton-contained MuiButton-containedPrimary"
                                                                        tabindex="0" type="button"><span
                                                                            class="MuiButton-label">Start Case<svg
                                                                                stroke="currentColor"
                                                                                fill="currentColor" stroke-width="0"
                                                                                viewBox="0 0 512 512" class="jss666"
                                                                                height="1em" width="1em"
                                                                                xmlns="http://www.w3.org/2000/svg">
                                                                                <path
                                                                                    d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                                </path>
                                                                            </svg></span><span
                                                                            class="MuiTouchRipple-root"></span></button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div data-tut="false" class="jss671">
                            <div class="jss648">
                                <div style="display: flex; align-items: center;">Study Gap<span
                                        class="jss649">(1/15)</span>
                                    <div class="jss686"
                                        title="Target your weaker topics here with a set of cases curated just for you. Attempt them to improve your clinical skills. "
                                        style=""><span>i</span></div>
                                </div><button class="MuiButtonBase-root MuiButton-root jss658 MuiButton-text"
                                    tabindex="0" type="button"><span class="MuiButton-label">VIEW ALL</span><span
                                        class="MuiTouchRipple-root"></span></button>
                            </div>
                            <div class="jss689">
                                <div class="swiper swiper-initialized swiper-horizontal swiper-pointer-events jss691">
                                    <div class="swiper-button-prev swiper-button-disabled"></div>
                                    <div class="swiper-button-next"></div>
                                    <div class="swiper-wrapper" style="transform: translate3d(0px, 0px, 0px);">
                                        <div class="swiper-slide jss695 swiper-slide-active"
                                            style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723 jss697">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-shortness-of-breath.7ce01402.svg"
                                                                    class="jss708" alt="Shortness_Of_Breath"></div>
                                                            <div class="jss736">Charles, 76M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Shortness Of Breath</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-3__2eRc7 jss727">
                                                                <div>LEVEL</div>
                                                                <div>3</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Charles Reed is 76 y.o male
                                                                        presenting with Shortness Of Breath.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695 swiper-slide-next" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Theodor, 30M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-2__119Vo jss727">
                                                                <div>LEVEL</div>
                                                                <div>2</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Theodor Richardson is 30 y.o
                                                                        male presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Bob, 68M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                            <div class="jss751"
                                                                style="background-color: rgb(251, 167, 153);"><img
                                                                    class="jss752"
                                                                    src="/static/media/completed-tick-icon.bd00f01e.svg">
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Bob Rowland is 68 y.o male
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711">
                                                                <div class="jss667"><button
                                                                        class="MuiButtonBase-root MuiButton-root jss663 jss668 MuiButton-contained MuiButton-containedPrimary"
                                                                        tabindex="0" type="button"><span
                                                                            class="MuiButton-label"><img
                                                                                src="/static/media/retry-icon-white.85eb4d27.svg"
                                                                                alt="icon_retry"
                                                                                class="jss669"></span><span
                                                                            class="MuiTouchRipple-root"></span></button>
                                                                    <div class="jss670">
                                                                        <div class="jss673">HISTORY DONE</div>
                                                                        <div class="jss674">24/37</div>
                                                                    </div>
                                                                </div>
                                                                <div class="jss667"><button
                                                                        class="MuiButtonBase-root MuiButton-root jss664 jss668 MuiButton-contained MuiButton-containedSecondary"
                                                                        tabindex="0" type="button"><span
                                                                            class="MuiButton-label"><img
                                                                                src="/static/media/retry-icon-white.85eb4d27.svg"
                                                                                alt="icon_retry"
                                                                                class="jss669"></span><span
                                                                            class="MuiTouchRipple-root"></span></button>
                                                                    <div class="jss670 jss672">
                                                                        <div class="jss673">DIAGNOSIS DONE</div>
                                                                        <div class="jss674">&gt; 21%</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-leg-pain.86596e95.svg"
                                                                    class="jss708" alt="Leg_Pain"></div>
                                                            <div class="jss736">Ahmed, 56M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Leg Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-2__119Vo jss727">
                                                                <div>LEVEL</div>
                                                                <div>2</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Ahmed Abdullah is 56 y.o male
                                                                        presenting with Leg Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss705">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Anderson, 27M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Anderson Chan is 27 y.o male
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-palpitations.f6c4397b.svg"
                                                                    class="jss708" alt="Palpitations"></div>
                                                            <div class="jss736">Graham, 70M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Palpitations</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Graham Nelson is 70 y.o male
                                                                        presenting with Palpitations.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-shortness-of-breath.7ce01402.svg"
                                                                    class="jss708" alt="Shortness_Of_Breath"></div>
                                                            <div class="jss736">Warren, 79M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Shortness Of Breath</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Warren Taylor is 79 y.o male
                                                                        presenting with Shortness Of Breath.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Andrea, 65F</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Andrea Rossi is 65 y.o female
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Eleanor, 65F</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Eleanor Gibbs is 65 y.o female
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-leg-pain.86596e95.svg"
                                                                    class="jss708" alt="Leg_Pain"></div>
                                                            <div class="jss736">Richard, 45M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Leg Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Richard Clarke is 45 y.o male
                                                                        presenting with Leg Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Shaun, 58M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-2__119Vo jss727">
                                                                <div>LEVEL</div>
                                                                <div>2</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Shaun Newton is 58 y.o male
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Karen, 68F</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-2__119Vo jss727">
                                                                <div>LEVEL</div>
                                                                <div>2</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Karen Pace is 68 y.o female
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-shortness-of-breath.7ce01402.svg"
                                                                    class="jss708" alt="Shortness_Of_Breath"></div>
                                                            <div class="jss736">Qing, 60F</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Shortness Of Breath</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-2__119Vo jss727">
                                                                <div>LEVEL</div>
                                                                <div>2</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Qing Li is 60 y.o female
                                                                        presenting with Shortness Of Breath.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Christina, 54F</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Christina Miller is 54 y.o
                                                                        female presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="swiper-slide jss695" style="margin-right: 20px;">
                                            <div>
                                                <div class="jss722 jss723 jss700">
                                                    <div class="jss731"
                                                        style="border-bottom-color: rgb(251, 167, 153);">
                                                        <div class="jss732">
                                                            <div class="jss707"><img
                                                                    src="/static/media/oscer-pres-chest-pain.1d193d4f.svg"
                                                                    class="jss708" alt="Chest_Pain"></div>
                                                            <div class="jss736">Scott, 68M</div><span
                                                                class="jss737">PRESENTING WITH</span>
                                                            <div class="jss738">Chest Pain</div>
                                                            <div
                                                                class="style_level-badge__1FduJ style_level-1__rDArA jss727">
                                                                <div>LEVEL</div>
                                                                <div>1</div>
                                                            </div>
                                                        </div>
                                                        <div class="jss740">
                                                            <div class="jss742"
                                                                style="border-bottom-color: rgb(251, 167, 153);"></div>
                                                            <div class="jss743">
                                                                <div class="jss744">
                                                                    <div class="jss745">history Mode | diagnosis Mode
                                                                    </div>
                                                                    <div class="jss746">Scott Thomas is 68 y.o male
                                                                        presenting with Chest Pain.</div>
                                                                </div>
                                                                <div class="jss747">
                                                                    <div class="jss748">VOICE</div>
                                                                    <div>
                                                                        <div class="jss754"><span
                                                                                class="jss755 jss756"></span><span
                                                                                class="MuiSwitch-root jss757 MuiSwitch-sizeSmall"><span
                                                                                    class="MuiButtonBase-root MuiIconButton-root jss758 MuiSwitch-switchBase MuiSwitch-colorPrimary"
                                                                                    aria-disabled="false"><span
                                                                                        class="MuiIconButton-label"><input
                                                                                            class="jss761 MuiSwitch-input"
                                                                                            name="audioSwitch"
                                                                                            type="checkbox"
                                                                                            value=""><span
                                                                                            class="MuiSwitch-thumb"></span></span><span
                                                                                        class="MuiTouchRipple-root"></span></span><span
                                                                                    class="MuiSwitch-track"></span></span><span
                                                                                class="jss755"></span></div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="jss711"><button
                                                                    class="MuiButtonBase-root MuiButton-root jss663 MuiButton-contained MuiButton-containedPrimary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">History<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button><button
                                                                    class="MuiButtonBase-root MuiButton-root jss664 MuiButton-contained MuiButton-containedSecondary"
                                                                    tabindex="0" type="button"><span
                                                                        class="MuiButton-label">Diagnosis<svg
                                                                            stroke="currentColor" fill="currentColor"
                                                                            stroke-width="0" viewBox="0 0 512 512"
                                                                            class="jss666" height="1em" width="1em"
                                                                            xmlns="http://www.w3.org/2000/svg">
                                                                            <path
                                                                                d="M500.5 231.4l-192-160C287.9 54.3 256 68.6 256 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2zm-256 0l-192-160C31.9 54.3 0 68.6 0 96v320c0 27.4 31.9 41.8 52.5 24.6l192-160c15.3-12.8 15.3-36.4 0-49.2z">
                                                                            </path>
                                                                        </svg></span><span
                                                                        class="MuiTouchRipple-root"></span></button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>