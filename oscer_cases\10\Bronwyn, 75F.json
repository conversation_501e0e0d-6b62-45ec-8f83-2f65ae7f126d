{"metadata": {"name": "<PERSON><PERSON><PERSON>, 75F", "scraped_at": "2025-09-05T12:41:36.359665", "script_url": "https://www.oscer.ai/pwf/script/SAsoqMNyTuA6"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based at the local Emergency Department. <PERSON><PERSON><PERSON> Meadows is a 75 year old female (she/her) who has come into the ED complaining of leg weakness. Please take a focused history from Bronwyn with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON> and I am a 75 year old female.", "My pronouns are she/her.", "I've come into the hospital today because of this weakness that I had in my left leg."], "Persona": ["I've seen all the ads on television about stroke symptoms and I'm concerned that I'm having one. I saw what it did to my <PERSON>, it's my worst nightmare for that loss of independence to happen to me."], "HOPC": ["I got up in the middle of the night to use the toilet and noticed that my left leg was completely weak.", "I hobbled over to the bathroom thinking that I'd slept in a weird position and the blood supply would eventually return but it took about an hour for my left leg to return back to normal.", "My leg just wasn't responsive at all, it felt completely dissociated from my body.", "The weakness was throughout my entire leg, it wasn't just one spot or movement in particular.", "Apart from the leg weakness I didn't notice any other symptoms.", "I didn't notice that my face was droopy.", "I had no numbness or tingling sensation anywhere.", "I don't think there was any changes to my speech. My husband never mentioned anything.", "I didn't have any pain any where.", "I had no changes to my vision.", "When I was walking I didn't have any changes to my balance or dizziness.", "I haven't had any fevers or been feeling unwell.", "I haven't had any back pain.", "I haven't had any headaches of late."], "PMHx": ["I was diagnosed with type 2 diabetes in my early sixties, so I guess I've had it for close to fiften years now.", "My GP has me taking 500mg Metformin everday for the diabetes.", "My doctor has never mentioned my diabetes is poorly controlled, I've never had any troubles with my eyes or feet like they tell me to look out for.", "I also have high blood pressure. They diagnosed me with that about 10 years or so now.", "My blood pressure is reasonably well controlled, they have me on a tablet called amlodopine for it. I take 5mg per day.", "I have never had any surgeries.", "I don't have any allergies that I am aware of.", "My vaccinations are up to date including my covid-19 vaccination."], "FMHx": ["None of my parents are alive anymore, they both died quite young.", "My father had a heart attack when he was 54 years old.", "My mother had a big stroke when she was 68 years old.", "My brother has had some heart troubles too, he's had surgery for some blocked arteries and has high blood pressure like me."], "SHx": ["Unfortunately I am a smoker, it's a nasty habit I know.", "I have smoked half a packet a day for 50 years or so.", "I still drink alcohol but not much these days. I have maybe 3 glasses of white wine total in an average week.", "I have never used any recreational drugs.", "I live at home with my husband <PERSON>.", "My diet is okay. My husband and I love good food, so we probably eat more cheese and carbs than we should for our age.", "I walk with some girlfriends a couple of times a week, although I know we can always do more exercise at our age.", "I love tending to the garden, I am in there most days pottering around.", "After the kids grew up I worked a few days a week as a receptionist, but I stopped that 10 years ago now.", "We have two adult kids, they're 44 and 40 years old now. We have 5 grandchildren all up."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 13, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Walk", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Injuries", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Asking Generally"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Spasms", "score": 1, "items": ["Asking Generally"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Loss of Height", "score": 1, "items": ["Loss of Height"]}, {"title": "Neck Stiffness", "score": 1, "items": ["Neck Stiffness"]}, {"title": "Vision Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Auditory Changes", "score": 1, "items": ["Hearing Loss"]}, {"title": "<PERSON><PERSON>ness", "score": 1, "items": ["<PERSON><PERSON>ness"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Urinary Retention", "Urinary Incontinence"]}, {"title": "Speech Difficulty", "score": 1, "items": ["Speech Difficulty"]}, {"title": "Balance Difficulty", "score": 1, "items": ["Balance Difficulty"]}, {"title": "Sensitive to Light", "score": 1, "items": ["Sensitive to Light"]}, {"title": "Sensitive to Sound", "score": 1, "items": ["Sensitive to Sound"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Faecal Incontinence"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Asking Generally"]}, {"title": "Coordination Difficulty", "score": 1, "items": ["Ataxia"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Fractures"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Stroke", "Migraine", "Asking Generally", "Transient Ischaemic Attacks"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Dyslipidaemia", "Asking Generally", "Atrial Fibrillation"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally", "Marfan's Syndrome"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Stroke", "Migraine", "Asking Generally", "Neurological Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Functional History", "score": 1, "subcategories": [{"title": "Domestic Activities Of Daily Living", "score": 1, "items": ["Domestic Activities"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}