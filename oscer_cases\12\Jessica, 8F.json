{"metadata": {"name": "<PERSON>, 8F", "scraped_at": "2025-09-05T13:05:55.526401", "script_url": "https://www.oscer.ai/pwf/script/3YgSLWdNp9wq"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the GP clinic. <PERSON> (she/her) aged 8, has been brought in by her mother, <PERSON><PERSON>. She presents with urinary frequency. Please take a history from <PERSON><PERSON> to determine the most likely diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["<PERSON> and her mother, <PERSON><PERSON>, have come to see you at the GP clinic.", "<PERSON><PERSON> tells you that <PERSON> is weeing a lot recently."], "Persona": ["<PERSON> seems a bit flat, so her mum, <PERSON><PERSON>, does the talking.", "<PERSON><PERSON> is friendly and very happy to chat about her daughter."], "HOPC": ["<PERSON><PERSON> tells you that she has noticed her daughter is needing to wee a lot.", "She is going to the bathroom to wee about 10 times a day for the past two days. This is not normal for <PERSON>.", "<PERSON><PERSON> says that she is also complaining of it hurting when she wees - a stinging sort of sensation.", "<PERSON> has never experienced anything like this before.", "<PERSON><PERSON> also seems to think that the urine smells a bit odd, somewhat unpleasant. However, it still looks a normal light-yellow colour.", "Aside from the weeing symptoms which all appeared 2 days ago, <PERSON><PERSON> has noticed that <PERSON> has been a bit less herself and energetic over the past couple of days, and thinks she feels hot.", "<PERSON><PERSON> hasn't measured her temperature, as she doesn't have a probe at home.", "<PERSON><PERSON> has also noticed that <PERSON> has been struggling to poo for the last couple of weeks.", "Usually <PERSON> would poo everyday, at least once, but now hasn't gone for about 3-4 days and has been struggling with constipation for the last 2 weeks or so.", "Previously, <PERSON>' poos would be soft, but formed, and mid-brown. At the moment, her poos are firm, hard to pass, but still brown.", "There is no blood or pus in her poos.", "On top of this, <PERSON> has been experiencing a sore tummy for the past 2-3 days. Her tummy is sore below her belly button, centrally at the crease of her pants.", "<PERSON> has not experienced any nausea or vomiting, obstipation, headaches, dizziness, haematuria, flank pain, weight change, bloody stools or polydipsia.", "The remaining review of systems is normal.", "<PERSON><PERSON> is worried that <PERSON> hasn't bounced back after a couple of days now."], "PMHx": ["<PERSON> has no known medical conditions, nor has she ever needed to come into hospital in the past.", "Her birth and development were without any complications.", "There have been no concerns from Mum & Dad, or any medical professionals regarding <PERSON>' development.", "<PERSON> has not had a history of bed-wetting.", "She has not yet had her first period."], "FMHx": ["There is no family history of any conditions.", "<PERSON>' siblings, <PERSON> (4) and <PERSON> (11) had uncomplicated births and development."], "SHx": ["<PERSON> is in grade 3 at the local public primary school.", "She is enjoying school and has two best-friends, named <PERSON> and <PERSON>.", "<PERSON> is not feeling up to going to school today, and is a bit worried about weeing herself at the moment.", "There is no history of domestic or sexual abuse. <PERSON> feels safe at home.", "<PERSON> is not been sexually active, nor has she consumed any alcohol, drugs or cigarettes.", "<PERSON> has a great relatioship with her parents and brothers, and has no concerns about living at home.", "<PERSON>' diet is good. She eats wheat-based cereal with milk in the morning. For school, she takes two pieces of fruit, one small packet of popcorn and 1 sandwich. Once she gets home, she has some afternoon tea, which is a mix of fruit, crackers and maybe a sweet. Then for dinner, we mix up the menu, but it's mostly stir-fry, pasta or something like that.", "She drinks water at home, but <PERSON><PERSON> isn't sure how much water <PERSON> is drinking at school."]}, "marking_rubric": {"HOPC": [{"title": "Urinary Frequency", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally", "Baseline Urination"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Amount"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Patient Concerns", "score": 1, "subcategories": [{"title": "Patient Concerns", "score": 1, "items": ["Patient Concerns"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Fluid Intake", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Urinary Symptoms", "score": 2, "items": ["Cloudy Urine", "Frothy Urine", "Asking Generally", "Malodorous Urine", "Painful Urination", "Urinating at Night", "Sweet-Smelling Urine", "Urinary Incontinence"]}, {"title": "Altered Behaviour", "score": 2, "items": ["<PERSON><PERSON>", "Quiet", "Confused", "Irritability", "Asking Generally"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Thirsty", "score": 1, "items": ["Asking Generally"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Flank Pain", "score": 1, "items": ["Flank Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Poor Feeding", "score": 1, "items": ["Poor Feeding"]}, {"title": "Skin Changes", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Acetone Breath", "score": 1, "items": ["Acetone Breath"]}, {"title": "Appetite Change", "score": 1, "items": ["Asking Generally", "Loss of Appetite", "Increased Appetite"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Genital Discharge", "score": 1, "items": ["Asking Generally"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Constipation", "Asking Generally", "Mucous in Stools", "Most Recent Bowel Movement"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Developmental History", "score": 2, "subcategories": [{"title": "Developmental Concerns", "score": 1, "items": ["Asking Generally"]}, {"title": "Developmental Milestones", "score": 1, "items": ["Talking", "Eating Solids", "Toilet training"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Thyroid Disease", "Type 1 Diabetes", "Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Genitourinary Disease", "Urinary Tract Infection"]}], "Social": [], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "<PERSON><PERSON><PERSON>"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}