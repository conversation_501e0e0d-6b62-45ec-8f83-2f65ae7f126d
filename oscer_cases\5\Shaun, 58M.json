{"metadata": {"name": "<PERSON>, 58M", "scraped_at": "2025-09-03T00:30:36.379095", "script_url": "https://www.oscer.ai/pwf/script/zL69n8BA5qqc"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 58 year old male. He is presenting to the emergency department complaining of chest pain. Please take a focused history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m a 58 year old male and I work as a train driver. My pronouns are he/him.", "I’ve come in to see the doctor because it feels like someone has dropped a tonne of bricks on my chest."], "Persona": ["Concerned and erratic, doesn’t know what is happening but worried about it.", "My Dad died from a heart attack, I hope it’s not that, I’m only 58.", "I’m really concerned about how hard my heart was pounding, I’ve never experienced anything like that before."], "HOPC": ["My chest was feeling a bit tight when I was playing golf this afternoon, and then all of a sudden when I was fetching a ball it felt like a tonne of bricks had come down on my chest. I came here straight afterwards.", "The pain is in the middle of my chest.", "I also had an ache in my left shoulder.", "The chest pain isn’t sharp, it’s more of a crushing pain.", "The chest pain is a 7/10.", "I was all sweaty which never happens when I’m playing golf.", "While this was all happening, my heart seemed to be thumping madly in my chest.", "I also felt like I couldn’t breathe, I almost thought I was going to pass out.", "Deep breaths don’t make the pain worse.", "The pain doesn’t go down to my back at all.", "I’ve never had any troubles with indigestion", "I haven’t had a fever.", "I haven’t had a cough.", "My mood was fine before all this happened, I’ve never had troubles with my mental health."], "PMHx": ["The doctors picked up that I had high blood pressure ten years ago in one of my work medicals. I’ve been on 10mg of Amlodipine daily ever since.", "I haven’t had any other surgeries or illnesses.", "I don’t take any other prescription medications.", "I take some ibuprofen if my back is playing up, it sometimes happens because I’m always sitting at work driving the trains.", "I don’t take any other vitamins or supplements.", "I don’t have any allergies.", "My vaccinations are all up to date."], "FMHx": ["Dad’s heart gave way when he was 68 years old, he had a heart attack.", "<PERSON>’s still alive and doing pretty well for her age, her bones are a bit brittle but apart from that she’s okay."], "SHx": ["I’ve smoked for as long as I can remember, I was a teenager when I got into it so I guess it’s been at least 40 years.", "I smoke a pack a day, it’s not good for the budget with the cost of the things nowadays.", "I normally drink a can or two of beer a night, it depends how hard the day has been!", "I’ve been drinking since I was 18.", "I’ve never done any recreational drugs, that’s not my scene.", "My diet isn’t as good as it should be, especially with the high blood pressure I have.", "My wife cooks us the usual meat and veggies at night, but it’s the snacks that get me. I can’t help myself, I’m always snacking while I’m on the trains.", "I weigh a few more stones than I should.", "I live at home with my wife, we’ve got a daughter but she’s moved out now and off at University.", "I haven’t travelled anywhere recently."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 16, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Burning", "Ripping", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 4, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 2, "items": ["Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Eating or Diet", "Context at Onset", "Physical Activity", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Arm Pain", "score": 1, "items": ["Arm Pain"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Asking Generally", "Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Heart Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 2, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally", "Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": []}}}