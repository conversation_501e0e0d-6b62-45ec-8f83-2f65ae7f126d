{"metadata": {"name": "<PERSON>, 33M", "scraped_at": "2025-09-03T00:10:44.030416", "script_url": "https://www.oscer.ai/pwf/script/hoDFZt1JyFJc"}, "tabs": {"doctor_information": {"Intro": "You are a GP registrar working at an inner-city practice. You are about to see a new patient, <PERSON>,. who has some stomach pain. You know you need to establish both a working diagnosis as well as gather a comprehensive past history.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am 33 years old.", "I identify as male.", "I have come to my local GP today because of this stomach pain that won’t seem to go away."], "Persona": ["I am here so you can fix whatever is wrong with me so I can continue living my life as normal.", "I am very happy to answer any of your questions."], "HOPC": ["I have got this stomach pain that is starting to get pretty annoying. It’s just really uncomfortable and it won’t go away. It is in the top right side of my stomach.", "The pain started 5 days ago.", "I can’t think of anything that would have caused this.", "I have been feeling pretty nauseous, I haven’t vomited.", "I have had no appetite and I am exhausted all the time, I have no energy.", "I think I have a bit of a fever, I just feel like I’m running a bit hotter than normal. I haven’t measured it.", "This all came on pretty suddenly. I was feeling pretty well 6 days ago, and then I woke up the next morning feeling pretty awful!", "Someone actually mentioned to me this morning that my eyes were looking a little yellow.", "I feel muscle ache’s all over my body.", "I don’t have a cough or a runny nose or anything like that."], "PMHx": ["I don't have any allergies that I’m aware of.", "I don’t know if I have had any of my vaccinations, I’m pretty bad at keeping track of all that stuff.", "I don’t have any medical conditions, and I have never been to hospital.", "I don’t take any medications."], "FMHx": ["I lost touch with my family a long time ago. I don’t know anything about their health."], "SHx": ["My diet isn't great, I eat whatever I can afford really. I eat a lot of take-out food.", "I go to the gym every day. I like to keep fit.", "I haven't had a drink the past week or so, which is saying something for me. Usually, I'd have 10 or so beers a week. I’ve been drinking alcohol since I was a teenager.", "I've been smoking for the past 15 years. I smoke about half a pack a day but I haven’t been smoking the past week, I just haven’t felt like it. I have never considered quitting smoking.", "I try to stay away from illegal drugs although I did experiment with some heroin while I was in prison. It probably wasn’t very safe, we were all sharing needles.", "I got out of prison about 1 month ago, I was only there for 6 months. I don’t feel like talking about why I was in prison today, maybe another time.", "I am sexually active. I have had approximately 10 different sexual partners in the past 12 months, and about 3 sexual partners in the past 3 months. I have sex with men and women. I don't usually use condoms. I haven’t had a STI check in about a year.", "I have had chlamydia in the past, about 2 years ago, but I got it treated.", "I dropped out of school in year 10. Since then I have had lots of part-time and casual jobs mainly working as a mechanic.", "I have lot’s of tattoos. I got my first tattoo when I was only 15 years old."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Pain", "score": 16, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Burning", "Cramping", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Lower", "Upper", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Eat", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Vomiting", "Medications", "Eating or Diet", "Asking Generally", "Positional or Movements", "Defecating or Flatulating"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Ideas", "Injuries", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Defecating or Flatulating", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Constipation", "Asking Generally", "Defecating Blood"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Hepatitis", "Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Tattoos", "Piercings", "Blood Transfusion"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Hepatitis", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Hepatitis B", "Liver Cancer", "Pancreatitis", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Fatty Foods", "Asking Generally", "Contaminated Food or Water"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 2, "items": ["IV Drug Use", "User Status"]}]}], "Basics": [{"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}