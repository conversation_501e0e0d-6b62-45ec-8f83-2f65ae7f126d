{"metadata": {"name": "<PERSON>, 22M", "scraped_at": "2025-09-03T00:19:56.743487", "script_url": "https://www.oscer.ai/pwf/script/bVq8BYh2F8f6"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based in a General Practice clinic. <PERSON> is a 22 year old male (he/him) who has presented with a fever. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm currently studying engineering at university.", "My pronouns are he/him.", "I am a 22 year old man and I have come to the GP today because I have a fever."], "Persona": ["I am a young and fit male.", "I am not anxious.", "I get the occasional cold but this is worse than usual so I am a little concerned.", "I decided to check my temperature today and got quite worried that it was 38.1 degrees Celsius."], "HOPC": ["I began to feel unwell about three days ago.", "I have had a runny nose and a hacking, dry cough.", "I have not coughed up any blood.", "I just feel generally unwell and all of my muscles ache. I don't have muscle weakness, it's more of an ache.", "I'm pretty sure I have a fever. Last time I checked it was 38.1 degrees Celsius.", "I'm feeling pretty tired. I don’t have much energy and I’m unable to really study for university.", "I can’t remember coming into contact with any sick people.", "Nothing seems to make this illness better or worse, panadol helps a bit with the muscle aches. It's pretty much the same since it started.", "I do not have any abdominal pain.", "I have not travelled recently.", "I have not lost my sense of taste or smell.", "I have not been in contact with any covid positive cases. I always wear a mask and socially distance when necessary.", "I haven't noticed any changes in my bowel habits or urination. I don't have any back pain.", "I don't have a headache, or any neck stiffness or photophobia."], "PMHx": ["I have no other medical conditions. I get seasonal hayfever but I do not have asthma or eczema.", "I've never been to hospital.", "I don't take any medications.", "I'm not allergic to anything.", "I am up to date with my childhood vaccinations but never get the flu vaccine."], "FMHx": ["My mother is 48 and was diagnosed with high blood pressure when she was 44, she manages it well with lifestyle modifications. My father is 52 and got told last year he has type 2 diabetes. Now he takes metformin everyday and joins mum on walks.", "I don't have any siblings.", "I don't know of any other medical conditions that run in the family."], "SHx": ["I live at home with my parents.", "I study engineering at university.", "I have never smoked.", "I drink 4 to 5 beers on the weekends approximately every fortnight with friends.", "I have never taken any recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Fever", "score": 10, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Temperature"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Patient Ideas", "Recent Illness", "Context at Onset", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Coryzal Symptoms", "score": 2, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Chest Pain", "score": 1, "items": ["Tightness", "Chest Pain"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Vaccinations", "score": 1, "items": ["Covid-19"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Covid-19 Test"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Immunodeficiency History", "score": 1, "items": ["Immunocompromised"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Pneumonia", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "Infection Prevention", "score": 1, "items": ["Hand Hygiene", "Social Distancing", "Personal Protective Equipment Use"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}