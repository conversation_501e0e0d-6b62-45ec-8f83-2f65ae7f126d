{"metadata": {"name": "<PERSON>, 45M", "scraped_at": "2025-09-03T00:29:24.716982", "script_url": "https://www.oscer.ai/pwf/script/9biVevENuqNh"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 45 year old male. He has presented to the emergency department with leg pain. You are the first doctor to see <PERSON>. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m 45 years old and I work as a wool trader. My pronouns are he/him.", "I’ve come in to see the doctor because I’ve got a severe pain in my right leg."], "Persona": ["I’m very busy as a small business owner, I don’t have time for this. Can you just give me something for the pain so I can get out of here."], "HOPC": ["I drove back from a wool trading convention 3 days ago, it was a 9 hour drive and I only stopped once for petrol.", "The pain in my leg came on gradually, after I got home I had a pain in my right calf.", "The pain in my calf has been quite constant, I thought it was just a cramp.", "But this morning I woke up and the pain was much worse.", "The pain in my calf is now a 6/10 if I had to give it a number.", "The pain feels like a dull ache.", "It doesn’t radiate anywhere else.", "My right leg is a bit bigger than my left and it feels tighter.", "My right calf is warmer too if you touch it, see, have a feel.", "The skin around my right calf is a bit more red too.", "My calf is quite tender, it hurt when I was trying to massage the cramp out.", "I’ve been hobbling around the last few days.", "I’ve haven’t found anything that makes the pain in my calf better.", "I’ve been taking paracetamol to try help with the pain, but that’s not doing much.", "I’ve got meetings with clients in the next few days so I’ll be driving around again and I need to get this sorted. I can’t cancel them.", "I don’t have a cough or any shortness of breath.", "I haven’t had any trauma or strained a muscle.", "I haven’t had any muscle weakness or changes in sensation.", "I haven’t had a fever.", "I haven’t had any cuts or lesions to my right calf."], "PMHx": ["I’ve never really been sick before to tell you the truth doc.", "I don’t have any allergies.", "My vaccinations are all up to date.", "I don’t have any regular medications, I’m only 45!", "I very occasionally take paracetamol or an ibuprofen if I need it, like the last few days.", "I don’t take any supplements or things like that, I don’t believe in it."], "FMHx": ["My mother died when she was 64 years old after a complication from her hip surgery. The doctors said a big clot got stuck in her lung.", "My father is 72 years old, he’s got type 2 diabetes but other than that he’s perfectly well.", "I do not have any siblings."], "SHx": ["I own a small business with a friend. We’re wool traders, so there’s lots of driving and travel involved.", "I live at home with my wife <PERSON><PERSON><PERSON> and our two children.", "I eat quite well at home because we’ve got the kids. But my job involves a lot of travel, so I don’t eat great when I’m out on the road.", "I do smoke, and I don’t really have the motivation to quit at the moment because it’s a good distraction while I’m travelling.", "I have 20 cigarettes a day I suppose, it’s been like that for the last 20 years.", "I’m a big connoisseur of red wine, I have 2 glasses of red a day at work lunches or just at home with dinner.", "Back in my University days I occasionally smoked marijuana with friends. But I’ve never touched any recreational drugs since.", "I don’t do a whole lot of exercise, but <PERSON><PERSON><PERSON> and I normally go for a walk for an hour when I get back from work. It’s our quality time together.", "I guess I’m about 15 kilograms heavier than I should be for my size."]}, "marking_rubric": {"HOPC": [{"title": "Leg <PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Throbbing", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Injuries", "Palpating", "Immobility", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Foot Pain", "score": 1, "items": ["Foot Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Hair Changes", "score": 1, "items": ["Hair Loss"]}, {"title": "Skin Changes", "score": 1, "items": ["<PERSON><PERSON>", "Sc<PERSON>", "<PERSON>y", "Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Clotting Disorders", "Deep Vein Thrombosis"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally"]}, {"title": "Rheumatological Disease History", "score": 1, "items": ["Rheumatoid Arthritis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Clotting Disorders", "Deep Vein Thrombosis"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}