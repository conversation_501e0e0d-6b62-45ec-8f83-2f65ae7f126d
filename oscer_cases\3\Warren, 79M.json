{"metadata": {"name": "<PERSON>, 79M", "scraped_at": "2025-09-03T00:14:43.614791", "script_url": "https://www.oscer.ai/pwf/script/Pz0YZNQKid9H"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You are seeing <PERSON>, a 79 year old male who has come in complaining of shortness of breath. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m a male and this year I’m 79 years young. My pronouns are he/him.", "I’ve come into the doctor’s because <PERSON> made me. She’s sick and tired of me always complaining that I can’t do anything around the house because I get a bit breathless."], "Persona": ["I guess I’m most concerned that it’s something related to my heart.", "I had some troubles with my heart 5 years ago, and they said to watch out for breathlessness or pain."], "HOPC": ["I’ve been feeling a little short of breath, especially when I’m walking around.", "The shortness of breath has been getting worse in the last 2 or 3 days.", "I can only make it 20 metres or so before I need to sit down to get my breath back.", "Sitting and resting seems to make things better, I’m fine then.", "I’ve actually woken up twice feeling totally short of breath in the last few days. I had to sit up and gasp for breath until I could get it back.", "I normally sleep with 2 pillows, I’ve probably done that for 3 years or so, I find I can breathe a bit easier that way.", "The last few years especially I’ve had trouble with my legs getting all swollen.", "By the end of the day I always notice that my socks have left a mark, that’s something that never happened until a few years ago.", "I seem to get a wet cough at night too.", "I haven’t been coughing up any muck, and I haven’t had a wheeze.", "I haven’t been feverish and feeling hot or cold or anything like that.", "I haven’t had any cramping or pain in my legs.", "I do get a bit of chest pain when I’m walking around.", "I haven’t felt nauseous or been confused.", "I have been feeling very fatigued.", "I have not had any recent surgeries, long haul flights or periods of immobility."], "PMHx": ["I’ve had high blood pressure for over 15 years now. The doctor told me I had high cholesterol around that time too.", "I had a heart attack five years ago. Luckily we were visiting the kids in the city so they got me into hospital early and unplugged it quickly.", "The doctors have me on all these tablets since the heart troubles, I’ve got a list of them. I take 40mg of Furosemide twice a day, 16mg of Candesartan once a day, 25mg of Metoprolol two times a day, 100mg of Aspirin a day, and 40 mg of Atorvastatin a day.", "I’ve got enough tablets to worry about to keep the heart going, I don’t take any of that herbal stuff.", "I don’t have any allergies.", "All of my vaccinations are up to date."], "FMHx": ["Like me, Dad had high blood pressure and his heart eventually gave way.", "My Mum’s health was always pretty good, she just passed away from old age.", "I have two kids and a couple of grandkids who are all very healthy.", "I do not have any siblings."], "SHx": ["I live at home with my wife, <PERSON>.", "<PERSON> and my GP have been saying for years that I needed to lose some weight. It wasn’t until the heart attack that I started doing something about it.", "Since I’ve recovered from the heart attack, I was trying to do as much exercise as I could. But recently I can’t do anything. If I go out to get the mail I’m totally spent.", "My diet is pretty good now, I cleaned it up after the heart nearly stopped a few years ago.", "I guess the smokes and drinks were always a weak point for me, I always knew I did too much of them but it was until the heart nearly gave way that I’ve cut back.", "I now have one beer on a Sunday with dinner.", "I used to drink 4-5 beers every night for as long as I can remember, but the heart problems changed that.", "I’ve fully stopped the smoking now.", "I used to smoke probably 10 a day since I was a teenager, see it was all quite normal back then, no one knew how bad the stuff was for you.", "I’ve never touched drugs, and I’ve warned my kids never to either. It’s nasty stuff.", "It’s <PERSON> and <PERSON> at home, we have 2 kids but they’re all grown up and moved into the city, we even have a couple of grandkids!", "I’m retired now, but I had my own business as a commercial electrician for years."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Night-time", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Skin Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal Congestion"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Frothy Urine", "Asking Generally", "Decreased Urination"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Pneumonia", "Asking Generally", "Pulmonary Hypertension"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Asking Generally", "Chronic Kidney Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["NSAID", "Inhalers", "Adherence", "Antibiotics", "Asking Generally", "Past Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Respiratory Disease", "Genitourinary Disease"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 2, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Time Off", "Occupation", "Workload Current", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}