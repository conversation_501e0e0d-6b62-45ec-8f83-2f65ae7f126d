{"metadata": {"name": "Theodor, 30M", "scraped_at": "2025-09-03T00:08:20.692083", "script_url": "https://www.oscer.ai/pwf/script/kyJVSYWbTgcR"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the emergency department. <PERSON>, a 30 year old male, presents complaining of chest pain. Please take a focused history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 30 year old male, and I work as an accountant. My pronouns are he/him.", "I’ve come to the hospital today because I have chest pain."], "Persona": ["I’m a pretty relaxed and happy person.", "I normally like to have a joke, but I am really distressed by this pain.", "I am worried it is something serious."], "HOPC": ["I am experiencing chest pain.", "The chest pain is in the middle of my chest, behind my sternum. The chest pain radiates to my back.", "The pain is at least a 9 out of 10. It is stopping me from being able to move around.", "The chest pain came on suddenly about 1 hour ago. I was at work when it started. I wasn’t doing anything out of the ordinary at the time.", "The pain feels like tearing.", "The chest pain hasn’t gotten any worse or any better since it started.", "Nothing seems to make the pain any better or worse.", "I’ve never had chest pain like this before.", "I have not had a cough.", "The pain does not get better when I lean forward.", "The pain does not change when I move around.", "The pain does not get worse when I take a deep breath or cough.", "I have not had any dizziness or loss of consciousness.", "I have not had any nausea or vomiting.", "I have not had any neck, arm or jaw pain."], "PMHx": ["I’m usually pretty healthy, I don’t have any allergies that I’m aware of. I don’t take any regular medications. My vaccinations are up to date.", "I have Marfan Syndrome.", "I see a cardiologist and a vascular surgeon because of my <PERSON>fan syndrome, but I have never needed any surgery or medications."], "FMHx": ["My father had diabetes and hypertension. He died in his 60s.", "My mother is healthy.", "I have one sister who is healthy.", "I do not have any children."], "SHx": ["I work as an accountant at a big firm. My work is not normally very stressful.", "I am happy at my job.", "I like to exercise. I go for a run 5 times a week.", "I eat a healthy, balanced diet. I eat a lot of vegetables and white meat.", "I have a girlfriend. We live together. I am well supported by my girlfriend.", "I haven’t traveled recently.", "I drink alcohol socially. I usually only drink on the weekends, and I never drink more than 4 beers. I have been drinking since I turned 18.", "I will sometimes have a cigarette. It is only in very occasional social situations, and I never have more than 1.", "I’ve never used recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Injuries", "Palpating", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Arm Pain", "score": 1, "items": ["Arm Pain"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>", "Dyslipidaemia", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Marfan Syndrome", "Ehlers-Danlos Syndrome"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Aortic Dissection", "Clotting Disorders", "Pulmonary Embolism", "Deep Vein Thrombosis", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}