{"metadata": {"name": "<PERSON>, 72F", "scraped_at": "2025-09-03T00:43:18.883076", "script_url": "https://www.oscer.ai/pwf/script/ZYVrieDIbcOW"}, "tabs": {"doctor_information": {"Intro": "You have been asked to see <PERSON>. <PERSON> is a 72 year old female, presenting to general practice with shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 72 year old woman.", "My pronouns are she/her.", "I am a retiree.", "I’ve come to the Emergency Department today because of increasing shortness of breath and a productive cough."], "Persona": ["I don’t want to have to move into a nursing home, I am determined to stay in my own home.", "My husband had to do a lot of convincing to get me to come into the hospital."], "HOPC": ["I have been getting increasingly short of breath over the past week.", "I am usually short of breath because of my COPD, but in the past week it has gotten so bad that I am no longer able to shower myself because of my breathlessness.", "I usually have a productive cough of white sputum, but it has turned green over the past week and I seem to be coughing up more than usual.", "The cough is worse in the mornings.", "Most mornings I cough up about a teaspoon or so of thick, green sputum, that’s relatively thick, and then a few smaller coughing fits during the day.", "I have never coughed up any blood.", "I have tried taking my inhalers (fluticasone/salmeterol), as well my salbutamol inhaler but they aren’t helping much.", "I don't know of anyone around me that has been sick.", "I have not noticed any swelling in my limbs.", "I have not had any recent unexpected weight loss, night sweats or fevers."], "PMHx": ["I have COPD that was diagnosed 2 years ago.", "I take fluticasone/salmeterol inhaler twice daily and salbutamol as required for my COPD.", "I have been to hospital for lower respiratory tract infections three times already this year, my last admission was 2 months ago.", "My last respiratory function tests done 2 months ago identified a FEV1 of 45% of predicted values and my functional mobility was 50% of predicted values.", "I also have a history of obesity and depression."], "FMHx": ["I was not in contact with my parents for a lot of my life so I am not too sure about my family history."], "SHx": ["I am a retired highschool teacher and I live at home with my husband.", "I've smoked a pack of cigarettes every day for the past 40 years. I don’t want to quit smoking.", "I don’t drink alcohol anymore. I used to have a glass of wine with dinners most nights until a few years ago - I figured I was getting too old to keep doing that.", "I eat a relatively balanced diet. I cook all of my food at home.", "I don’t do any exercise because of my shortness of breath.", "I need a bit of help around the house with cooking, cleaning and personal hygeine but my husband is great helping with these. We don't need to rely on anyone else at this stage.", "I have been needing my husband to help shower me this last week though which I have never needed help with before. I just get too short of breath.", "I have a past history of depression, but my mood at the moment is pretty good.", "I have no suicidal thoughts or thoughts of self harm.", "My 2 children live close by and check in on us at least once a week."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 11, "subcategories": [{"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Talk", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Change Over Time", "Onset of Worsening", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Resting", "Allergies", "Night-time", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Season or Temperature", "Waking up Short of Breath", "Shortness of Breath when Laying Down", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Exacerbation Symptoms", "score": 1, "subcategories": [{"title": "Sputum Colour", "score": 1, "items": ["Asking Generally"]}]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON>sal <PERSON>", "Nasal <PERSON>harge", "Post-<PERSON><PERSON>", "Nasal Congestion"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Medication Side Effects", "score": 1, "items": ["Allergies", "Medication Side Effects"]}, {"title": "Prescription Medications", "score": 1, "items": ["Duration", "Inhalers", "Adherence", "Cessation", "Regularity", "Antibiotics", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["X-Ray", "Spirometry", "Chest X-ray"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 2, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Cardiac Failure", "Acute Bronchitis", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "Past Medical History (2)", "score": 2, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["COPD", "Cancer", "Lung Cancer", "Heart Failure", "Asking Generally", "Respiratory Disease", "Alpha-1 Antitrypsin Deficiency"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Living Situation", "score": 1, "items": ["Children", "Nursing Home", "Living Situation"]}]}, {"title": "Functional History", "score": 2, "subcategories": [{"title": "Mobility", "score": 1, "items": ["Walking", "Mobility"]}, {"title": "Social Support", "score": 1, "items": ["Independent"]}, {"title": "Domestic Activities Of Daily Living", "score": 1, "items": ["Cooking", "Cleaning", "Domestic Activities"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Feeding", "Dressing", "Toileting", "Asking Generally", "Personal Hygiene", "Domestic Activities"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}