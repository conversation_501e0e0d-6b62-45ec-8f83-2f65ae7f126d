{"metadata": {"name": "<PERSON>, 54M", "scraped_at": "2025-09-03T11:52:05.301539", "script_url": "https://www.oscer.ai/pwf/script/muIZ4DmW71ur"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 54 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 54 year old man and I work as a truck driver.", "I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out."], "Persona": ["I’m a pretty relaxed and happy person.", "I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.", "I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about."], "HOPC": ["I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.", "I can now only walk a few kilometres before getting very short of breath and needing to rest.", "Initially I was only getting short of breath when I was working hard.", "For the past 2 or 3 weeks I have gotten breathless with light exertion.", "I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.", "I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.", "Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.", "I’m otherwise feeling well.", "I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats."], "PMHx": ["I’m usually pretty healthy and I have no underlying medical conditions.", "I don’t have any allergies that I’m aware of. I don’t take any regular medications.", "I take the occasional course of antibiotics for chest infections that I get most winters.", "I can’t remember what the antibiotic I take is called.", "I haven’t had a chest infection in over 6 months."], "FMHx": ["My father has high blood pressure.", "There are no other medical conditions in my family.", "I have no siblings.", "My children are both happy and healthy."], "SHx": ["I work as a truck driver. My current role involves delivering food to supermarkets. This requires me to travel long distances to various different locations.", "I have never worked with asbestos or any dangerous chemicals or fumes.", "I’ve been happily married to my wife for almost 30 years and live at home with my wife <PERSON> and two adult children.", "I have no pets.", "I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking. The smoking helps me get through a long day of truck driving.", "I smoked marijuana in my late teens.", "I usually drink a beer or two most nights.", "I eat a relatively balanced diet.", "I don’t do much exercise, I prefer to watch sport on TV rather than play it.", "I wouldn’t say that I’m overweight."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Onset of Worsening"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Allergies", "Night-time", "Positional", "Anxiousness", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Season or Temperature", "Shortness of Breath when Laying Down", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Swelling", "score": 1}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Nasal <PERSON>harge", "score": 1}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Cough Aggravating Factors", "score": 1, "items": ["Night-time", "Asking Generally"]}]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Sleep Apnoea", "Bronchiectasis", "Asking Generally", "Respiratory Infections", "Interstitial Lung Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia", "Iron Deficiency"]}]}, {"title": "Medication History", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 2, "items": ["Inhalers", "Adherence", "Antibiotics", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Respiratory Disease", "Interstitial Lung Disease", "Alpha-1 Antitrypsin Deficiency"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Passive Exposure"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 2, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Previous Occupation"]}, {"title": "Occupational Exposure", "score": 1, "items": ["Dust", "Asbestos", "Stone Dust", "General <PERSON><PERSON>s", "Chemicals or Toxins", "Personal Protective Equipment Use"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}