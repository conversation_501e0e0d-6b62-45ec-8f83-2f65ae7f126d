{"metadata": {"name": "<PERSON><PERSON>, 64M", "scraped_at": "2025-09-05T12:48:46.384677", "script_url": "https://www.oscer.ai/pwf/script/DEpmgipnZtRi"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in a surgical outpatient clinic. The registrar has asked you to see <PERSON><PERSON>, who is a 64 year old man (he/him) that has been referred due to weight loss. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON> and I am a 64 year old male (he/him).", "I've been referred to the clinic today because I've lost some weight over the past couple of months and my GP was concerned."], "Persona": ["My biggest concern is that I've got some sort of cancer, my father was taken from us in his late sixties because of bowel cancer.", "I feel quite healthy and well for my age, but I know weight loss like this is not normal."], "HOPC": ["The weight loss has been happening for the last 6 months.", "I haven't been trying to lose weight which is the strangest part.", "I have not changed my diet at all recently, I'm still eating the same foods.", "I've lost 9 kilograms in the past few months.", "I've gone down from 87 kilograms to 78 kilograms.", "I actually lost one of my rings while I was out surfing the other day, that's when I realised the weight loss is too much.", "I have noticed some changes to my bowel habits, they're different to what they used to be.", "My bowel habits used to be one brown stool per day, and the stools were a normal shape and smell.", "My stools have become looser, and I have had more episodes of diarrhoea even though I haven't been sick.", "I've also noticed some blood in my stools.", "The blood in my stools is not there every time I use my bowels.", "The blood is red and is mixed in with my stools.", "I've never participated in those government screening programs for bowel cancer. I've got the letters but just never got around to doing it.", "I haven't had any abdominal pains.", "I have not had any fevers.", "I have not had any night sweats.", "I have not had any nausea or vomiting."], "PMHx": ["I've been fit and healthy my whole life.", "I have never been admitted to hospital for anything.", "I don't have any allergies.", "I've had all my vaccinations through childhood and adulthood.", "I've never done any of the cancer screening tests. I know I should but because I've always been healthy I rarely go to the GP."], "FMHx": ["My father passed away from bowel cancer when he was 68 years old, and my mother passed away from pneumonia at 84 years old.", "My younger brother is fine, he doesn't have any medical conditions that I know about.", "No one in my family has had any thyroid or autoimmune conditions.", "No one in my family has had irritable bowel syndrome."], "SHx": ["I'm really into my surfing, I've become a long boarder as I've gotten older.", "I've got a great group of friends. We all go surfing together and then get a coffee afterwards.", "I think I'm really fit for my age. Outside of surfing I also go for a 20 minute run every morning.", "I've never smoked throughout my life, I've always taken pride in my fitness and that's not compatible with smoking.", "I do drink alcohol, but I've never been a big drinker.", "I don't drink during the week. I have 1 or 2 glasses of red wine on a Friday and Saturday night.", "I have never taken any recreational drugs before.", "My diet is quite healthy. My wife and I always eat a lot of protein and vegetables or a salad during the week, and then one the weekend we allow ourselves one treat a meal like pasta or takeaway even.", "I have two grown up boys with my wife <PERSON>. They're 31 and 27 years old.", "I'm a school teacher, although I'm only working part time now."]}, "marking_rubric": {"HOPC": [{"title": "Weight Loss", "score": 10, "subcategories": [{"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Ability to Fit Clothes"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Weight Loss"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Diet", "score": 1, "items": ["Amount of Food"]}, {"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Altered Bowel Habits", "score": 5, "items": ["Diarr<PERSON>a", "Constipation", "Faecal Urgency", "Asking Generally", "Defecating Blood", "Mucous in Stools", "Malodorous Stools", "Pencil-Thin Stools", "Black and Tarry Stools", "Incomplete Faecal Emptying"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Anal Pain", "score": 1, "items": ["Anal Pain"]}, {"title": "Bulging Eyes", "score": 1, "items": ["Bulging Eyes", "Asking Generally"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Skin Lesions", "score": 1}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Asking Generally", "Loss of Appetite"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Anxiousness", "Asking Generally", "Depression or Low Mood"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["St<PERSON>ch Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Polyp", "Diverticulitis", "Coeliac Disease", "Asking Generally", "Inflammatory Bowel Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["Colonoscopy", "Cancer Screening", "Faecal Occult Blood Test"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Polyp", "Bowel Cancer", "Asking Generally", "Autoimmune Disease", "Gastrointestinal Disease", "Inflammatory Bowel Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Gluten", "Amount of Fibre", "Asking Generally", "Red Meat-Rich Diet"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}