{"metadata": {"name": "<PERSON>, 66M", "scraped_at": "2025-09-05T12:12:00.640519", "script_url": "https://www.oscer.ai/pwf/script/M8p46waQFgpJ"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement in general practice. You have been asked to take a history from <PERSON>, a 66 year old gentleman who has presented to the clinic with knee pain.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm 66 years old.", "I've come in to see the doctor today because of this treacherous left knee, it's really been giving me more trouble of late.", "I manage a Performing Arts centre, and I've been getting considerable pain recently traversing the theatre stairs."], "Persona": ["I think it's probably wear and tear from all the running I used to do.", "I'm most concerned about getting the pain under control for work.", "My expectations are that hopefully you have a better solution than a few ibuprofens and pushing through the pain!"], "HOPC": ["I guess the knee pain all started around 5 years ago.", "The knee pain was not too bad at the start but it has been worsening, especially lately.", "The knee pain is on my left side.", "The knee pain has been worsening.", "I typically find the knee pain is worse at night after a day of using it.", "The knee pain doesn't radiate anywhere else.", "My whole knee feels achy at the end of the day.", "My knee is tender to touch all over.", "My knee pain would have to get up to an 8 out of 10.", "It feels like there are little bits of bone or something in my knee, it can making a cracking noise when I move it.", "I have been taking ibuprofen for the knee pain, it helps a little but I still have a lot of pain.", "Walking up the theatre stairs at work really aggravates the knee pain. Any movement in general makes it worse.", "I have not had a fever.", "My left knee is not red, hot, or tender.", "I don't have any skin rashes or plaques.", "I have never been diagnosed with psoriasis.", "I don't have any lumps or bumps around or behind my knee.", "I have not experienced an injury or trauma to my knee recently.", "I have not been unwell recently.", "I have not had any diarrhoea."], "PMHx": ["Apart from the left knee pain, I've been healthy and well.", "I'm taking one blood pressure tablet, but my blood pressure has been reasonably well controlled.", "I take 5mg of amlodipine daily.", "I also take ibuprofen for my knee pain.", "I take 400 mg when I need it. Probably two or three times a day when it's bad.", "I have been to hospital for a kidney stone 7 or 8 years ago. And once when I had pneumonia as a child.", "I have never had any surgeries."], "FMHx": ["I have two younger sisters and they are both healthy with no medical conditions as far as I know. Our parents have both passed away. Our father died when we were younger in an accident, and our mother passed away in her sleep because of her heart."], "SHx": ["I live at home with my wife <PERSON>.", "We have two boys together but they're in the twenties and have moved out of the house now.", "I manage a Performing Arts centre.", "I am not a smoker anymore.", "I smoked for maybe 10 years in my early twenties to thirties, but it was only half a packet of cigarettes a day.", "I still drink alcohol.", "I have one glass of red wine on average per night.", "I have never been a heavy drinker.", "I have never used any recreational drugs before.", "I'm 177cm tall, and I think I was around 90 kilograms the last time I was on the scales.", "I have been a runner all my life, I used to do marathons. But I would be lucky to get two or three short walks in a week because of the knee pain. After a day at work I can't do anything with it."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 16, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON><PERSON>", "Locking", "Clicking", "Grinding", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Other Joints", "Asking Generally", "Localised or Generalised"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Walk", "Asking Generally", "Ability to Exercise", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 6, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Sitting", "Exercise", "Laying Down", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Injuries", "Movement", "Night-time", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wounds", "score": 1, "items": ["Wounds"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Skin Heat", "score": 1, "items": ["Skin Heat"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Knee Deformity", "score": 1, "items": ["Tophus"]}, {"title": "Morning Stiffness", "score": 1, "items": ["Morning Stiffness"]}, {"title": "Walking Difficulty", "score": 1, "items": ["Immobility", "<PERSON><PERSON><PERSON>ait"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Osteoporosis", "Asking Generally", "Septic Arthritis"]}, {"title": "Rheumatological Disease History", "score": 1, "items": ["Joint Disease", "Rheumatoid Arthritis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Gout", "<PERSON><PERSON><PERSON><PERSON>", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Psoriatic Arthritis", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Meat-Rich Diet", "Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Functional History", "score": 1, "subcategories": [{"title": "Mobility", "score": 1, "items": ["Mobility"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}