{"metadata": {"name": "Dael, 26M", "scraped_at": "2025-09-05T12:20:24.119144", "script_url": "https://www.oscer.ai/pwf/script/pSlwAETORQCt"}, "tabs": {"doctor_information": {"Intro": "You are a junior doctor on your Emergency Department rotation. <PERSON><PERSON> is a 26-year-old man who is presenting to the ED with noisy breathing. Please take a history from <PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON>, I am a 26-year-old male.", "My pronouns are he/him.", "I have come to the ED because I am very worried about my breathing, I am making a horrible noise."], "Persona": ["I am usually a very fit and healthy 26-year-old, I’ve never really had to see the doctor before.", "I am worried about the change in my breathing.", "I am a very anxious person, I have struggled on and off with my mental health for a number of years now."], "HOPC": ["I am having trouble with my breathing.", "It began about 2 hours ago when I was in an exam at uni, it started quite suddenly.", "I am making a horrible loud noise when I try to breathe in, it is a kind of low-pitched noise.", "It hasn't really changed much since it began.", "I have never had anything like this happen before.", "I also feel short of breath...I feel as though I can’t get enough air in.", "I feel some pain in my chest. It is on the left-hand side.", "It came on suddenly.", "It is a dull pressure type of pain, it does not radiate.", "My heart has been beating very fast and hard since this began.", "I have been sweating and shaking since this all began too.", "I have been coughing in between some of my breaths.", "I felt nauseous before this all started, I still feel nauseous now, I have not vomited.", "Nothing I have tried has helped any of my symptoms.", "Nothing has made my symptoms any worse.", "I have never had anything like this before.", "Sometimes I get palpitations, or nausea or a little bit short of breath with my anxiety, but nothing close to this in the past.", "I do not have a fever.", "No one around me has been sick.", "I have not been sick recently.", "I can only speak in short sentences.", "This has stopped me in my tracks, I cannot complete any of my normal tasks."], "PMHx": ["I was diagnosed with anxiety when I was 15, I have been managing with cognitive behavioural therapy and the use of a psychologist since then.", "I have never had any surgery.", "I don't take any prescription or over the counter medications.", "I don't have any allergies.", "I am up to date with all of my vaccinations."], "FMHx": ["My dad has high blood pressure and sister has asthma, aside from that everyone is very healthy."], "SHx": ["I am a final year law student at a big university in the city.", "I love what I study but I tend to get very stressed around assessment times.", "My exams have made me feel particularly stressed! Doing well is extremely important to me.", "I work part-time as a tutor.", "I play plenty of sport, basketball is my sport of choice.", "I have a girlfriend, <PERSON><PERSON>, we have been together for 8 years.", "I am sexually active", "I live at home with <PERSON><PERSON>, our relationship is great.", "We also have a cat called <PERSON><PERSON>.", "I get very anxious in social settings; I tend to like events with only a small number of my close friends.", "I don't smoke cigarettes, I have never smoked.", "I drink alcohol, usually only 3-4 beers once a week.", "I have never tried recreational drugs.", "I feel very safe at home.", "I would describe my mental health as up and down, my anxiety has come and gone in waves for the last decade."]}, "marking_rubric": {"HOPC": [{"title": "Noisy Breathing", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Pitch", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Speak", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Stress", "Exercise", "Cold Weather", "Recent Illness", "Asking Generally", "Context at Onset", "Lifestyle Changes"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Sleeping", "score": 1, "items": ["Asking Generally"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Face", "Asking Generally"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Emotional", "score": 1, "items": ["Emotional"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Chest Tightness", "score": 1, "items": ["Chest Tightness"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "Nasal <PERSON>harge"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Anaphylaxis History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Mental Health History", "score": 1, "items": ["Asking Generally", "Depression or Low Mood"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Pneumothorax", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Marfan's Syndrome"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Dust", "<PERSON><PERSON>", "Animals"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asthma", "Anxiety", "Panic Attacks", "Asking Generally", "Respiratory Disease", "Depression or Low Mood"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Self Harm", "score": 1, "items": ["Self Harm"]}, {"title": "Suicidal Risk", "score": 1, "items": ["Suicidal Risk", "Suicidal Thoughts"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Financial Stability", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Passive Exposure"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}