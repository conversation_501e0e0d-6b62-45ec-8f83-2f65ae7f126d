{"metadata": {"name": "Ali, 29M", "scraped_at": "2025-09-02T23:51:49.561372", "script_url": "https://www.oscer.ai/pwf/script/PnnY3bqxNp1N"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 29 year old male, who has presented today to general practice complaining of a cough. You have been asked to see <PERSON>. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m a 29 year old male.", "My pronouns are he/him.", "I work as an engineer in the city.", "I’ve come in to see the doctor today because I've just had this terrible cough that won't seem to go away"], "Persona": ["I'm pretty stressed at the moment.", "This cough is really starting to concern me, I'm worried it might be something serious like cancer."], "HOPC": ["I've had this cough for almost 2 months now.", "My cough is always there, it feels like it’s getting worse every day at the moment.", "My cough started off dry, but now it has mucous in it.", "Nothing really makes the cough better or worse, it’s constant.", "I’m not sure when I started bringing up the phlegm, but it's been a while.", "I’m bringing up about a teaspoon of phlegm maybe, it’s enough for me to be concerned about.", "I've noticed some red streaks in the phlegm recently.", "I think that I might have a very minor fever for a few months too.", "I have checked my temperature a few times and I had a fever of 38 degrees.", "I’ve also lost some weight. I haven't weighed myself, but I did go down a whole belt buckle.", "I have had a few episodes of night sweats, although I can't remember how long ago they started.", "I've also been feeling more tired the last few months.", "I haven’t had any shortness of breath.", "I haven’t had a wheeze.", "I don’t have any chest pain.", "I haven’t had a runny nose or been sneezing."], "PMHx": ["I don't have any medical conditions.", "I have never had surgery or been hospitalised for anything.", "I’m not taking any other prescription or over the counter medications, I never have luckily.", "I only use protein powder as a supplement for the gym.", "I don’t have any allergies.", "I got all the usual Australian vaccines as a kid, I don’t think I haven’t had any since."], "FMHx": ["There are no significant medical conditions in my family."], "SHx": ["I went to China for work and stayed in China for a six months.", "I've been smoking a pack of cigarettes for the last 12 years.", "I also drink alcohol, I probably have 5 beers per day.", "I smoked a little bit of weed in the past.", "I don't exercise that much at all.", "My diet isn't that great.", "I often get dinner from the Italian place near work.", "I guess my stress levels are quite high, I'm pretty stretched with my life right now."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 11, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 1, "items": ["Whooping", "Asking Generally", "Productive Cough"]}, {"title": "Sputum Amount", "score": 1, "items": ["Asking Generally", "Cups or Teaspoons"]}, {"title": "Sputum Colour", "score": 1, "items": ["White", "Red or Pink", "Green, Yellow or Brown"]}, {"title": "Sputum Character", "score": 1, "items": ["Blood", "Asking Generally", "Wet, Thick or Sticky"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to Stop Coughing"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Smoking", "Vaccination", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Season or Temperature"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bone Pain", "score": 1, "items": ["Bone Pain"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Throat Irritation", "score": 1, "items": ["Throat Irritation"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Egg", "Dust", "Wheat", "<PERSON><PERSON>", "Animals", "Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Infectious Disease History", "score": 1, "items": ["HIV"]}, {"title": "Respiratory Disease History", "score": 2, "items": ["Asthma", "Influenza", "Pneumonia", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bronchiectasis", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "ACE Inhibitor", "Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Lung Cancer", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Emigration", "Been to Asia", "Been to China", "Recent Travel", "Been to Africa"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Infectious Contacts", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally", "<PERSON><PERSON><PERSON><PERSON>", "Contact with Covid-19 Cases"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 2, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Childcare Worker", "Work with Animals", "Healthcare Professional"]}, {"title": "Occupational Exposure", "score": 1, "items": ["Dust", "Animals", "Smoking", "Asbestos"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}