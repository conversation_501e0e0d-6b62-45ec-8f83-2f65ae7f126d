{"metadata": {"name": "<PERSON><PERSON>, 16F", "scraped_at": "2025-09-05T12:46:50.848671", "script_url": "https://www.oscer.ai/pwf/script/jIN7quMhswQo"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based in a general practice clinic. <PERSON><PERSON> is a 16-year-old woman presenting with polydipsia. Please take a history from <PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON>, I am 16 years old and my pronouns are she/her.", "I have come to the GP because I am very worried about the amount I have been going to the bathroom, it’s starting to get out of control."], "Persona": ["I am usually a very fit and healthy 16-year-old, I’ve never really had to see the doctor before.", "I am worried about the recent change in my urinary habits, I shouldn’t need to go to the bathroom this regularly.", "I am a quiet and reserved young woman."], "HOPC": ["I have noticed that I have been going to the bathroom much more often.", "This began suddenly about 3 days ago.", "I need to go and wee ten times a day, it is becoming very frustrating.", "Since it started nothing much has changed.", "This is the first time I have ever experienced something like this.", "I have been very thirsty as well.", "I can never quench my thirst, I am drinking much more water than I usually would.", "I feel like I am hungry all the time too.", "I am really craving sweet foods.", "I am feeling pretty fatigued at the moment too.", "My sleep has not been affected.", "I find I am unable to do the same amount of daily activities before because I just feel really worn out.", "I have never experienced this before either.", "The frequent trips to the bathroom and the fatigue are really starting to interrupt my day.", "The symptoms haven’t been helped by anything I have tried.", "Nothing seems to make anything worse either.", "I have not been unwell recently.", "I have never had a UTI.", "I haven't lost any weight or had any weakness or blurred vision.", "I haven't had any nausea, vomiting, or confusion.", "I haven't lost consciousness.", "I haven't traveled recently.", "My appetite is fine.", "I do not have a fever.", "I don't have any other urinary symptoms like urgency, haematuria, or dysuria.", "I have not noticed any skin changes or discharge around my vagina."], "PMHx": ["I don’t have any medical conditions.", "I have never had an operation or been hospitalised for anything.", "I don't take any prescription or over the counter medications.", "I am not allergic to anything.", "I am up to date with vaccines.", "My diet is usually pretty good. Mum makes sure I eat pretty healthily for the most part.", "I don't really do much exercise, I am far too busy with theatre."], "FMHx": ["My dad has diabetes, I'm not sure what type but he has had it since he was a kid and takes his medication with a needle.", "Everyone else in the family is healthy."], "SHx": ["I am a year 9 student, I love maths and French the most.", "I enjoy school, I don’t have heaps of friends but I have a few really good ones.", "I don’t play any sport, I act in the school theatre.", "I don’t have a boyfriend, I never have.", "I am not sexually active.", "I live at home with mum, dad, and my sister <PERSON><PERSON>, we all get along very well.", "We also have a cat called <PERSON><PERSON>.", "I don't smoke cigarettes, I have never smoked.", "I don't drink alcohol, I have never drunk alcohol.", "I have never tried recreational drugs.", "I feel very safe at home.", "I would describe my mental health as very good."]}, "marking_rubric": {"HOPC": [{"title": "Polydipsia", "score": 8, "subcategories": [{"title": "Severity", "score": 1, "subcategories": [{"title": "Quantitative", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Drinking", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Caffeine", "Patient Ideas", "Salt-Rich Diet", "Context at Onset", "Lifestyle Change", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Urinary Symptoms", "score": 2, "items": ["Cloudy Urine", "Frothy Urine", "Urinating Blood", "Asking Generally", "Painful Urination", "Urinating at Night", "Sweet-Smelling Urine"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Thirsty", "score": 1, "items": ["Thirsty", "Asking Generally"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Acetone Breath", "score": 1, "items": ["Acetone Breath"]}, {"title": "Vision Changes", "score": 1, "items": ["Double Vision", "Asking Generally", "Decreased Eye Movements"]}, {"title": "Appetite Change", "score": 1, "items": ["Asking Generally", "Increased Appetite"]}, {"title": "Breathing Deeply", "score": 1, "items": ["Breathing Deeply"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Asking Generally"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Irritability"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness"]}, {"title": "Endocrine Disease History", "score": 1, "items": ["Diabetes", "Diabetes Insipidus"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Thyroid Disease", "Asking Generally"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Urinary Tract Infection"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Lithium", "Diuretics", "Asking Generally"]}]}, {"title": "Menstrual History", "score": 1, "subcategories": [{"title": "Blood Amount", "score": 1, "items": ["Asking Generally"]}, {"title": "Menstrual Cycle", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Type 1 Diabetes", "Asking Generally", "Autoimmune Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Sugary Foods", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}