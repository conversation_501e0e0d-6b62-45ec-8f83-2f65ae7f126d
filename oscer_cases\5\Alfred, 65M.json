{"metadata": {"name": "Alfred, 65M", "scraped_at": "2025-09-03T00:28:18.223347", "script_url": "https://www.oscer.ai/pwf/script/9ARR7uWvgjde"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 65 year old male presenting to general practice complaining of blood in his urine. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["<PERSON> is a 65 year old male who presents with a 1 week history of ‘red coloured urine’."], "Persona": ["I am a mechanic who doesn’t usually go to the doctors and was reluctant to come in today.", "I am stoic but clearly quite concerned."], "HOPC": ["His urine has been a funny colour (darker than normal) for a little over a week now but he only began to worry about it as it became a deeper red colour 2 days ago.", "I have no other symptoms.", "I have no fever.", "I have no B symptoms or other constitutional symptoms.", "I have never had symptoms like this before.", "I have no pain.", "I have not noticed frothy urine or any clots or debris in my urine.", "I have not had any other urinary symptoms."], "PMHx": ["I have hypercholesterolemia. This is managed with a statin.", "I do not know what the dose is. I started it about 10 years ago.", "I do not have any other medical conditions.", "I have not had any surgeries in the past.", "I have never had kidney stones before.", "I have never had any urinary tract infections before."], "FMHx": ["My father died of a SCC lung cancer - he was a heavy smoker for most of his life.", "<PERSON> is still alive.", "I do not have any siblings.", "My two children, 16 and 18-years-old. They are both healthy."], "SHx": ["I have a wife and two children.", "My wife is a nurse. She was the one who nagged me to come in.", "I play squash twice per week. I am quite high up on the club ladder.", "Things are well at home.", "I work full-time as a mechanic at the moment.", "I worked in a textile factory for over 20 years before I trained as a mechanic. I was exposed to aniline dyes at this time.", "I have never smoked.", "I have 1-2 beers most days."]}, "marking_rubric": {"HOPC": [{"title": "Haematuria", "score": 11, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red or Pink", "Asking Generally", "Dark Red or Brown"]}, {"title": "Character", "score": 1, "items": ["Timing of Blood", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Blood"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Offset", "Duration", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Injuries", "Recent Illness", "Context at Onset"]}]}]}], "Assoc.": [{"title": "Urinary Symptoms", "score": 5, "items": ["Frothy Urine", "Urinary Urgency", "Painful Urination", "Urinating at Night", "Decreased Urination", "Urinary Incontinence", "Hesitancy, Intermittency or Dribbling"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Flank Pain", "score": 1, "items": ["Flank Pain"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urethral Discharge", "score": 1}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer", "Bowel Cancer", "Bladder Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Urinary Tract Infection"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Nephrolithiasis", "Asking Generally", "Urethral Stricture", "Benign Prostatic Hyperplasia"]}]}, {"title": "Medications", "score": 1}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Bladder Cancer", "Nephrolithiasis", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Red Meat-Rich Diet"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 2, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Previous Occupation", "Personal Protective Equipment Use"]}, {"title": "Occupational Exposure", "score": 1, "items": ["General <PERSON><PERSON>s", "Chemicals or Toxins"]}]}], "Basics": [{"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}