{"metadata": {"name": "<PERSON>, 68F", "scraped_at": "2025-09-03T00:31:11.122091", "script_url": "https://www.oscer.ai/pwf/script/7FkcXtp1Neww"}, "tabs": {"doctor_information": {"Intro": "You are a student in general practice, and you have been asked to see <PERSON>. <PERSON> is a 68 year old woman, presenting with chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 68 year old woman. My pronouns are she/her.", "I work as a design consultant.", "I’ve come in to see the doctor today because I’ve been getting some chest pain and it is starting to worry me."], "Persona": ["I am a very driven woman, who likes to get the job done.", "I don’t take particularly good care of my health, but I wouldn’t say I am super unhealthy either.", "I am concerned that the chest pain is something bad."], "HOPC": ["I have been experiencing this chest pain for about 6 months. It seems to be getting worse.", "The chest pain is central. It radiates to my arm and jaw when it comes on.", "The chest pain comes on fairly suddenly.", "I only notice the chest pain when I am exerting myself, such as walking up a hill. Currently, even walking along the flat is causing the chest pain.", "Only exertion seems to exacerbate the chest pain.", "The chest pain goes away when I rest. Nothing else seems to help with the chest pain.", "I can’t think of anything that would have caused this chest pain. Nothing has changed in my life that would have caused this.", "I have also been experiencing some shortness of breath. I notice the shortness of breath when I am exerting myself.", "The shortness of breath happens at the same time as the chest pain. It comes on suddenly.", "The shortness of breath started at the same time as the chest pain, and has also been getting worse.", "Exertion makes the shortness of breath worse.", "Resting makes the shortness of breath go away.", "I have also been noticing some dizziness. Sometimes it feels like I am about to pass out.", "The dizziness comes on when I exert myself. It happens at the same time as the chest pain and shortness of breath."], "PMHx": ["I’m usually pretty healthy, I have no underlying medical conditions.", "I have never had any surgery or been hospitalised for anything.", "I don’t take any regular medications.", "I occasionally take Panadol for headaches.", "I don’t have any allergies that I’m aware of.", "All of my vaccinations are up to date, even my covid ones.", "I eat a relatively balanced diet.", "I don’t do much exercise. I feel too tired after work to do exercise.", "I wouldn’t say that I’m overweight."], "FMHx": ["My father had aortic stenosis and required open heart surgery to fix it.", "There are no other medical conditions that run in my family."], "SHx": ["I work as a design consultant. My work is very busy.", "I live with my partner, <PERSON>. We have been together for 5 years now.", "I don’t have any pets.", "I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.", "I occasionally drink a glass of wine socially.", "I do not take any recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Most Recent Episode", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Resting", "Context at Onset", "Physical Activity", "Emotional or Stress", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Legs"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Anxiousness", "Irritability", "Concentration Difficulty"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["ICU admission", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Mental Health History", "score": 1, "items": ["Asking Generally", "Depression or Low Mood"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 2, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Vascular Disease", "Valvular Heart Disease", "Coronary Artery Disease", "Hypertrophic Cardiomyopathy"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Beta Antagonists", "Past Medications", "Antihypertensives", "Combination Inhaler"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Anxiety", "Suicide", "Asking Generally", "Coronary Artery Disease", "Mental Health Disorders", "Hypertrophic Cardiomyopathy"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Vegan or Vegetarian", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Financial Stability", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion", "Patient Questions"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}