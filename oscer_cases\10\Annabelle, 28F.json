{"metadata": {"name": "<PERSON>, 28F", "scraped_at": "2025-09-05T12:49:25.682396", "script_url": "https://www.oscer.ai/pwf/script/cjkmCVjARbyp"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement at the local GP clinic. Your next patient is <PERSON>, a 28 year old female (she/her) who presents with a rash. Please take a focused history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 28 year old female (she/her).", "I have come in to see the GP today due to this weird rash that I've noticed on arms."], "Persona": ["I am quite a reserved person and don't like to actively seek medical care, especially when it is something as minor as a rash.", "I came in at the insistance of my father who noticed the rash and wanted me to get it checked out.", "Overall, I am quite an introverted person."], "HOPC": ["I've noticed this rash particularly around my elbow on and off over the last few years, but I've never sought medical advice or care for it.", "It seems to just be confined to the back of my elbow and can extend down the back of my forearm a little bit too (extensor surface). I haven't really noticed it anywhere else on my body.", "This most recent rash I've had for about 2-3 weeks now.", "About 3 or 4 years ago I first noticed this weird rash on my elbows. It would come and go so I never really got super concerned as I thought I might just be reacting to something I brushed past.", "The rash is composed of these discrete red, scaly plaques over the surface of the skin.", "I do notice that if I accidentally scratch it on something, I often get a flare of the rash soon after.", "These plaques can be very itchy when they come on and I really can't help but scratch them! However, it is quite painful when I do.", "The rash is quite episodic, it seems to last for a few weeks at a time and then dissapear for a while before returning again."], "PMHx": ["Aside from the rash, I have never really had any major health problems.", "I have never been diagnosed with any medical conditions in the past.", "I was hospitalised when I was 13 years old with a badly broken arm from trampolining. My parents were very unhappy with me!", "I have never had surgery fortunately, the idea of being put under anaesthesia quite frightens me.", "I don't take any medication regularly other than the oral contraceptive pill.", "I don't have any allergies that I am aware of and I am up to date with all my immunisations, including the COVID-19 vaccine."], "FMHx": ["My father is 57 years old and doing well. He is known to have psoriasis of his knee which was diagnosed by doctors when he was in his 20's.", "My mother is 55 years old and is also quite well. She has been told that she has high cholestrol but she keeps it well under control with medication the doctors prescribe.", "I have a brother who is 26 years old and doesn't have any medical conditions that I am aware of."], "SHx": ["I live in an apartment in the city with my long-term boyfriend.", "I work in human resources for a large corporation and am tasked with talent acquisition.", "For my age, I feel quite financially secure and don't spend beyond my means.", "To get my exercise, I go for runs about 3-4 times a week, usually of about 8km in distance. I also go for morning and evening walks most days.", "I try to eat a well-balanced diet of largely vegetables and protein, which is normally either fish or chicken.", "I drink alcohol socially on the weekends. This normally consists of a few cocktails on Friday night and maybe 1-2 wines over the remaining weekend.", "I have never smoked or tried recreational drugs before.", "I last travelled to Italy a few years ago, but I haven't been anywhere since."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 14, "subcategories": [{"title": "Quality", "score": 5, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Pink", "Black", "<PERSON>", "Colour"]}, {"title": "Character", "score": 4, "items": ["Hot", "Flat", "Itch", "Pain", "Sc<PERSON>", "<PERSON><PERSON><PERSON>", "Raised", "Bleeding", "Palpable", "Discharge", "Irregular", "Blistering", "Symmetrical", "Asymmetrical", "Asking Generally", "Well-circumscribed"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Asking Generally", "Location at Onset"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Number of Rashes"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 2, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Topical Creams", "Asking Generally", "Topical Steroids"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Medications", "Sun exposure", "Cold Exposure", "Heat Exposure", "Skin Products", "Topical Creams", "Context at Onset", "Occupational Exposure"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Arm Pain", "score": 1, "items": ["Arm Pain"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Nail Changes", "score": 1, "items": ["White", "Brittle", "Asking Generally"]}, {"title": "Skin Lesions", "score": 1, "items": ["Face Lesions", "Mouth Lesions"]}, {"title": "Skin Infections", "score": 1, "items": ["Skin Infections"]}, {"title": "<PERSON><PERSON><PERSON>'s phenomenon", "score": 1, "items": ["<PERSON><PERSON><PERSON>'s phenomenon"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Duration", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Hayfever", "Asking Generally"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally", "Autoimmune Disease", "Dermatological Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": []}}}