{"metadata": {"name": "<PERSON>, 66F", "scraped_at": "2025-09-03T00:42:44.267296", "script_url": "https://www.oscer.ai/pwf/script/TQVQW1IuPXkd"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON> a 66 year old female retired nurse. She has presented to the emergency department complaining of chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I’m <PERSON>. I’m a 66 year old retired nurse. I came to the clinic today because I’ve been having some chest pain."], "Persona": ["I am an energetic person and kind whenever I can be.", "I’ve never had chest pain like this. I’m really worried that it’s something serious."], "HOPC": ["I’ve had sharp chest pain on my left side for the past 3 days.", "The chest pain doesn’t move anywhere.", "The pain isn’t too severe but it is uncomfortable.", "The chest pain is worsened when I take deep breaths.", "The chest pain has been quite constant since it started.", "I also have had a painful cough since the chest pain came on.", "With the cough, I bring up a teaspoon of thick yellow-greenish phlegm. I haven’t seen any blood in my phlegm.", "I've also been feeling short of breath. It gets worse when I go up the stairs or try to do too much.", "I think I might be having a fever as well.", "I also have less appetite than I usually do.", "I don’t feel overly fatigued.", "I haven’t had any weight loss or night sweats.", "I haven’t been immobile for extended periods.", "My grandchild was sick when he visited me a week ago."], "PMHx": ["I don't take any regular prescription medication.", "I take a multi-vitamin every morning.", "My gallbladder was removed a few years ago with no complications.", "My lung function was checked a month ago and everything seemed fine.", "I do not take any prescription medications."], "FMHx": ["My father had some bowel problems in his 60s which he survived through. Unfortunately, a few years ago he got colon cancer and passed away. He was 80 at the time.", "My mum is fine. She has gout."], "SHx": ["I am no longer working. I have just recently retired after a long career as a midwife.", "I’ve been smoking half a pack for 40 years.", "I usually have a glass or two of red wine with my friends on the weekend.", "I live with my husband <PERSON>. I am happy at home.", "I tend to eat a healthy nutrient-rich diet.", "I haven’t traveled recently."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 15, "subcategories": [{"title": "Quality", "score": 4, "subcategories": [{"title": "Character", "score": 4, "items": ["De<PERSON><PERSON>", "<PERSON>", "Burning", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Requiring Hospitalisation", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 2, "subcategories": [{"title": "Time Course", "score": 2, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Ideas", "Resting", "Injuries", "Palpating", "Night-time", "Context at Onset", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 4, "subcategories": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Malaise", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 2, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1}, {"title": "Vaccinations", "score": 1}, {"title": "Past Medical History (2)", "score": 2, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}], "FMHx": [{"title": "Family History", "score": 2, "subcategories": [{"title": "Family History", "score": 2, "items": ["Asthma", "Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Respiratory Disease", "Deep Vein Thrombosis"]}]}, {"title": "Family History (2)", "score": 2, "items": ["Asthma", "Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Respiratory Disease", "Deep Vein Thrombosis"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}