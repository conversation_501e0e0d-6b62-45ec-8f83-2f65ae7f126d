{"metadata": {"name": "<PERSON>, 32F", "scraped_at": "2025-09-03T00:43:56.105404", "script_url": "https://www.oscer.ai/pwf/script/4rCMgiRGLhJZ"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 32 year old female presenting to the emergency department with complaints of chest pain. You are the first doctor to see <PERSON>. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 32 year old woman and I work as a film director. I am currently working on a new production that requires lots of travel.", "I’ve come to the doctor today because I’ve got excruciating chest pain, and I can’t catch my breath."], "Persona": ["I’m starting to worry that it might be something more serious.", "Nothing is making the chest pain or shortness of breath better!", "I just want to be able to breathe again!"], "HOPC": ["I’m currently having excruciating chest pain.", "The chest pain came on very suddenly, only 20 minutes ago.", "I was walking home from dinner with friends when it started.", "The chest pain is sharp, like a burning sensation.", "I’ve never experienced pain like this before, I’d rate it a 10 out of 10.", "The chest pain is more on the left side of my chest.", "The chest pain doesn’t radiate anywhere else.", "The chest pain is constant, and nothing makes it better.", "The chest pain is worse when I try to take a deep breath, and when I cough or sneeze it’s excruciating.", "I have difficulty breathing and I am feeling very short of breath.", "The shortness of breath also came on 20 minutes ago when I was walking home.", "I’d rate the shortness of breath as a 9 out of 10.", "The shortness of breath is getting worse.", "I have some pain in my right calf and it feels like I have strained it.", "I was happy and relaxed when the chest pain started.", "I don’t have a fever.", "I don’t have a cough."], "PMHx": ["I’ve never had any medical conditions, this is my first time in hospital.", "I've been taking the oral contraceptive pill for the past 10 years.", "I occasionally take paracetamol when I need it.", "I don’t take any alternative medicines or supplements.", "I don’t have any allergies.", "I’m up to date with all of my vaccinations."], "FMHx": ["I think Dad has had a DVT, but I’m not 100% certain.", "I can’t think of anything else major in my family."], "SHx": ["I went to London about 1 week ago for work.", "I eat a pretty healthy diet.", "I normally exercise every day, but I can barely move right now.", "I have a great partner who I’m engaged to.", "I like to drink the occasional wine, which is maybe a bottle of wine per week.", "I am a social smoker, I occasionally have 1 or 2 cigarettes on the weekends.", "I have never used any recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["De<PERSON><PERSON>", "<PERSON>", "Burning", "Ripping", "Cramping", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 3, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 2, "subcategories": [{"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 3, "items": ["Resting", "Positional", "Medications"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Ideas", "Resting", "Injuries", "Palpating", "Night-time", "Eating or Diet", "Context at Onset", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 2, "items": ["Duration", "Asking Generally", "Past Medications", "Oestrogen Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Clotting Disorders", "Pulmonary Embolism", "Deep Vein Thrombosis"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Heart Disease", "Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Deep Vein Thrombosis", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}