{"metadata": {"name": "Ray, 21M", "scraped_at": "2025-09-05T12:11:20.457737", "script_url": "https://www.oscer.ai/pwf/script/t0A6VuESCMIS"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 21-year-old man presenting to ED with J<PERSON>ndice. You are a medical student working in a busy ED, please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am 21 years old and my pronouns are he/him.", "I have come to the ED because I am worried about some yellowing of my skin and eyes that I have noticed."], "Persona": ["I am a very fit and well 21-year-old, I can't remember the last time I needed to come to the doctor.", "I am a little bit confused about the change in colour of my skin and eyes, it's very strange."], "HOPC": ["I woke up yesterday morning and noticed that my skin was a bit yellow in the mirror.", "Come to think of it, I think the whites of my eyes are a bit yellow too.", "I don't think the discolouration has gotten any worse since yesterday.", "This has happened to me once before about a week ago after an all-night shift at work, it only lasted one day though.", "I have been feeling generally unwell.", "I feel tired all the time, I'm not sleeping well because of all the extra hours at work.", "I'm not actually sick, just exhausted I think.", "I don't have any abdominal pain.", "My appetite is fine.", "I do not have a fever.", "I have not lost any weight recently.", "I have not recently travelled.", "I have not recently eaten any contaminated food.", "I have had no changes to my bowel habits.", "I have had no changes to my urination."], "PMHx": ["I do not have any ongoing medical issues.", "I have never had any surgery.", "I take no medications.", "I take a multivitamin every morning, not for any reason, just a habit I got into.", "I am allergic to codeine; it makes me feel sick.", "I am up to date with all my vaccinations."], "FMHx": ["My parents are both pretty healthy, I think my dad mentioned that he sometimes gets some yellow skin too, the doctor wasn't worried though."], "SHx": ["I am an architecture intern.", "Things at work have been really stressful, we have a big brief due so I have been doing 14-hour days for a week straight now.", "I have also had to do some shifts where I worked all the way through the night.", "My sleep habits are terrible at the moment, I'd be lucky to be getting four hours a night.", "I usually exercise a lot, but I have been so tired recently I have had to stop.", "I eat really well, I have still managed to meal prep at the start of each week.", "I live at home by myself in a one bedroom apartment.", "I do not smoke cigarettes.", "I like a drink.", "I drink beer mostly.", "I usually only drink on weekends, I haven't had a drink for two weeks because work has been so busy.", "I smoke a bit of weed occasionally.", "I have never injected any drugs."]}, "marking_rubric": {"HOPC": [{"title": "Jaundice", "score": 8, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Stress", "IV Drug Use", "Diet or Eating", "Recent Illness", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["<PERSON>le Stools", "<PERSON><PERSON>ools", "Asking Generally"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Antiepileptics", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Asking Generally", "Gilbert's Syndrome", "Alcoholic Liver Disease"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Cholecystectomy", "Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Cholecystectomy", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Hepatitis", "Thalassaemia", "Liver Disease", "Asking Generally", "Haemoglobinopathy", "Gilbert's Syndrome", "Sickle Cell Anaemia"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Amount Currently"]}, {"title": "Patient Demographics", "score": 1, "items": ["Ethnicity"]}]}, {"title": "Detailed Sexual History", "score": 2, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexual Partners", "Sexually Active", "Sexual Practices", "Frequency of Unprotected Sex"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Ability to Work", "Workload Current", "Workload Baseline"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}