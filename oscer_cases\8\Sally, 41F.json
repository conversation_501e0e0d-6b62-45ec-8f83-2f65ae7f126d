{"metadata": {"name": "<PERSON>, 41F", "scraped_at": "2025-09-05T12:09:19.669008", "script_url": "https://www.oscer.ai/pwf/script/ImGvwAJEIIeb"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 41 year old woman presenting to the ED with yellow skin. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am 41 years old and my pronouns are she/her.", "I have come to ED because I am very worried about the change in colour to my skin, and the whites of my eyes - they're turning yellow!"], "Persona": ["I am usually pretty well and don't seem to get sick too often.", "I am worried about the change in colour to my skin and eyes, it can't be good news!"], "HOPC": ["I noticed that my skin and eyes were slightly yellowed one day ago after my husband pointed it out.", "My skin and eyes have not returned back to normal colour since this began.", "The yellow colour has been becoming more pronounced since it began.", "I have never had changes to my skin or eye colour before.", "I have felt nauseous for the last 12 hours.", "I have vomited 4 times in the last 12 hours.", "There is no blood in my vomit.", "I have some pain in my abdomen.", "The pain is in the upper middle part of my abdomen.", "The abdominal pain began 8 hours ago.", "The abdominal pain is a dull, boring pain.", "The abdominal pain is increasing in intensity.", "The abdominal pain radiates to my back.", "Lying down makes the abdominal pain worse.", "Sitting forward improves the abdominal pain.", "The abdominal pain was a 5 out of 10 when it began. The abdominal pain is an 8 out of 10 now.", "I have had similar abdominal pain in the past after eating a fatty meal.", "In the past the pain has only lasted 15 minutes, and is located more in the top right part of my tummy.", "My appetite has been decreased for 12 hours.", "I do not have a fever.", "I have not lost any weight recently.", "I have not travelled recently.", "I have had no changes to my bowel or urinary habits."], "PMHx": ["I do not have any ongoing medical issues.", "I have had my appendix removed as a child.", "I take no mediations, I take no over the counter medications.", "I am allergic to penicillin, it gives me a rash.", "I am up to date with all my vaccinations, I receive my flu shot every year.", "I have never had an ultrasound of my gall bladder."], "FMHx": ["My mother has hypertension which is well controlled with medication and my father has high cholesterol and diabetes which he takes some tablets for.", "My children are both fit and well."], "SHx": ["I work as a lawyer.", "I am sitting quite a lot at work and do a lot of late nights.", "I don't manage to do too much exercise.", "Because of the long work hours, I tend to eat a lot of fast foods.", "I do not smoke cigarettes, I have never smoked cigarettes.", "I do not drink alcohol, I have never drunk alcohol.", "I have never used recreational drugs.", "I am a proud Aboriginal woman from Wurundjeri country.", "I am a little bit stressed at work but feel on top of things.", "My sleep habits are good.", "I have two children a 9 year old girl named <PERSON> and a 7 year old boy named <PERSON>."]}, "marking_rubric": {"HOPC": [{"title": "Jaundice", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Diet or Eating", "Recent Illness", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bruising", "score": 1, "items": ["Bruising", "<PERSON>'s Sign", "Grey-<PERSON>'s Sign"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Shoulder Pain", "score": 1, "items": ["Shoulder Pain"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain", "Radiation to Back"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["<PERSON>le Stools", "Constipation", "Asking Generally"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes", "Hypercalcaemia"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Hepatitis", "Gallstones", "Liver Cancer", "Liver Failure", "Asking Generally", "Pancreatic Cancer", "Chronic Liver Disease", "Primary Sclerosing Cholangitis"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Pancreatitis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Hepatitis", "Gallstones", "Pancreatitis", "Liver Disease", "Asking Generally", "Pancreatic Cancer"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Streetfood", "Fatty Foods", "Asking Generally", "Contaminated Food or Water"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Detailed Sexual History", "score": 2, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexual Partners", "Sexual Practices"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}