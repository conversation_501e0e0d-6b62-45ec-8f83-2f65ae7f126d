{"metadata": {"name": "<PERSON><PERSON>, 18F", "scraped_at": "2025-09-05T12:19:41.921766", "script_url": "https://www.oscer.ai/pwf/script/nHMbk3FY33tF"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement at a GP clinic. <PERSON><PERSON> is an 18 year old female (she/her) who has come into the GP clinic presenting with hoarseness. Please take a history from <PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON> and I'm 18 years old.", "My pronouns are she/her.", "I've come into to see the <PERSON> today because I've been unwell for a few days, I think I might need antibiotics."], "Persona": ["I'm concerned about how long I've been sick for without improving, that's why I think it's time for some antibiotics to help improve things.", "I'm a relaxed and outgoing person that doesn't get stressed very easily."], "HOPC": ["You can probably hear it in my voice, but it has become totally hoarse for the last 3 days.", "I've been feeling unwell since last week, I think there's something going around because a lot of my school friends have been off sick.", "Last week I started sniffling all the time with a runny nose.", "I was also feeling really off with some body aches and pains, but that has improved now.", "I've got this dry cough happening as well that I can't seem to shake.", "I'm not bringing up any gunk with my cough.", "I'd describe the cough as almost like a dog barking.", "I've got a totally sore throat too, swallowing is horrible at the moment. It's like I've got razorblades in there.", "The sore throat started last week when I had the runny nose.", "I've definitely felt off, but I don't think I've been feverish at all.", "I'm not short of breath or anything.", "I don't have any lumps around my neck, my Mum checked for me at home.", "I don't have any chest pain, or pains when I take a deep breath in.", "I haven't had any trouble eating.", "I don't have any ear pain at all.", "I haven't had any loss of smell.", "I haven't had any loss of taste.", "I don't have a history of any mental health disorders."], "PMHx": ["I don't have any significant medical problems.", "I have never had any surgery or been hospitalised for anything.", "The only regular medication I take is the oral contraceptive pill.", "I don't take anything over the counter or any supplements.", "I don't have any allergies and I'm not anaphylactic to anything.", "My vaccines are all up to date, we have to get them through school. We all got our flu vaccines a couple of weeks ago.", "My diet is fine, I still live with my parents so I just eat whatever my Mum cooks. It's always healthy, she's got a bit of an obsession for having enough vegetables and protein.", "I play netball for a club locally, so I'm pretty active with that because of the games and all of the trainings."], "FMHx": ["There are no medical conditions that run in my family.", "Mum has had osteomyletis before, and Dad had a hernia on his belly button repaired but that's it.", "My two younger siblings are totally healthy as well."], "SHx": ["I live with my parents and feel very safe at home.", "I still go to school, I'm in my final year.", "I have a part time job at a restaurant, I usually do one night during the week or a weekend shift.", "I've never tried smoking before, I don't think my parents would be very happy with me if I became a smoker.", "I do drink alcohol.", "I have 3 to 4 drinks on a Saturday night, there's usually a 18th birthday party happening every weekend we head to.", "I drink pre-mixed drinks. I find beer disgusting and wine is too sour for me.", "I don't use any recreational drugs."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Weak Voice", "Low-Pitched", "Voice Volume", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Speak", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Tonsil <PERSON>", "score": 1, "items": ["Tonsil <PERSON>"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Swollen Tonsils", "score": 1, "items": ["Swollen Tonsils"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Regularity", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Duration", "Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Immunodeficiency History", "score": 1, "items": ["Immunocompromised"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Pneumonia", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Monospot", "EBV Serology", "Covid-19 Test"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Tonsillitis", "<PERSON><PERSON>", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}