{"cells": [{"cell_type": "markdown", "id": "bb662325-50e1-4da2-84d4-dc3850e07704", "metadata": {}, "source": ["# Oscer Medical Cases Scraper"]}, {"cell_type": "markdown", "id": "2a1a7877-cb4a-46bf-baf8-a8173098fb32", "metadata": {}, "source": ["## Open signin page from homepage"]}, {"cell_type": "code", "execution_count": 1, "id": "2d4c4941-2a0f-4774-a53b-03a74e90aa1b", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "import time\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "# Start the browser\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "    time.sleep(3)  # wait for page load (better to use WebDriverWait in production)\n", "\n", "    # Find and click the Sign In button\n", "    sign_in_button = driver.find_element(By.LINK_TEXT, \"Sign In\")\n", "    sign_in_button.click()\n", "\n", "    # Wait to see the login page\n", "    time.sleep(5)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "e3835d1b-cf43-4ba5-8dc5-64035563cd39", "metadata": {}, "source": ["## Dump Signin page contents"]}, {"cell_type": "code", "execution_count": 2, "id": "cedbf91e-49ad-488f-ab4e-527270feae2f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<html lang=\"en\" class=\"focus-outline-hidden\"><head><script type=\"text/javascript\" async=\"\" src=\"https://widget.intercom.io/widget/xyqioc2g\"></script><script class=\"w-json-ld\" type=\"application/ld+json\" id=\"w-json-ldwistia_68\">{\"@context\":\"http://schema.org/\",\"@id\":\"https://fast.wistia.net/embed/iframe/jrmogk8sl9\",\"@type\":\"VideoObject\",\"duration\":\"PT35S\",\"name\":\"Oscer Inital Explainer\",\"thumbnailUrl\":\"https://embed-ssl.wistia.com/deliveries/fac3c5499ed1562493b762e23e8fdef0.jpg?image_crop_resized=640x360\",\"embedUrl\":\"https://fast.wistia.net/embed/iframe/jrmogk8sl9?wseektoaction=true\",\"uploadDate\":\"2020-09-15T22:38:03.000Z\",\"description\":\"an Explainer Videos video\",\"contentUrl\":\"https://embed-ssl.wistia.com/deliveries/65a137c6e5d700f202a13843403d85817d0c4791.m3u8\",\"potentialAction\":{\"@type\":\"SeekToAction\",\"target\":\"https://www.oscer.ai/?wtime={seek_to_second_number}\",\"startOffset-input\":\"required name=seek_to_second_number\"}}</script><base href=\"/\"><meta charset=\"utf-8\"><link rel=\"shortcut icon\" href=\"/icons/oscer-favicon.ico\"><link rel=\"apple-touch-icon\" href=\"/icons/logo-icon-mobile-shortcut.png\"><meta name=\"viewport\" content=\"width=device-width,initial-scale=1\"><meta name=\"theme-color\" content=\"#000000\"><link rel=\"preload\" href=\"/static/media/slick.b7c9e1e4.woff\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"https://fonts.gstatic.com\"><link href=\"https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;700;900&amp;display=swap\" rel=\"stylesheet\"><link rel=\"stylesheet\" as=\"style\" href=\"/custom.css\" onload=\"this.rel=&quot;stylesheet&quot;\"><link href=\"https://fonts.googleapis.com/css?family=Roboto&amp;display=swap\" rel=\"stylesheet\"><link rel=\"manifest\" href=\"/manifest.json\"><link rel=\"stylesheet\" href=\"/custom.css\"><title>Oscer | Learn Clinical Reasoning - Free Sign Up</title><meta name=\"description\" content=\"Build clinical skills in diagnosis, history and examination with virtual patients powered by AI. Prepare for your OSCE and USMLE Step 2 CS with fun and challenging interactive cases.\"><meta name=\"keywords\" content=\"oscer oske\"><meta property=\"og:locale\" content=\"en_US\"><meta property=\"og:type\" content=\"website\"><meta property=\"og:title\" content=\"Oscer | Learn Clinical Reasoning - Free Sign Up\"><meta property=\"og:description\" content=\"Build clinical skills in diagnosis, history and examination with virtual patients powered by AI. Prepare for your OSCE and USMLE Step 2 CS with fun and challenging interactive cases.\"><meta property=\"og:url\" content=\"https://oscer.ai/\"><meta property=\"og:site_name\" content=\"Oscer\"><meta property=\"og:image:type\" content=\"image/jpeg\"><meta property=\"og:image\" content=\"/images/oscer-feature-image.jpg\"><meta name=\"twitter:card\" content=\"summary_large_image\"><meta name=\"twitter:title\" content=\"Oscer | Learn Clinical Reasoning - Free Sign Up\"><meta name=\"twitter:image\" content=\"https://www.oscer.ai/images/oscer-feature-image.jpg\"><meta name=\"twitter:description\" content=\"Build clinical skills in diagnosis, history and examination with virtual patients powered by AI. Prepare for your OSCE and USMLE Step 2 CS with fun and challenging interactive cases.\"><meta name=\"facebook-domain-verification\" content=\"dloo2r4y2zwswj7thb8sxd8byr4zf9\"><link rel=\"canonical\" href=\"https://oscer.ai/\"><script type=\"text/javascript\" async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=AW-428250314&amp;cx=c&amp;gtm=4e58r1\"></script><script type=\"text/javascript\" async=\"\" src=\"https://www.google-analytics.com/analytics.js\"></script><script type=\"text/javascript\" async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=G-6QFR5Y7TLF&amp;cx=c&amp;gtm=4e58r1\"></script><script src=\"https://connect.facebook.net/signals/config/1630750960310529?v=2.9.227&amp;r=stable&amp;domain=www.oscer.ai&amp;hme=98a01a771f1571b63142a5ab6b1965d297e9ee4aa2fec3ece59f72d8c5b28e26&amp;ex_m=86%2C148%2C128%2C19%2C121%2C60%2C41%2C122%2C67%2C59%2C135%2C75%2C13%2C85%2C27%2C116%2C107%2C65%2C68%2C115%2C132%2C94%2C137%2C7%2C3%2C4%2C6%2C5%2C2%2C76%2C84%2C138%2C212%2C160%2C54%2C217%2C214%2C215%2C47%2C175%2C26%2C64%2C221%2C220%2C163%2C29%2C53%2C8%2C56%2C80%2C81%2C82%2C87%2C111%2C28%2C25%2C114%2C110%2C109%2C129%2C66%2C131%2C130%2C43%2C112%2C52%2C104%2C12%2C134%2C38%2C203%2C205%2C170%2C22%2C23%2C24%2C16%2C17%2C37%2C34%2C35%2C71%2C77%2C79%2C92%2C120%2C123%2C39%2C93%2C20%2C18%2C98%2C61%2C32%2C125%2C124%2C126%2C117%2C21%2C31%2C51%2C91%2C133%2C62%2C15%2C30%2C185%2C156%2C262%2C201%2C146%2C188%2C181%2C89%2C113%2C70%2C102%2C46%2C40%2C100%2C101%2C106%2C50%2C14%2C108%2C99%2C57%2C42%2C95%2C45%2C48%2C0%2C83%2C136%2C1%2C105%2C11%2C103%2C9%2C49%2C78%2C55%2C127%2C58%2C97%2C74%2C73%2C44%2C118%2C72%2C69%2C63%2C96%2C88%2C36%2C119%2C33%2C90%2C10%2C139\" async=\"\"></script><script async=\"\" src=\"https://connect.facebook.net/en_US/fbevents.js\"></script><script src=\"https://connect.facebook.net/en_US/sdk.js?hash=********************************\" async=\"\" crossorigin=\"anonymous\"></script><script async=\"\" src=\"https://www.googletagmanager.com/gtm.js?id=GTM-WDV8FQD\"></script><script async=\"\" src=\"https://www.googletagmanager.com/gtag/js?id=UA-122458144-1\"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag(\"js\",new Date),gtag(\"config\",\"UA-122458144-1\",{send_page_view:!1}),gtag(\"config\",\"AW-428250314\",{send_page_view:!1})</script><script>!function(){var t=window,e=t.Intercom;if(\"function\"==typeof e)e(\"reattach_activator\"),e(\"update\",t.intercomSettings);else{var n=document,a=function(){a.c(arguments)};a.q=[],a.c=function(t){a.q.push(t)},t.Intercom=a;var c=function(){var t=n.createElement(\"script\");t.type=\"text/javascript\",t.async=!0,t.src=\"https://widget.intercom.io/widget/xyqioc2g\";var e=n.getElementsByTagName(\"script\")[0];e.parentNode.insertBefore(t,e)};t.attachEvent?t.attachEvent(\"onload\",c):t.addEventListener(\"load\",c,!1)}}()</script><script>!function(e,t,a,n,g){e[n]=e[n]||[],e[n].push({\"gtm.start\":(new Date).getTime(),event:\"gtm.js\"});var m=t.getElementsByTagName(a)[0],r=t.createElement(a);r.async=!0,r.src=\"https://www.googletagmanager.com/gtm.js?id=GTM-WDV8FQD\",m.parentNode.insertBefore(r,m)}(window,document,\"script\",\"dataLayer\")</script><script type=\"text/javascript\" id=\"hs-script-loader\" async=\"\" defer=\"defer\" src=\"//js-na1.hs-scripts.com/19624555.js\"></script><link href=\"/static/css/9.f71587e1.chunk.css\" rel=\"stylesheet\"><link href=\"/static/css/main.814b4919.chunk.css\" rel=\"stylesheet\"><script type=\"text/javascript\" src=\"//ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js\"></script><script type=\"text/javascript\" async=\"\" src=\"https://collector.leaddyno.com/visit?url=https%3A%2F%2Fwww.oscer.ai%2F&amp;referrer=&amp;agent=Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F139.0.0.0%20Safari%2F537.36&amp;js=true&amp;ld_t=2a3f0518-44da-4cd9-bca8-1956d3017025&amp;ld_ext=&amp;ld_fp=3574394603&amp;channel=&amp;purchase_code=&amp;key=17fc4507a19d14c59e69da73e1da7fc947bae41e&amp;callback=__LDCB_1756475115313_740254\"></script><script type=\"text/javascript\" async=\"\" src=\"https://collector.leaddyno.com/x?key=17fc4507a19d14c59e69da73e1da7fc947bae41e&amp;ld_fp=3574394603&amp;ld_t=2a3f0518-44da-4cd9-bca8-1956d3017025&amp;ts=1756475115314\"></script><script type=\"text/javascript\" async=\"\" src=\"https://collector.leaddyno.com/clickstream?url=https%3A%2F%2Fwww.oscer.ai%2F&amp;referrer=&amp;agent=Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F139.0.0.0%20Safari%2F537.36&amp;js=true&amp;ld_t=2a3f0518-44da-4cd9-bca8-1956d3017025&amp;ld_ext=&amp;ld_fp=3574394603&amp;channel=&amp;purchase_code=&amp;key=17fc4507a19d14c59e69da73e1da7fc947bae41e&amp;callback=__LDCB_1756475115314_906701&amp;page_url=https%3A%2F%2Fwww.oscer.ai%2F&amp;page_referrer=\"></script><style data-styled=\"\" data-styled-version=\"4.4.1\"></style><style data-jss=\"\" data-meta=\"MuiTypography\">\n", ".MuiTypography-root {\n", "  margin: 0;\n", "}\n", ".MuiTypography-body2 {\n", "  font-size: 0.875rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.43;\n", "  letter-spacing: 0.01071em;\n", "}\n", ".MuiTypography-body1 {\n", "  font-size: 1rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.5;\n", "  letter-spacing: 0.00938em;\n", "}\n", ".MuiTypography-caption {\n", "  font-size: 0.75rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.66;\n", "  letter-spacing: 0.03333em;\n", "}\n", ".MuiTypography-button {\n", "  font-size: 16px;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 600;\n", "  line-height: 1.75;\n", "  letter-spacing: 0.02857em;\n", "  text-transform: uppercase;\n", "}\n", ".MuiTypography-h1 {\n", "  color: #002348;\n", "  font-size: 2.25rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: normal;\n", "  line-height: 1.11;\n", "  letter-spacing: normal;\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h1 {\n", "    font-size: 2.9279rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h1 {\n", "    font-size: 3.1532rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h1 {\n", "    font-size: 3.6036rem;\n", "  }\n", "}\n", ".MuiTypography-h2 {\n", "  color: #002348;\n", "  font-size: 1.875rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: normal;\n", "  line-height: 1.2;\n", "  letter-spacing: -0.00833em;\n", "}\n", "@media (max-width:599.95px) {\n", "  .MuiTypography-h2 {\n", "    font-size: 25px;\n", "  }\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h2 {\n", "    font-size: 2.2917rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h2 {\n", "    font-size: 2.5rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h2 {\n", "    font-size: 2.7083rem;\n", "  }\n", "}\n", ".MuiTypography-h3 {\n", "  color: #002348;\n", "  font-size: 1.4375rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.2;\n", "  letter-spacing: 0em;\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h3 {\n", "    font-size: 1.6667rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h3 {\n", "    font-size: 1.6667rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h3 {\n", "    font-size: 1.875rem;\n", "  }\n", "}\n", ".MuiTypography-h4 {\n", "  font-size: 1.5625rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.235;\n", "  letter-spacing: 0.00735em;\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h4 {\n", "    font-size: 1.8219rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h4 {\n", "    font-size: 2.0243rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h4 {\n", "    font-size: 2.0243rem;\n", "  }\n", "}\n", ".MuiTypography-h5 {\n", "  font-size: 1.25rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.334;\n", "  letter-spacing: 0em;\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h5 {\n", "    font-size: 1.3118rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h5 {\n", "    font-size: 1.4993rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h5 {\n", "    font-size: 1.4993rem;\n", "  }\n", "}\n", ".MuiTypography-h6 {\n", "  font-size: 1.125rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 500;\n", "  line-height: 1.6;\n", "  letter-spacing: 0.0075em;\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-h6 {\n", "    font-size: 1.25rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-h6 {\n", "    font-size: 1.25rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-h6 {\n", "    font-size: 1.25rem;\n", "  }\n", "}\n", ".MuiTypography-subtitle1 {\n", "  color: #919aa3;\n", "  font-size: 1.4375rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: normal;\n", "  line-height: 1.5;\n", "  letter-spacing: normal;\n", "}\n", "@media (max-width:599.95px) {\n", "  .MuiTypography-subtitle1 {\n", "    font-size: 17px;\n", "  }\n", "}\n", "@media (min-width:600px) {\n", "  .MuiTypography-subtitle1 {\n", "    font-size: 1.6667rem;\n", "  }\n", "}\n", "@media (min-width:960px) {\n", "  .MuiTypography-subtitle1 {\n", "    font-size: 1.8333rem;\n", "  }\n", "}\n", "@media (min-width:1280px) {\n", "  .MuiTypography-subtitle1 {\n", "    font-size: 1.8333rem;\n", "  }\n", "}\n", ".MuiTypography-subtitle2 {\n", "  font-size: 0.875rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 500;\n", "  line-height: 1.57;\n", "  letter-spacing: 0.00714em;\n", "}\n", ".MuiTypography-overline {\n", "  font-size: 0.75rem;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 2.66;\n", "  letter-spacing: 0.08333em;\n", "  text-transform: uppercase;\n", "}\n", ".MuiTypography-srOnly {\n", "  width: 1px;\n", "  height: 1px;\n", "  overflow: hidden;\n", "  position: absolute;\n", "}\n", ".MuiTypography-alignLeft {\n", "  text-align: left;\n", "}\n", ".MuiTypography-alignCenter {\n", "  text-align: center;\n", "}\n", ".MuiTypography-alignRight {\n", "  text-align: right;\n", "}\n", ".MuiTypography-alignJustify {\n", "  text-align: justify;\n", "}\n", ".MuiTypography-noWrap {\n", "  overflow: hidden;\n", "  white-space: nowrap;\n", "  text-overflow: ellipsis;\n", "}\n", ".MuiTypography-gutterBottom {\n", "  margin-bottom: 40px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .MuiTypography-gutterBottom {\n", "    margin-bottom: 20px;\n", "  }\n", "}\n", ".MuiTypography-paragraph {\n", "  margin-bottom: 16px;\n", "}\n", ".MuiTypography-colorInherit {\n", "  color: inherit;\n", "}\n", ".MuiTypography-colorPrimary {\n", "  color: #4A84FF;\n", "}\n", ".MuiTypography-colorSecondary {\n", "  color: #fcb9ad;\n", "}\n", ".MuiTypography-colorTextPrimary {\n", "  color: rgba(0, 0, 0, 0.87);\n", "}\n", ".MuiTypography-colorTextSecondary {\n", "  color: rgba(0, 0, 0, 0.54);\n", "}\n", ".MuiTypography-colorError {\n", "  color: #f44336;\n", "}\n", ".MuiTypography-displayInline {\n", "  display: inline;\n", "}\n", ".MuiTypography-displayBlock {\n", "  display: block;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiTouchRipple\">\n", ".MuiTouchRipple-root {\n", "  top: 0;\n", "  left: 0;\n", "  right: 0;\n", "  bottom: 0;\n", "  z-index: 0;\n", "  overflow: hidden;\n", "  position: absolute;\n", "  border-radius: inherit;\n", "  pointer-events: none;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-ripple {\n", "  opacity: 0;\n", "  position: absolute;\n", "}\n", ".MuiTouchRipple-rippleVisible {\n", "  opacity: 0.3;\n", "  animation: <PERSON><PERSON><PERSON><PERSON><PERSON>Ripple-keyframes-enter 550ms cubic-bezier(0.4, 0, 0.2, 1);\n", "  transform: scale(1);\n", "}\n", ".MuiTouchRipple-ripplePulsate {\n", "  animation-duration: 200ms;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-child {\n", "  width: 100%;\n", "  height: 100%;\n", "  display: block;\n", "  opacity: 1;\n", "  border-radius: 50%;\n", "  background-color: currentColor;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-childLeaving {\n", "  opacity: 0;\n", "  animation: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-keyframes-exit 550ms cubic-bezier(0.4, 0, 0.2, 1);\n", "}\n", ".Mui<PERSON><PERSON>chR<PERSON>ple-childPulsate {\n", "  top: 0;\n", "  left: 0;\n", "  position: absolute;\n", "  animation: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ple-keyframes-pulsate 2500ms cubic-bezier(0.4, 0, 0.2, 1) 200ms infinite;\n", "}\n", "@-webkit-keyframes MuiTouchRipple-keyframes-enter {\n", "  0% {\n", "    opacity: 0.1;\n", "    transform: scale(0);\n", "  }\n", "  100% {\n", "    opacity: 0.3;\n", "    transform: scale(1);\n", "  }\n", "}\n", "@-webkit-keyframes MuiTouchRipple-keyframes-exit {\n", "  0% {\n", "    opacity: 1;\n", "  }\n", "  100% {\n", "    opacity: 0;\n", "  }\n", "}\n", "@-webkit-keyframes MuiTouchRipple-keyframes-pulsate {\n", "  0% {\n", "    transform: scale(1);\n", "  }\n", "  50% {\n", "    transform: scale(0.92);\n", "  }\n", "  100% {\n", "    transform: scale(1);\n", "  }\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiButtonBase\">\n", ".MuiButtonBase-root {\n", "  color: inherit;\n", "  border: 0;\n", "  cursor: pointer;\n", "  margin: 0;\n", "  display: inline-flex;\n", "  outline: 0;\n", "  padding: 0;\n", "  position: relative;\n", "  align-items: center;\n", "  user-select: none;\n", "  border-radius: 0;\n", "  vertical-align: middle;\n", "  -moz-appearance: none;\n", "  justify-content: center;\n", "  text-decoration: none;\n", "  background-color: transparent;\n", "  -webkit-appearance: none;\n", "  -webkit-tap-highlight-color: transparent;\n", "}\n", ".MuiButtonBase-root::-moz-focus-inner {\n", "  border-style: none;\n", "}\n", ".MuiButtonBase-root.Mui-disabled {\n", "  cursor: default;\n", "  pointer-events: none;\n", "}\n", "@media print {\n", "  .MuiButtonBase-root {\n", "    -webkit-print-color-adjust: exact;\n", "  }\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiSvgIcon\">\n", ".MuiSvgIcon-root {\n", "  fill: currentC<PERSON>r;\n", "  width: 1em;\n", "  height: 1em;\n", "  display: inline-block;\n", "  font-size: 1.5rem;\n", "  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "  flex-shrink: 0;\n", "  user-select: none;\n", "}\n", ".MuiSvgIcon-colorPrimary {\n", "  color: #4A84FF;\n", "}\n", ".MuiSvgIcon-colorSecondary {\n", "  color: #fcb9ad;\n", "}\n", ".MuiSvgIcon-colorAction {\n", "  color: rgba(0, 0, 0, 0.54);\n", "}\n", ".MuiSvgIcon-colorError {\n", "  color: #f44336;\n", "}\n", ".MuiSvgIcon-colorDisabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "}\n", ".MuiSvgIcon-fontSizeInherit {\n", "  font-size: inherit;\n", "}\n", ".MuiSvgIcon-fontSizeSmall {\n", "  font-size: 1.25rem;\n", "}\n", ".MuiSvgIcon-fontSizeLarge {\n", "  font-size: 2.1875rem;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiButton\">\n", ".<PERSON><PERSON><PERSON><PERSON>on-root {\n", "  color: rgba(0, 0, 0, 0.87);\n", "  padding: 6px 16px;\n", "  font-size: 14px;\n", "  min-width: 64px;\n", "  box-sizing: border-box;\n", "  transition: all 0.3s;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 500;\n", "  line-height: 1.14;\n", "  border-radius: 21.5px;\n", "  letter-spacing: 0.2px;\n", "  text-transform: initial;\n", "}\n", ".Mui<PERSON>utton-root:hover {\n", "  text-decoration: none;\n", "  background-color: rgba(0, 0, 0, 0.04);\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-root.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "}\n", "@media (hover: none) {\n", "  .Mui<PERSON>utton-root:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-root:hover.Mui-disabled {\n", "  background-color: transparent;\n", "}\n", ".MuiButton-label {\n", "  width: 100%;\n", "  display: inherit;\n", "  font-size: inherit;\n", "  align-items: inherit;\n", "  font-weight: inherit;\n", "  letter-spacing: inherit;\n", "  justify-content: inherit;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-text {\n", "  padding: 10px 8px;\n", "  box-shadow: none;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-textPrimary {\n", "  color: #4A84FF;\n", "}\n", ".Mu<PERSON>Button-textPrimary:hover {\n", "  background-color: #4A84FF0A;\n", "}\n", "@media (hover: none) {\n", "  .Mu<PERSON>Button-textPrimary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-textSecondary {\n", "  color: #002348;\n", "}\n", ".MuiButton-textSecondary:hover {\n", "  background-color: #0023480A;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-textSecondary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-outlined {\n", "  border: 1px solid rgba(0, 0, 0, 0.23);\n", "  padding: 10px 16px;\n", "  box-shadow: none;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-outlined.<PERSON><PERSON>-disabled {\n", "  border: 1px solid rgba(0, 0, 0, 0.12);\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-outlined.<PERSON><PERSON>-disabled {\n", "  color: white;\n", "  border-width: 0;\n", "}\n", ".Mui<PERSON><PERSON>on-outlinedPrimary {\n", "  color: #4A84FF;\n", "  border: 2px solid #4A84FF;\n", "}\n", ".MuiButton-outlinedPrimary:hover {\n", "  border: 2px solid #4A84FF;\n", "  background-color: #4A84FF0A;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-outlinedPrimary.Mui-disabled {\n", "  background-color: #4A84FF61;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-outlinedPrimary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiButton-outlinedSecondary {\n", "  color: #002348;\n", "  border: 2px solid #002348;\n", "}\n", ".MuiButton-outlinedSecondary:hover {\n", "  border: 2px solid #002348;\n", "  background-color: #0023480A;\n", "}\n", ".Mu<PERSON><PERSON><PERSON>on-outlinedSecondary.Mui-disabled {\n", "  border: 1px solid rgba(0, 0, 0, 0.26);\n", "}\n", ".Mu<PERSON><PERSON><PERSON>on-outlinedSecondary.Mui-disabled {\n", "  background-color: #00234861;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-outlinedSecondary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiButton-contained {\n", "  color: white;\n", "  padding: 10px 16px;\n", "  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2);\n", "  background-color: #e0e0e0;\n", "}\n", ".MuiButton-contained:hover {\n", "  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.2);\n", "  background-color: #d5d5d5;\n", "}\n", ".MuiButton-contained.Mui-focusVisible {\n", "  box-shadow: 0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12);\n", "}\n", ".MuiButton-contained:active {\n", "  box-shadow: 0px 5px 5px -3px rgba(0,0,0,0.2),0px 8px 10px 1px rgba(0,0,0,0.14),0px 3px 14px 2px rgba(0,0,0,0.12);\n", "}\n", ".MuiButton-contained.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "  box-shadow: none;\n", "  background-color: rgba(0, 0, 0, 0.12);\n", "}\n", ".MuiButton-contained.Mui-disabled {\n", "  color: white;\n", "  border-width: 0;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-contained:hover {\n", "    box-shadow: 0px 3px 1px -2px rgba(0,0,0,0.2),0px 2px 2px 0px rgba(0,0,0,0.14),0px 1px 5px 0px rgba(0,0,0,0.12);\n", "    background-color: #e0e0e0;\n", "  }\n", "}\n", ".MuiButton-contained:hover.Mui-disabled {\n", "  background-color: rgba(0, 0, 0, 0.12);\n", "}\n", ".MuiButton-containedPrimary {\n", "  color: white;\n", "  background-color: #4A84FF;\n", "}\n", ".MuiButton-containedPrimary:hover {\n", "  color: white;\n", "  background-color: #4A84FFEB;\n", "}\n", ".MuiButton-containedPrimary.Mui-disabled {\n", "  background-color: #4A84FF61;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-containedPrimary:hover {\n", "    background-color: #4A84FF;\n", "  }\n", "}\n", ".MuiButton-containedSecondary {\n", "  color: white;\n", "  border: 2px solid #002348;\n", "  background-color: #002348;\n", "}\n", ".MuiButton-containedSecondary:hover {\n", "  color: white;\n", "  border: 2px solid #002348;\n", "  background-color: #002348EB;\n", "}\n", ".MuiButton-containedSecondary.Mui-disabled {\n", "  background-color: #00234861;\n", "}\n", "@media (hover: none) {\n", "  .MuiButton-containedSecondary:hover {\n", "    background-color: #fcb9ad;\n", "  }\n", "}\n", ".MuiButton-disableElevation {\n", "  box-shadow: none;\n", "}\n", ".MuiButton-disableElevation:hover {\n", "  box-shadow: none;\n", "}\n", ".MuiButton-disableElevation.Mui-focusVisible {\n", "  box-shadow: none;\n", "}\n", ".MuiButton-disableElevation:active {\n", "  box-shadow: none;\n", "}\n", ".MuiButton-disableElevation.Mui-disabled {\n", "  box-shadow: none;\n", "}\n", ".MuiButton-colorInherit {\n", "  color: #83d0f0;\n", "  border-color: currentColor;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-textSizeSmall {\n", "  padding: 4px 5px;\n", "  font-size: 0.8125rem;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-textSizeLarge {\n", "  padding: 8px 11px;\n", "  font-size: 0.9375rem;\n", "}\n", ".MuiButton-outlinedSizeSmall {\n", "  padding: 3px 9px;\n", "  font-size: 0.8125rem;\n", "}\n", ".MuiButton-outlinedSizeLarge {\n", "  padding: 7px 21px;\n", "  font-size: 0.9375rem;\n", "}\n", ".MuiButton-containedSizeSmall {\n", "  padding: 4px 10px;\n", "  font-size: 0.8125rem;\n", "}\n", ".MuiButton-containedSizeLarge {\n", "  padding: 8px 22px;\n", "  font-size: 0.9375rem;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-fullWidth {\n", "  width: 100%;\n", "}\n", ".MuiButton-startIcon {\n", "  display: inherit;\n", "  margin-left: -4px;\n", "  margin-right: 8px;\n", "}\n", ".MuiButton-startIcon.MuiButton-iconSizeSmall {\n", "  margin-left: -2px;\n", "}\n", ".MuiButton-endIcon {\n", "  display: inherit;\n", "  margin-left: 8px;\n", "  margin-right: -4px;\n", "}\n", ".MuiButton-endIcon.MuiButton-iconSizeSmall {\n", "  margin-right: -2px;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-iconSizeSmall > *:first-child {\n", "  font-size: 18px;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-iconSizeMedium > *:first-child {\n", "  font-size: 20px;\n", "}\n", ".<PERSON><PERSON><PERSON><PERSON><PERSON>-iconSizeLarge > *:first-child {\n", "  font-size: 22px;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiIconButton\">\n", ".MuiIconButton-root {\n", "  flex: 0 0 auto;\n", "  color: rgba(0, 0, 0, 0.54);\n", "  padding: 12px;\n", "  overflow: visible;\n", "  font-size: 1.5rem;\n", "  text-align: center;\n", "  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "  border-radius: 50%;\n", "}\n", ".MuiIconButton-root:hover {\n", "  background-color: rgba(0, 0, 0, 0.04);\n", "}\n", ".MuiIconButton-root.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "  background-color: transparent;\n", "}\n", "@media (hover: none) {\n", "  .MuiIconButton-root:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiIconButton-edgeStart {\n", "  margin-left: -12px;\n", "}\n", ".MuiIconButton-sizeSmall.MuiIconButton-edgeStart {\n", "  margin-left: -3px;\n", "}\n", ".MuiIconButton-edgeEnd {\n", "  margin-right: -12px;\n", "}\n", ".MuiIconButton-sizeSmall.MuiIconButton-edgeEnd {\n", "  margin-right: -3px;\n", "}\n", ".MuiIconButton-colorInherit {\n", "  color: inherit;\n", "}\n", ".MuiIconButton-colorPrimary {\n", "  color: #4A84FF;\n", "}\n", ".MuiIconButton-colorPrimary:hover {\n", "  background-color: rgba(74, 132, 255, 0.04);\n", "}\n", "@media (hover: none) {\n", "  .MuiIconButton-colorPrimary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiIconButton-colorSecondary {\n", "  color: #fcb9ad;\n", "}\n", ".MuiIconButton-colorSecondary:hover {\n", "  background-color: rgba(252, 185, 173, 0.04);\n", "}\n", "@media (hover: none) {\n", "  .MuiIconButton-colorSecondary:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiIconButton-sizeSmall {\n", "  padding: 3px;\n", "  font-size: 1.125rem;\n", "}\n", ".MuiIconButton-label {\n", "  width: 100%;\n", "  display: flex;\n", "  align-items: inherit;\n", "  justify-content: inherit;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"PrivateSwitchBase\">\n", ".jss203 {\n", "  padding: 9px;\n", "}\n", ".jss206 {\n", "  top: 0;\n", "  left: 0;\n", "  width: 100%;\n", "  cursor: inherit;\n", "  height: 100%;\n", "  margin: 0;\n", "  opacity: 0;\n", "  padding: 0;\n", "  z-index: 1;\n", "  position: absolute;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiCircularProgress\">\n", ".MuiCircularProgress-root {\n", "  display: inline-block;\n", "}\n", ".MuiCircularProgress-static {\n", "  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiCircularProgress-indeterminate {\n", "  animation: MuiCircularProgress-keyframes-circular-rotate 1.4s linear infinite;\n", "}\n", ".MuiCircularProgress-determinate {\n", "  transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiCircularProgress-colorPrimary {\n", "  color: #4A84FF;\n", "}\n", ".MuiCircularProgress-colorSecondary {\n", "  color: #fcb9ad;\n", "}\n", ".MuiCircularProgress-svg {\n", "  display: block;\n", "}\n", ".MuiCircularProgress-circle {\n", "  stroke: currentColor;\n", "}\n", ".MuiCircularProgress-circleStatic {\n", "  transition: stroke-dashoffset 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiCircularProgress-circleIndeterminate {\n", "  animation: MuiCircularProgress-keyframes-circular-dash 1.4s ease-in-out infinite;\n", "  stroke-dasharray: 80px, 200px;\n", "  stroke-dashoffset: 0px;\n", "}\n", ".MuiCircularProgress-circleDeterminate {\n", "  transition: stroke-dashoffset 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", "@-webkit-keyframes MuiCircularProgress-keyframes-circular-rotate {\n", "  0% {\n", "    transform-origin: 50% 50%;\n", "  }\n", "  100% {\n", "    transform: rotate(360deg);\n", "  }\n", "}\n", "@-webkit-keyframes MuiCircularProgress-keyframes-circular-dash {\n", "  0% {\n", "    stroke-dasharray: 1px, 200px;\n", "    stroke-dashoffset: 0px;\n", "  }\n", "  50% {\n", "    stroke-dasharray: 100px, 200px;\n", "    stroke-dashoffset: -15px;\n", "  }\n", "  100% {\n", "    stroke-dasharray: 100px, 200px;\n", "    stroke-dashoffset: -125px;\n", "  }\n", "}\n", ".MuiCircularProgress-circleDisableShrink {\n", "  animation: none;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiBackdrop\">\n", ".MuiBackdrop-root {\n", "  top: 0;\n", "  left: 0;\n", "  right: 0;\n", "  bottom: 0;\n", "  display: flex;\n", "  z-index: -1;\n", "  position: fixed;\n", "  align-items: center;\n", "  justify-content: center;\n", "  background-color: rgba(0, 0, 0, 0.5);\n", "  -webkit-tap-highlight-color: transparent;\n", "}\n", ".MuiBackdrop-invisible {\n", "  background-color: transparent;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"makeStyles\">\n", ".jss202 {\n", "  width: 100%;\n", "  margin-top: 25px;\n", "  text-align: center;\n", "  line-height: 0.01em;\n", "  border-bottom: 1px solid var(--secondary-color-2-o1);\n", "}\n", ".jss202 span {\n", "  color: var(--secondary-color-2-o4);\n", "  padding: 0 5px;\n", "  font-size: 14px;\n", "  background-color: var(--neutral-color-2);\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiInputBase\">\n", "@-webkit-keyframes mui-auto-fill {}\n", "@-webkit-keyframes mui-auto-fill-cancel {}\n", ".MuiInputBase-root {\n", "  color: rgba(0, 0, 0, 0.87);\n", "  cursor: text;\n", "  display: inline-flex;\n", "  position: relative;\n", "  font-size: 1rem;\n", "  box-sizing: border-box;\n", "  align-items: center;\n", "  font-family: \"<PERSON>o\", \"Helve<PERSON>\", \"Aria<PERSON>\", sans-serif;\n", "  font-weight: 400;\n", "  line-height: 1.1876em;\n", "  letter-spacing: 0.00938em;\n", "}\n", ".MuiInputBase-root.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.38);\n", "  cursor: default;\n", "}\n", ".MuiInputBase-multiline {\n", "  padding: 6px 0 7px;\n", "}\n", ".MuiInputBase-multiline.MuiInputBase-marginDense {\n", "  padding-top: 3px;\n", "}\n", ".MuiInputBase-fullWidth {\n", "  width: 100%;\n", "}\n", ".MuiInputBase-input {\n", "  font: inherit;\n", "  color: currentColor;\n", "  width: 100%;\n", "  border: 0;\n", "  height: 1.1876em;\n", "  margin: 0;\n", "  display: block;\n", "  padding: 6px 0 7px;\n", "  min-width: 0;\n", "  background: none;\n", "  box-sizing: content-box;\n", "  animation-name: mui-auto-fill-cancel;\n", "  letter-spacing: inherit;\n", "  animation-duration: 10ms;\n", "  -webkit-tap-highlight-color: transparent;\n", "}\n", ".MuiInputBase-input::-webkit-input-placeholder {\n", "  color: currentColor;\n", "  opacity: 0.42;\n", "  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiInputBase-input::-moz-placeholder {\n", "  color: currentColor;\n", "  opacity: 0.42;\n", "  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiInputBase-input:-ms-input-placeholder {\n", "  color: currentColor;\n", "  opacity: 0.42;\n", "  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiInputBase-input::-ms-input-placeholder {\n", "  color: currentColor;\n", "  opacity: 0.42;\n", "  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "}\n", ".MuiInputBase-input:focus {\n", "  outline: 0;\n", "}\n", ".MuiInputBase-input:invalid {\n", "  box-shadow: none;\n", "}\n", ".MuiInputBase-input::-webkit-search-decoration {\n", "  -webkit-appearance: none;\n", "}\n", ".MuiInputBase-input.Mui-disabled {\n", "  opacity: 1;\n", "}\n", ".MuiInputBase-input:-webkit-autofill {\n", "  animation-name: mui-auto-fill;\n", "  animation-duration: 5000s;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input::-webkit-input-placeholder {\n", "  opacity: 0 !important;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input::-moz-placeholder {\n", "  opacity: 0 !important;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input:-ms-input-placeholder {\n", "  opacity: 0 !important;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input::-ms-input-placeholder {\n", "  opacity: 0 !important;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input:focus::-webkit-input-placeholder {\n", "  opacity: 0.42;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input:focus::-moz-placeholder {\n", "  opacity: 0.42;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input:focus:-ms-input-placeholder {\n", "  opacity: 0.42;\n", "}\n", "label[data-shrink=false] + .MuiInputBase-formControl .MuiInputBase-input:focus::-ms-input-placeholder {\n", "  opacity: 0.42;\n", "}\n", ".MuiInputBase-inputMarginDense {\n", "  padding-top: 3px;\n", "}\n", ".MuiInputBase-inputMultiline {\n", "  height: auto;\n", "  resize: none;\n", "  padding: 0;\n", "}\n", ".MuiInputBase-inputTypeSearch {\n", "  -moz-appearance: textfield;\n", "  -webkit-appearance: textfield;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiInput\">\n", ".MuiInput-root {\n", "  position: relative;\n", "}\n", "label + .MuiInput-formControl {\n", "  margin-top: 16px;\n", "}\n", ".MuiInput-colorSecondary.MuiInput-underline:after {\n", "  border-bottom-color: #fcb9ad;\n", "}\n", ".MuiInput-underline:after {\n", "  left: 0;\n", "  right: 0;\n", "  bottom: 0;\n", "  content: \"\";\n", "  position: absolute;\n", "  transform: scaleX(0);\n", "  transition: transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;\n", "  border-bottom: 2px solid #4A84FF;\n", "  pointer-events: none;\n", "}\n", ".MuiInput-underline.Mui-focused:after {\n", "  transform: scaleX(1);\n", "}\n", ".MuiInput-underline.Mui-error:after {\n", "  transform: scaleX(1);\n", "  border-bottom-color: #f44336;\n", "}\n", ".MuiInput-underline:before {\n", "  left: 0;\n", "  right: 0;\n", "  bottom: 0;\n", "  content: \"\\00a0\";\n", "  position: absolute;\n", "  transition: border-bottom-color 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;\n", "  border-bottom: 1px solid rgba(0, 0, 0, 0.42);\n", "  pointer-events: none;\n", "}\n", ".MuiInput-underline:hover:not(.Mui-disabled):before {\n", "  border-bottom: 2px solid rgba(0, 0, 0, 0.87);\n", "}\n", ".MuiInput-underline.Mui-disabled:before {\n", "  border-bottom-style: dotted;\n", "}\n", "@media (hover: none) {\n", "  .MuiInput-underline:hover:not(.Mui-disabled):before {\n", "    border-bottom: 1px solid rgba(0, 0, 0, 0.42);\n", "  }\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiFormControl\">\n", ".MuiFormControl-root {\n", "  border: 0;\n", "  margin: 0;\n", "  display: inline-flex;\n", "  padding: 0;\n", "  position: relative;\n", "  min-width: 0;\n", "  flex-direction: column;\n", "  vertical-align: top;\n", "}\n", ".MuiFormControl-marginNormal {\n", "  margin-top: 16px;\n", "  margin-bottom: 8px;\n", "}\n", ".MuiFormControl-marginDense {\n", "  margin-top: 8px;\n", "  margin-bottom: 4px;\n", "}\n", ".MuiFormControl-fullWidth {\n", "  width: 100%;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiTextField\">\n", "\n", "</style><style data-jss=\"\" data-meta=\"MuiFormControlLabel\">\n", ".MuiFormControlLabel-root {\n", "  cursor: pointer;\n", "  display: inline-flex;\n", "  align-items: center;\n", "  margin-left: -11px;\n", "  margin-right: 16px;\n", "  vertical-align: middle;\n", "  -webkit-tap-highlight-color: transparent;\n", "}\n", ".MuiFormControlLabel-root.Mui-disabled {\n", "  cursor: default;\n", "}\n", ".MuiFormControlLabel-labelPlacementStart {\n", "  margin-left: 16px;\n", "  margin-right: -11px;\n", "  flex-direction: row-reverse;\n", "}\n", ".MuiFormControlLabel-labelPlacementTop {\n", "  margin-left: 16px;\n", "  flex-direction: column-reverse;\n", "}\n", ".MuiFormControlLabel-labelPlacementBottom {\n", "  margin-left: 16px;\n", "  flex-direction: column;\n", "}\n", ".MuiFormControlLabel-label.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.38);\n", "}\n", "</style><style data-jss=\"\" data-meta=\"MuiCheckbox\">\n", ".MuiCheckbox-root {\n", "  color: rgba(0, 0, 0, 0.54);\n", "}\n", ".MuiCheckbox-colorPrimary.Mui-checked {\n", "  color: #4A84FF;\n", "}\n", ".MuiCheckbox-colorPrimary.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "}\n", ".MuiCheckbox-colorPrimary.Mui-checked:hover {\n", "  background-color: rgba(74, 132, 255, 0.04);\n", "}\n", "@media (hover: none) {\n", "  .MuiCheckbox-colorPrimary.Mui-checked:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", ".MuiCheckbox-colorSecondary.Mui-checked {\n", "  color: #fcb9ad;\n", "}\n", ".MuiCheckbox-colorSecondary.Mui-disabled {\n", "  color: rgba(0, 0, 0, 0.26);\n", "}\n", ".MuiCheckbox-colorSecondary.Mui-checked:hover {\n", "  background-color: rgba(252, 185, 173, 0.04);\n", "}\n", "@media (hover: none) {\n", "  .MuiCheckbox-colorSecondary.Mui-checked:hover {\n", "    background-color: transparent;\n", "  }\n", "}\n", "</style><style data-jss=\"\" data-meta=\"makeStyles\">\n", ".jss169 {\n", "  width: 510px;\n", "  display: flex;\n", "  padding: 45px 60px;\n", "  position: relative;\n", "  box-shadow: 0 2px 30px 0 var(--black-3-color);\n", "  align-items: center;\n", "  border-bottom: 19px solid var(--primary-color-2);\n", "  border-radius: 16px;\n", "  flex-direction: column;\n", "  background-color: var(--neutral-color-2);\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss169 {\n", "    width: 100vw;\n", "    height: auto;\n", "    padding: 30px 45px;\n", "    min-height: 90vh;\n", "    border-radius: 0;\n", "  }\n", "}\n", ".jss170 {\n", "  width: 100%;\n", "  height: 100vh;\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "  background-color: var(--background-color);\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss170 {\n", "    height: 100%;\n", "  }\n", "}\n", ".jss171 {\n", "  top: -69px;\n", "  right: -215px;\n", "  z-index: 100;\n", "  position: absolute;\n", "}\n", "@media (max-width:959.95px) {\n", "  .jss171 {\n", "    display: none;\n", "  }\n", "}\n", ".jss172 {\n", "  color: #002348;\n", "  font-size: 30px;\n", "  align-self: center;\n", "  text-align: center;\n", "  font-weight: bold;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss172 {\n", "    font-size: 24px;\n", "  }\n", "}\n", ".jss173 {\n", "  max-width: 150px;\n", "  margin-bottom: 40px;\n", "}\n", ".jss174 {\n", "  margin-top: 25px;\n", "}\n", ".jss175 {\n", "  width: 100%;\n", "  display: flex;\n", "  margin-top: 25px;\n", "  align-items: flex-start;\n", "  flex-direction: column;\n", "}\n", ".jss176 .MuiInput-underline:before {\n", "  border-bottom-color: var(--secondary-color-2-o1);\n", "}\n", ".jss176 .MuiInputBase-root {\n", "  padding: 0 0 5px 0;\n", "}\n", ".jss176 .MuiFormHelperText-root {\n", "  margin-left: 34px;\n", "}\n", ".jss177 {\n", "  flex: 1;\n", "  width: 100%;\n", "  margin-bottom: 25px;\n", "}\n", ".jss178 {\n", "  margin-bottom: 3px;\n", "}\n", ".jss179 {\n", "  max-width: 25px;\n", "  max-height: 25px;\n", "}\n", ".jss180 {\n", "  margin-left: 10px;\n", "}\n", ".jss181 {\n", "  width: 100%;\n", "  display: flex;\n", "  align-items: center;\n", "  margin-bottom: 20px;\n", "  justify-content: space-between;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss181 {\n", "    margin-top: -10px;\n", "  }\n", "}\n", ".jss182 {\n", "  color: #002348;\n", "  margin: 0;\n", "  margin-left: -11px;\n", "  margin-right: 5px;\n", "}\n", ".jss182 .MuiIconButton-label {\n", "  color: #83d0f0;\n", "  border-radius: 5px;\n", "}\n", ".jss182 .MuiTypography-body1 {\n", "  font-size: 14px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss182 .MuiTypography-body1 {\n", "    font-size: 12px;\n", "  }\n", "}\n", ".jss183 {\n", "  width: 100%;\n", "}\n", ".jss184 {\n", "  padding: 13px 45px;\n", "}\n", ".jss184.<PERSON><PERSON>-disabled {\n", "  color: white;\n", "  background-color: #4A84FF;\n", "}\n", ".jss185 {\n", "  font-weight: bold;\n", "}\n", ".jss186 {\n", "  opacity: 0.2;\n", "}\n", ".jss187 {\n", "  opacity: 0.9;\n", "}\n", ".jss188 {\n", "  color: #83d0f0;\n", "  font-size: 14px;\n", "}\n", ".jss188:hover {\n", "  color: #83d0f0;\n", "  text-shadow: 0px 0px 0.2px #83d0f0;\n", "  text-decoration: underline;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss188 {\n", "    font-size: 12px;\n", "  }\n", "}\n", ".jss189 {\n", "  color: #002348;\n", "  font-size: 14px;\n", "  margin-top: 25px;\n", "}\n", ".jss190 {\n", "  margin-top: 4px;\n", "}\n", ".jss191 {\n", "  color: #fcb9ad;\n", "  font-size: 14px;\n", "  align-self: center;\n", "  margin-top: 5px;\n", "  text-align: center;\n", "}\n", ".jss192 {\n", "  color: #4A84FF;\n", "  font-weight: bold;\n", "}\n", ".jss192:hover {\n", "  text-decoration: underline;\n", "}\n", ".jss193 {\n", "  flex: 1;\n", "  width: 100%;\n", "  border: 2px solid #83d0f0;\n", "  display: flex;\n", "  font-size: 14px;\n", "  background: rgba(131, 208, 240, 0.1);\n", "  margin-top: -15px;\n", "  max-height: 40px;\n", "  min-height: 40px;\n", "  align-items: center;\n", "  line-height: 20px;\n", "  border-radius: 25px;\n", "  margin-bottom: 10px;\n", "  justify-content: space-between;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss193 {\n", "    font-size: 12px;\n", "    letter-spacing: -0.5px;\n", "  }\n", "}\n", ".jss194 {\n", "  flex: 1;\n", "  width: 100%;\n", "  border: 2px solid #e67f7f;\n", "  display: flex;\n", "  font-size: 14px;\n", "  background: rgba(230, 127, 127, 0.1);\n", "  margin-top: -15px;\n", "  max-height: 40px;\n", "  min-height: 40px;\n", "  align-items: center;\n", "  line-height: 20px;\n", "  border-radius: 25px;\n", "  margin-bottom: 10px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss194 {\n", "    font-size: 12px;\n", "    letter-spacing: -0.5px;\n", "  }\n", "}\n", ".jss195 {\n", "  display: flex;\n", "  align-i-tems: center;\n", "  justify-content: center;\n", "}\n", ".jss196 {\n", "  width: 24px;\n", "  height: 24px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss196 {\n", "    width: 20px;\n", "    height: 20px;\n", "  }\n", "}\n", ".jss197 {\n", "  width: 40px;\n", "  display: flex;\n", "  padding: 0 8px;\n", "  text-align: center;\n", "  align-items: center;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss197 {\n", "    width: 32px;\n", "  }\n", "}\n", ".jss198 {\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "}\n", ".jss199 {\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "}\n", ".jss200 {\n", "  color: #83d0f0;\n", "  font-weight: bolder;\n", "  margin-right: 10px;\n", "  text-decoration: underline;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss200 {\n", "    margin-right: 5px;\n", "  }\n", "}\n", ".jss201 {\n", "  color: #83d0f0;\n", "}\n", ".jss201:hover {\n", "  color: #4A84FF;\n", "}\n", "</style><style data-jss=\"\" data-meta=\"makeStyles\">\n", ".jss136 {\n", "  width: 510px;\n", "  display: flex;\n", "  padding: 45px 60px;\n", "  position: relative;\n", "  box-shadow: 0 2px 30px 0 var(--black-3-color);\n", "  align-items: center;\n", "  border-bottom: 19px solid var(--primary-color-2);\n", "  border-radius: 16px;\n", "  flex-direction: column;\n", "  background-color: var(--neutral-color-2);\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss136 {\n", "    width: 100vw;\n", "    height: 100%;\n", "    padding: 30px 45px;\n", "    border-radius: 0;\n", "  }\n", "}\n", ".jss137 {\n", "  width: 100%;\n", "  height: 100vh;\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "  background-color: var(--background-color);\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss137 {\n", "    height: 100%;\n", "  }\n", "}\n", ".jss138 {\n", "  top: -69px;\n", "  right: -215px;\n", "  z-index: 100;\n", "  position: absolute;\n", "}\n", "@media (max-width:959.95px) {\n", "  .jss138 {\n", "    display: none;\n", "  }\n", "}\n", ".jss139 {\n", "  color: #002348;\n", "  font-size: 30px;\n", "  align-self: center;\n", "  text-align: center;\n", "  font-weight: bold;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss139 {\n", "    font-size: 24px;\n", "  }\n", "}\n", ".jss140 {\n", "  max-width: 150px;\n", "  margin-bottom: 40px;\n", "}\n", ".jss141 {\n", "  margin-top: 25px;\n", "}\n", ".jss142 {\n", "  width: 100%;\n", "  display: flex;\n", "  margin-top: 25px;\n", "  align-items: flex-start;\n", "  flex-direction: column;\n", "}\n", ".jss143 .MuiInput-underline:before {\n", "  border-bottom-color: var(--secondary-color-2-o1);\n", "}\n", ".jss143 .MuiInputBase-root {\n", "  padding: 0 0 5px 0;\n", "}\n", ".jss143 .MuiFormHelperText-root {\n", "  margin-left: 34px;\n", "}\n", ".jss144 {\n", "  flex: 1;\n", "  width: 100%;\n", "  margin-bottom: 25px;\n", "}\n", ".jss145 {\n", "  margin-bottom: 3px;\n", "}\n", ".jss146 {\n", "  max-width: 25px;\n", "  max-height: 25px;\n", "}\n", ".jss147 {\n", "  margin-left: 10px;\n", "}\n", ".jss148 {\n", "  width: 100%;\n", "  display: flex;\n", "  align-items: center;\n", "  margin-bottom: 20px;\n", "  justify-content: space-between;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss148 {\n", "    margin-top: -10px;\n", "  }\n", "}\n", ".jss149 {\n", "  color: #002348;\n", "  margin: 0;\n", "  margin-left: -11px;\n", "  margin-right: 5px;\n", "}\n", ".jss149 .MuiIconButton-label {\n", "  color: #83d0f0;\n", "  border-radius: 5px;\n", "}\n", ".jss149 .MuiTypography-body1 {\n", "  font-size: 14px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss149 .MuiTypography-body1 {\n", "    font-size: 12px;\n", "  }\n", "}\n", ".jss150 {\n", "  width: 100%;\n", "}\n", ".jss151 {\n", "  padding: 13px 45px;\n", "}\n", ".jss151.<PERSON><PERSON>-disabled {\n", "  color: white;\n", "  background-color: #4A84FF;\n", "}\n", ".jss152 {\n", "  font-weight: bold;\n", "}\n", ".jss153 {\n", "  opacity: 0.2;\n", "}\n", ".jss154 {\n", "  opacity: 0.9;\n", "}\n", ".jss155 {\n", "  color: #83d0f0;\n", "  font-size: 14px;\n", "}\n", ".jss155:hover {\n", "  color: #83d0f0;\n", "  text-shadow: 0px 0px 0.2px #83d0f0;\n", "  text-decoration: underline;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss155 {\n", "    font-size: 12px;\n", "  }\n", "}\n", ".jss156 {\n", "  color: #002348;\n", "  font-size: 14px;\n", "  margin-top: 25px;\n", "}\n", ".jss157 {\n", "  margin-top: 4px;\n", "}\n", ".jss158 {\n", "  color: #fcb9ad;\n", "  font-size: 14px;\n", "  align-self: center;\n", "  margin-top: 5px;\n", "  text-align: center;\n", "}\n", ".jss159 {\n", "  color: #4A84FF;\n", "  font-weight: bold;\n", "}\n", ".jss159:hover {\n", "  text-decoration: underline;\n", "}\n", ".jss160 {\n", "  flex: 1;\n", "  width: 100%;\n", "  border: 2px solid #83d0f0;\n", "  display: flex;\n", "  font-size: 14px;\n", "  background: rgba(131, 208, 240, 0.1);\n", "  margin-top: -15px;\n", "  max-height: 40px;\n", "  min-height: 40px;\n", "  align-items: center;\n", "  line-height: 20px;\n", "  border-radius: 25px;\n", "  margin-bottom: 10px;\n", "  justify-content: space-between;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss160 {\n", "    font-size: 12px;\n", "    letter-spacing: -0.5px;\n", "  }\n", "}\n", ".jss161 {\n", "  flex: 1;\n", "  width: 100%;\n", "  border: 2px solid #e67f7f;\n", "  display: flex;\n", "  font-size: 14px;\n", "  background: rgba(230, 127, 127, 0.1);\n", "  margin-top: -15px;\n", "  max-height: 40px;\n", "  min-height: 40px;\n", "  align-items: center;\n", "  line-height: 20px;\n", "  border-radius: 25px;\n", "  margin-bottom: 10px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss161 {\n", "    font-size: 12px;\n", "    letter-spacing: -0.5px;\n", "  }\n", "}\n", ".jss162 {\n", "  display: flex;\n", "  align-i-tems: center;\n", "  justify-content: center;\n", "}\n", ".jss163 {\n", "  width: 24px;\n", "  height: 24px;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss163 {\n", "    width: 20px;\n", "    height: 20px;\n", "  }\n", "}\n", ".jss164 {\n", "  width: 40px;\n", "  display: flex;\n", "  padding: 0 8px;\n", "  text-align: center;\n", "  align-items: center;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss164 {\n", "    width: 32px;\n", "  }\n", "}\n", ".jss165 {\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "}\n", ".jss166 {\n", "  display: flex;\n", "  align-items: center;\n", "  justify-content: center;\n", "}\n", ".jss167 {\n", "  color: #83d0f0;\n", "  font-weight: bolder;\n", "  margin-right: 10px;\n", "  text-decoration: underline;\n", "}\n", "@media (max-width:599.95px) {\n", "  .jss167 {\n", "    margin-right: 5px;\n", "  }\n", "}\n", ".jss168 {\n", "  color: #83d0f0;\n", "}\n", ".jss168:hover {\n", "  color: #4A84FF;\n", "}\n", "</style><script data-react-helmet=\"true\">\n", "      (function(h,o,t,j,a,r){\n", "        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n", "        h._hjSettings={hjid:2232518,hjsv:6};\n", "        a=o.getElementsByTagName('head')[0];\n", "        r=o.createElement('script');r.async=1;\n", "        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n", "        a.append<PERSON><PERSON><PERSON>(r);\n", "    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n", "      </script><script async=\"\" src=\"https://static.hotjar.com/c/hotjar-2232518.js?sv=6\"></script><script data-react-helmet=\"true\">!(function(f, b, e, v, n, t, s) {\n", "        if (f.fbq) return;\n", "        n = f.fbq = function() {\n", "          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);\n", "        };\n", "        if (!f._fbq) f._fbq = n;\n", "        n.push = n;\n", "        n.loaded = !0;\n", "        n.version = '2.0';\n", "        n.queue = [];\n", "        t = b.createElement(e);\n", "        t.async = !0;\n", "        t.src = v;\n", "        s = b.getElementsByTagName(e)[0];\n", "        s.parentNode.insertBefore(t, s);\n", "      })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');\n", "      fbq('init', '1630750960310529');\n", "      fbq('track', 'PageView');</script><style type=\"text/css\" data-fbcssmodules=\"css:fb.css.base css:fb.css.dialog css:fb.css.iframewidget\">.fb_hidden{position:absolute;top:-10000px;z-index:10001}.fb_reposition{overflow:hidden;position:relative}.fb_invisible{display:none}.fb_reset{background:none;border:0px;border-spacing:0;color:#000;cursor:auto;direction:ltr;font-family:lucida grande,tahoma,verdana,arial,sans-serif;font-size:11px;font-style:normal;font-variant:normal;font-weight:400;letter-spacing:normal;line-height:1;margin:0;overflow:visible;padding:0;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;visibility:visible;white-space:normal;word-spacing:normal}.fb_reset>div{overflow:hidden}@keyframes fb_transform{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.fb_animate{animation:fb_transform .3s forwards}\n", "\n", ".fb_hidden{position:absolute;top:-10000px;z-index:10001}.fb_reposition{overflow:hidden;position:relative}.fb_invisible{display:none}.fb_reset{background:none;border:0px;border-spacing:0;color:#000;cursor:auto;direction:ltr;font-family:lucida grande,tahoma,verdana,arial,sans-serif;font-size:11px;font-style:normal;font-variant:normal;font-weight:400;letter-spacing:normal;line-height:1;margin:0;overflow:visible;padding:0;text-align:left;text-decoration:none;text-indent:0;text-shadow:none;text-transform:none;visibility:visible;white-space:normal;word-spacing:normal}.fb_reset>div{overflow:hidden}@keyframes fb_transform{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.fb_animate{animation:fb_transform .3s forwards}\n", "\n", ".fb_dialog{background:#525252b3;position:absolute;top:-10000px;z-index:10001}.fb_dialog_advanced{border-radius:8px;padding:10px}.fb_dialog_content{background:#fff;color:#373737}.fb_dialog_close_icon{background:url(https://connect.facebook.net/rsrc.php/v4/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 0 transparent;cursor:pointer;display:block;height:15px;position:absolute;right:18px;top:17px;width:15px}.fb_dialog_mobile .fb_dialog_close_icon{left:5px;right:auto;top:5px}.fb_dialog_padding{background-color:transparent;position:absolute;width:1px;z-index:-1}.fb_dialog_close_icon:hover{background:url(https://connect.facebook.net/rsrc.php/v4/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -15px transparent}.fb_dialog_close_icon:active{background:url(https://connect.facebook.net/rsrc.php/v4/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -30px transparent}.fb_dialog_iframe{line-height:0}.fb_dialog_content .dialog_title{background:#6d84b4;border:1px solid #365899;color:#fff;font-size:14px;font-weight:700;margin:0}.fb_dialog_content .dialog_title>span{background:url(https://connect.facebook.net/rsrc.php/v4/yd/r/Cou7n-nqK52.gif) no-repeat 5px 50%;float:left;padding:5px 0 7px 26px}body.fb_hidden{height:100%;left:0;margin:0;overflow:visible;position:absolute;top:-10000px;transform:none;width:100%}.fb_dialog.fb_dialog_mobile.loading{background:url(https://connect.facebook.net/rsrc.php/v4/ya/r/3rhSv5V8j3o.gif) #fff no-repeat 50% 50%;min-height:100%;min-width:100%;overflow:hidden;position:absolute;top:0;z-index:10001}.fb_dialog.fb_dialog_mobile.loading.centered{background:none;height:auto;min-height:initial;min-width:initial;width:auto}.fb_dialog.fb_dialog_mobile.loading.centered #fb_dialog_loader_spinner{width:100%}.fb_dialog.fb_dialog_mobile.loading.centered .fb_dialog_content{background:none}.loading.centered #fb_dialog_loader_close{clear:both;color:#fff;display:block;font-size:18px;padding-top:20px}#fb-root #fb_dialog_ipad_overlay{background:#0006;inset:0;min-height:100%;position:absolute;width:100%;z-index:10000}#fb-root #fb_dialog_ipad_overlay.hidden{display:none}.fb_dialog.fb_dialog_mobile.loading iframe{visibility:hidden}.fb_dialog_mobile .fb_dialog_iframe{position:sticky;top:0}.fb_dialog_content .dialog_header{background:linear-gradient(from(#738aba),to(#2c4987));border-bottom:1px solid;border-color:#043b87;box-shadow:#fff 0 1px 1px -1px inset;color:#fff;font:700 14px Helvetica,sans-serif;text-overflow:ellipsis;text-shadow:rgba(0,30,84,.296875) 0px -1px 0px;vertical-align:middle;white-space:nowrap}.fb_dialog_content .dialog_header table{height:43px;width:100%}.fb_dialog_content .dialog_header td.header_left{font-size:12px;padding-left:5px;vertical-align:middle;width:60px}.fb_dialog_content .dialog_header td.header_right{font-size:12px;padding-right:5px;vertical-align:middle;width:60px}.fb_dialog_content .touchable_button{background:linear-gradient(from(#4267B2),to(#2a4887));background-clip:padding-box;border:1px solid #29487d;border-radius:3px;display:inline-block;line-height:18px;margin-top:3px;max-width:85px;padding:4px 12px;position:relative}.fb_dialog_content .dialog_header .touchable_button input{background:none;border:none;color:#fff;font:700 12px Helvetica,sans-serif;margin:2px -12px;padding:2px 6px 3px;text-shadow:rgba(0,30,84,.296875) 0px -1px 0px}.fb_dialog_content .dialog_header .header_center{color:#fff;font-size:16px;font-weight:700;line-height:18px;text-align:center;vertical-align:middle}.fb_dialog_content .dialog_content{background:url(https://connect.facebook.net/rsrc.php/v4/y9/r/jKEcVPZFk-2.gif) no-repeat 50% 50%;border:1px solid #4A4A4A;border-bottom:0;border-top:0;height:150px}.fb_dialog_content .dialog_footer{background:#f5f6f7;border:1px solid #4A4A4A;border-top-color:#ccc;height:40px}#fb_dialog_loader_close{float:left}.fb_dialog.fb_dialog_mobile .fb_dialog_close_icon{visibility:hidden}#fb_dialog_loader_spinner{animation:rotateSpinner 1.2s linear infinite;background-color:transparent;background-image:url(https://connect.facebook.net/rsrc.php/v4/yD/r/t-wz8gw1xG1.png);background-position:50% 50%;background-repeat:no-repeat;height:24px;width:24px}@keyframes rotateSpinner{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\n", "\n", ".fb_iframe_widget{display:inline-block;position:relative}.fb_iframe_widget span{display:inline-block;position:relative;text-align:justify}.fb_iframe_widget iframe{position:absolute}.fb_iframe_widget_fluid_desktop,.fb_iframe_widget_fluid_desktop span,.fb_iframe_widget_fluid_desktop iframe{max-width:100%}.fb_iframe_widget_fluid_desktop iframe{min-width:220px;position:relative}.fb_iframe_widget_lift{z-index:1}.fb_iframe_widget_fluid{display:inline}.fb_iframe_widget_fluid span{width:100%}\n", "</style><script type=\"text/javascript\" async=\"\" src=\"https://collector.leaddyno.com//elc?url=https%3A%2F%2Fwww.oscer.ai%2F&amp;referrer=&amp;agent=Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F139.0.0.0%20Safari%2F537.36&amp;js=true&amp;ld_t=2a3f0518-44da-4cd9-bca8-1956d3017025&amp;ld_ext=&amp;ld_fp=3574394603&amp;channel=&amp;purchase_code=&amp;key=17fc4507a19d14c59e69da73e1da7fc947bae41e&amp;callback=__LDCB_1756475116402_639017\"></script><script async=\"\" src=\"https://script.hotjar.com/modules.79484904679daf64371c.js\" charset=\"utf-8\"></script><style id=\"googleidentityservice_button_styles\" nonce=\"undefined\">.qJTHM{-webkit-user-select:none;color:#202124;direction:ltr;-webkit-touch-callout:none;font-family:\"Roboto-Regular\",arial,sans-serif;-webkit-font-smoothing:antialiased;font-weight:400;margin:0;overflow:hidden;-webkit-text-size-adjust:100%}.ynRLnc{left:-9999px;position:absolute;top:-9999px}.L6cTce{display:none}.bltWBb{overflow-wrap:break-word;word-break:break-word}.hSRGPd{color:#1a73e8;cursor:pointer;font-weight:500;text-decoration:none}.Bz112c-W3lGp{height:16px;width:16px}.Bz112c-E3DyYd{height:20px;width:20px}.Bz112c-r9oPif{height:24px;width:24px}.Bz112c-u2z5K{height:36px;width:36px}.Bz112c-uaxL4e{-webkit-border-radius:10px;border-radius:10px}.LgbsSe-Bz112c{display:block}.S9gUrf-YoZ4jf,.S9gUrf-YoZ4jf *{border:none;margin:0;padding:0}.fFW7wc-ibnC6b>.aZ2wEe>div{border-color:#4285f4}.P1ekSe-ZMv3u{-webkit-transition:height linear 200ms;-webkit-transition:height linear 200ms;transition:height linear 200ms}.P1ekSe-ZMv3u>div:nth-child(1){background-color:#1a73e8!important;-webkit-transition:width linear 300ms;-webkit-transition:width linear 300ms;transition:width linear 300ms}.P1ekSe-ZMv3u>div:nth-child(2),.P1ekSe-ZMv3u>div:nth-child(3){background-image:linear-gradient(to right,rgba(255,255,255,.7),rgba(255,255,255,.7)),linear-gradient(to right,#1a73e8,#1a73e8)!important}.haAclf{display:inline-block}.nsm7Bb-HzV7m-LgbsSe{border-radius:4px;box-sizing:border-box;-webkit-transition:background-color 0.218s,border-color 0.218s;transition:background-color 0.218s,border-color 0.218s;-webkit-user-select:none;-webkit-appearance:none;background-color:#fff;background-image:none;border:1px solid #dadce0;color:#3c4043;cursor:pointer;font-family:\"Google Sans\",arial,sans-serif;font-size:14px;height:40px;letter-spacing:0.25px;outline:none;overflow:hidden;padding:0 12px;position:relative;text-align:center;vertical-align:middle;white-space:nowrap;width:auto}@media screen and (-ms-high-contrast:active){.nsm7Bb-HzV7m-LgbsSe{border:2px solid windowText;color:windowText}}@media screen and (preferes-contrast:more){.nsm7Bb-HzV7m-LgbsSe{color:#000}}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe{font-size:14px;height:32px;letter-spacing:0.25px;padding:0 10px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe{font-size:11px;height:20px;letter-spacing:0.3px;padding:0 8px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe{padding:0;width:40px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe{width:32px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe{width:20px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK{border-radius:20px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.pSzOP-SxQuSe{border-radius:16px}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK.purZT-SxQuSe{border-radius:10px}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc{border:none;color:#fff}.nsm7Bb-HzV7m-LgbsSe.MFS4be-v3pZbf-Ia7Qfc{background-color:#1a73e8}.nsm7Bb-HzV7m-LgbsSe.MFS4be-JaPV2b-Ia7Qfc{background-color:#202124;color:#e8eaed}@media screen and (prefers-contrast:more){.nsm7Bb-HzV7m-LgbsSe.MFS4be-JaPV2b-Ia7Qfc{color:#fff}}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:18px;margin-right:8px;min-width:18px;width:18px}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:14px;min-width:14px;width:14px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{height:10px;min-width:10px;width:10px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin-left:8px;margin-right:-4px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin:0;padding:10px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{padding:8px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c{padding:4px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-top-left-radius:3px;border-bottom-left-radius:3px;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center;-webkit-align-items:center;align-items:center;background-color:#fff;height:36px;margin-left:-10px;margin-right:12px;min-width:36px;width:36px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c,.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf .nsm7Bb-HzV7m-LgbsSe-Bz112c{margin:0;padding:0}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{height:28px;margin-left:-8px;margin-right:10px;min-width:28px;width:28px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{height:16px;margin-left:-6px;margin-right:8px;min-width:16px;width:16px}.nsm7Bb-HzV7m-LgbsSe.Bz112c-LgbsSe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-radius:3px;margin-left:2px;margin-right:0;padding:0}.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-radius:18px}.nsm7Bb-HzV7m-LgbsSe.pSzOP-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-radius:14px}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-radius:8px}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;-webkit-box-align:center;align-items:center;-webkit-flex-direction:row;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex-direction:row;-webkit-box-pack:space-between;-webkit-justify-content:space-between;justify-content:space-between;-webkit-flex-wrap:nowrap;flex-wrap:nowrap;height:100%;position:relative;width:100%}.nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX{-webkit-box-pack:center;-webkit-justify-content:center;justify-content:center}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-BPrWId{-webkit-flex-grow:1;-webkit-box-flex:1;flex-grow:1;font-family:\"Google Sans\",arial,sans-serif;font-weight:500;overflow:hidden;text-overflow:ellipsis;vertical-align:top}.nsm7Bb-HzV7m-LgbsSe.purZT-SxQuSe .nsm7Bb-HzV7m-LgbsSe-BPrWId{font-weight:300}.nsm7Bb-HzV7m-LgbsSe .oXtfBe-l4eHX .nsm7Bb-HzV7m-LgbsSe-BPrWId{-webkit-flex-grow:0;-webkit-box-flex:0;flex-grow:0}.nsm7Bb-HzV7m-LgbsSe .nsm7Bb-HzV7m-LgbsSe-MJoBVe{-webkit-transition:background-color 0.218s;transition:background-color 0.218s;bottom:0;left:0;position:absolute;right:0;top:0}.nsm7Bb-HzV7m-LgbsSe:hover,.nsm7Bb-HzV7m-LgbsSe:focus{box-shadow:none;border-color:#d2e3fc;outline:none}.nsm7Bb-HzV7m-LgbsSe:focus-within{outline:2px solid #00639b;border-color:transparent}.nsm7Bb-HzV7m-LgbsSe:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(66,133,244,.08)}.nsm7Bb-HzV7m-LgbsSe:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe,.nsm7Bb-HzV7m-LgbsSe:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(66,133,244,.1)}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:hover .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(255,255,255,.24)}.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:active .nsm7Bb-HzV7m-LgbsSe-MJoBVe,.nsm7Bb-HzV7m-LgbsSe.MFS4be-Ia7Qfc:focus .nsm7Bb-HzV7m-LgbsSe-MJoBVe{background:rgba(255,255,255,.32)}.nsm7Bb-HzV7m-LgbsSe .n1UuX-DkfjY{border-radius:50%;display:-webkit-box;display:-webkit-flex;display:flex;height:20px;margin-left:-4px;margin-right:8px;min-width:20px;width:20px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId{font-family:\"Roboto\";font-size:12px;text-align:left}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .ssJRIf,.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .fmcmS{overflow:hidden;text-overflow:ellipsis}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff{display:-webkit-box;display:-webkit-flex;display:flex;-webkit-align-items:center;-webkit-box-align:center;align-items:center;color:#5f6368;fill:#5f6368;font-size:11px;font-weight:400}.nsm7Bb-HzV7m-LgbsSe.jVeSEe.MFS4be-Ia7Qfc .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff{color:#e8eaed;fill:#e8eaed}@media screen and (prefers-contrast:more){.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff,.nsm7Bb-HzV7m-LgbsSe.jVeSEe.MFS4be-Ia7Qfc .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff{color:#000;fill:#000}}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-BPrWId .K4efff .Bz112c{height:18px;margin:-3px -3px -3px 2px;min-width:18px;width:18px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-top-left-radius:0;border-bottom-left-radius:0;border-top-right-radius:3px;border-bottom-right-radius:3px;margin-left:12px;margin-right:-10px}.nsm7Bb-HzV7m-LgbsSe.jVeSEe.JGcpL-RbRzK .nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf{border-radius:18px}.L5Fo6c-sM5MNb{border:0;display:block;left:0;position:relative;top:0}.L5Fo6c-bF1uUb{-webkit-border-radius:4px;border-radius:4px;bottom:0;cursor:pointer;left:0;position:absolute;right:0;top:0}.L5Fo6c-bF1uUb:focus{border:none;outline:none}sentinel{}</style><script src=\"https://browser.sentry-cdn.com/9.6.1/bundle.min.js\" crossorigin=\"anonymous\" integrity=\"sha384-kbRmCeIl7Uxr+vT9YhSAdguCdd4L5QPRj7jzQTanorUVVlw/Y5X9vtzVyOEHLfpH\"></script><script charset=\"utf-8\" src=\"/static/js/46.e22103d0.chunk.js\"></script><style data-emotion=\"css\"></style><link id=\"googleidentityservice\" type=\"text/css\" media=\"all\" href=\"https://accounts.google.com/gsi/style\" rel=\"stylesheet\"><meta http-equiv=\"origin-trial\" content=\"A8o5T4MyEkRZqLA9WeG2XTFdV5tsX2Prg85xyQ+RL1btVuybB1K/EQ+7JUsPK+J32oBMTnsoF9B4A+qTlL6efgQAAABweyJvcmlnaW4iOiJodHRwczovL2FjY291bnRzLmdvb2dsZS5jb206NDQzIiwiZmVhdHVyZSI6IkZlZENtQnV0dG9uTW9kZSIsImV4cGlyeSI6MTc0NDY3NTIwMCwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==\"></head><body><noscript><iframe src=\"https://www.googletagmanager.com/ns.html?id=GTM-WDV8FQD\" height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe></noscript><noscript>Oscer uses Javascript to work! Please enable Javascript in your browser to use Oscer.</noscript><noscript><img height=\"1\" width=\"1\" style=\"display:none\" src=\"https://www.facebook.com/tr?id=***************&ev=PageView&noscript=1\"/></noscript><noscript><iframe src=\"https://www.googletagmanager.com/ns.html?id=GTM-WDV8FQD\" height=\"0\" width=\"0\" style=\"display:none;visibility:hidden\"></iframe></noscript><script>window.fbAsyncInit=function(){FB.init({appId:\"2155349584599222\",autoLogAppEvents:!0,xfbml:!0,version:\"v10.0\"})}</script><script async=\"\" defer=\"defer\" crossorigin=\"anonymous\" src=\"https://connect.facebook.net/en_US/sdk.js\"></script><div id=\"app\"><div class=\"application\"><div class=\"jss137\"><div class=\"jss136\"><img src=\"/static/media/character-signup-lookdown.97b53c98.svg\" alt=\"oscer__icon__signIn\" class=\"jss138\"><a href=\"/\"><img src=\"/static/media/oscer-full-logo.9d897cff.svg\" alt=\"oscer__icon__logo\" class=\"jss173\"></a><div class=\"jss172\">WELCOME BACK!</div><div class=\"jss202\" style=\"margin-top: 25px;\"><span>sign in with one click</span></div><div class=\"jss174\"><div class=\"style_social-wrapper__GX7zl\" style=\"display: flex; align-items: center;\"><button class=\"animated-btn\" style=\"width: 40px; height: 40px; margin-right: 15px; outline: none;\"><img src=\"/static/media/facebook-signin-icon.5feb359b.svg\" alt=\"facebook_icon\"></button><div><div style=\"height: 40px;\"><div class=\"S9gUrf-YoZ4jf\" style=\"position: relative;\"><div><div tabindex=\"0\" role=\"button\" aria-labelledby=\"button-label\" class=\"nsm7Bb-HzV7m-LgbsSe Bz112c-LgbsSe hJDwNd-SxQuSe MFS4be-v3pZbf-Ia7Qfc MFS4be-Ia7Qfc JGcpL-RbRzK\"><div class=\"nsm7Bb-HzV7m-LgbsSe-MJoBVe\"></div><div class=\"nsm7Bb-HzV7m-LgbsSe-bN97Pc-sM5MNb \"><div class=\"nsm7Bb-HzV7m-LgbsSe-Bz112c-haAclf\"><div class=\"nsm7Bb-HzV7m-LgbsSe-Bz112c\"><svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" class=\"LgbsSe-Bz112c\"><g><path fill=\"#EA4335\" d=\"M24 9.5c3.54 0 6.71 1.22 9.21 3.6l6.85-6.85C35.9 2.38 30.47 0 24 0 14.62 0 6.51 5.38 2.56 13.22l7.98 6.19C12.43 13.72 17.74 9.5 24 9.5z\"></path><path fill=\"#4285F4\" d=\"M46.98 24.55c0-1.57-.15-3.09-.38-4.55H24v9.02h12.94c-.58 2.96-2.26 5.48-4.78 7.18l7.73 6c4.51-4.18 7.09-10.36 7.09-17.65z\"></path><path fill=\"#FBBC05\" d=\"M10.53 28.59c-.48-1.45-.76-2.99-.76-4.59s.27-3.14.76-4.59l-7.98-6.19C.92 16.46 0 20.12 0 24c0 3.88.92 7.54 2.56 10.78l7.97-6.19z\"></path><path fill=\"#34A853\" d=\"M24 48c6.48 0 11.93-2.13 15.89-5.81l-7.73-6c-2.15 1.45-4.92 2.3-8.16 2.3-6.26 0-11.57-4.22-13.47-9.91l-7.98 6.19C6.51 42.62 14.62 48 24 48z\"></path><path fill=\"none\" d=\"M0 0h48v48H0z\"></path></g></svg></div></div><span class=\"L6cTce\" id=\"button-label\">Sign in with Google. Opens in new tab</span></div></div></div><iframe src=\"https://accounts.google.com/gsi/button?type=icon&amp;theme=filled_blue&amp;size=large&amp;text=undefined&amp;shape=circle&amp;logo_alignment=undefined&amp;width=undefined&amp;locale=undefined&amp;click_listener=undefined&amp;is_fedcm_supported=true&amp;client_id=*************-leg6tbhd47q98nguhe33cpf68it5qmpg.apps.googleusercontent.com&amp;iframe_id=gsi_119498_459577&amp;cas=qks2bhn99lmcliYKUcR8%2F51BBD%2FdcQ2sTuakwCENZyQ\" class=\"L5Fo6c-PQbLGe\" allow=\"identity-credentials-get\" id=\"gsi_119498_459577\" title=\"Sign in with Google Button\" style=\"display: block; position: relative; top: 0px; left: 0px; height: 0px; width: 0px; border: 0px;\"></iframe></div></div></div><div class=\"MuiBackdrop-root\" aria-hidden=\"true\" style=\"opacity: 0; visibility: hidden; z-index: 100; color: rgb(255, 255, 255);\"><div class=\"MuiCircularProgress-root MuiCircularProgress-indeterminate\" role=\"progressbar\" style=\"width: 40px; height: 40px;\"><svg class=\"MuiCircularProgress-svg\" viewBox=\"22 22 44 44\"><circle class=\"MuiCircularProgress-circle MuiCircularProgress-circleIndeterminate\" cx=\"44\" cy=\"44\" r=\"20.2\" fill=\"none\" stroke-width=\"3.6\"></circle></svg></div></div></div></div><div class=\"jss202\" style=\"margin-top: 20px;\"><span>or use your email</span></div><form class=\"jss175\" autocomplete=\"off\"><div class=\"MuiFormControl-root MuiTextField-root jss176 jss177\"><div class=\"MuiInputBase-root MuiInput-root MuiInput-underline MuiInputBase-formControl MuiInput-formControl MuiInputBase-adornedStart\"><img src=\"/static/media/email-icon-dark.39d00e49.svg\" alt=\"email_icon\" class=\"jss179\"><input aria-invalid=\"false\" autocomplete=\"off\" id=\"email\" placeholder=\"Email Address\" required=\"\" type=\"text\" class=\"MuiInputBase-input MuiInput-input jss180 MuiInputBase-inputAdornedStart\" value=\"\"></div></div><div class=\"MuiFormControl-root MuiTextField-root jss176 jss177\"><div class=\"MuiInputBase-root MuiInput-root MuiInput-underline MuiInputBase-formControl MuiInput-formControl MuiInputBase-adornedStart\"><img src=\"/static/media/password-icon-dark.dcb1faca.svg\" alt=\"pwd_icon\" class=\"jss179\"><input aria-invalid=\"false\" autocomplete=\"off\" id=\"password\" placeholder=\"Password\" required=\"\" type=\"password\" class=\"MuiInputBase-input MuiInput-input jss180 MuiInputBase-inputAdornedStart\" value=\"\"></div></div><div class=\"jss181\"><label class=\"MuiFormControlLabel-root jss182\"><span class=\"MuiButtonBase-root MuiIconButton-root jss203 MuiCheckbox-root MuiCheckbox-colorPrimary MuiIconButton-colorPrimary\" aria-disabled=\"false\"><span class=\"MuiIconButton-label\"><input class=\"jss206\" name=\"checkedA\" type=\"checkbox\" data-indeterminate=\"false\" value=\"\"><svg class=\"MuiSvgIcon-root\" focusable=\"false\" viewBox=\"0 0 24 24\" aria-hidden=\"true\"><path d=\"M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z\"></path></svg></span><span class=\"MuiTouchRipple-root\"></span></span><span class=\"MuiTypography-root MuiFormControlLabel-label MuiTypography-body1\">Keep me logged in</span></label><a class=\"jss188\" href=\"/forgot_password\">Forgot Password?</a></div><button class=\"MuiButtonBase-root MuiButton-root jss183 MuiButton-contained jss184 MuiButton-containedPrimary\" tabindex=\"0\" type=\"button\"><span class=\"MuiButton-label jss185\">Sign in</span><span class=\"MuiTouchRipple-root\"></span></button></form><div class=\"jss189\">Don't have an account yet? <a class=\"jss192\" href=\"/signup\">Sign up</a></div></div></div></div></div><script src=\"https://js.stripe.com/v3/\"></script><script type=\"text/javascript\" src=\"https://static.leaddyno.com/js\"></script><script>LeadDyno.key=\"17fc4507a19d14c59e69da73e1da7fc947bae41e\",LeadDyno.recordVisit(),LeadDyno.autoWatch()</script><script>!function(e){function t(t){for(var n,c,o=t[0],d=t[1],u=t[2],i=0,s=[];i<o.length;i++)c=o[i],Object.prototype.hasOwnProperty.call(f,c)&&f[c]&&s.push(f[c][0]),f[c]=0;for(n in d)Object.prototype.hasOwnProperty.call(d,n)&&(e[n]=d[n]);for(l&&l(t);s.length;)s.shift()();return a.push.apply(a,u||[]),r()}function r(){for(var e,t=0;t<a.length;t++){for(var r=a[t],n=!0,c=1;c<r.length;c++){var d=r[c];0!==f[d]&&(n=!1)}n&&(a.splice(t--,1),e=o(o.s=r[0]))}return e}var n={},c={8:0},f={8:0},a=[];function o(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.e=function(e){var t=[];c[e]?t.push(c[e]):0!==c[e]&&{1:1,2:1,11:1,12:1,13:1,14:1,15:1,16:1,18:1,20:1,21:1,23:1,26:1,27:1,28:1}[e]&&t.push(c[e]=new Promise((function(t,r){for(var n=\"static/css/\"+({}[e]||e)+\".\"+{0:\"31d6cfe0\",1:\"06d795b9\",2:\"51447820\",3:\"31d6cfe0\",4:\"31d6cfe0\",5:\"31d6cfe0\",6:\"31d6cfe0\",10:\"31d6cfe0\",11:\"a7365119\",12:\"72f0d8d8\",13:\"c8eba2e4\",14:\"d25080b5\",15:\"52c938f3\",16:\"c8436f05\",17:\"31d6cfe0\",18:\"3bab3b69\",19:\"31d6cfe0\",20:\"a70dd3c1\",21:\"17ac374a\",22:\"31d6cfe0\",23:\"e157a69a\",24:\"31d6cfe0\",25:\"31d6cfe0\",26:\"51447820\",27:\"c2de46b5\",28:\"6a4cd27a\",29:\"31d6cfe0\",30:\"31d6cfe0\",31:\"31d6cfe0\",32:\"31d6cfe0\",33:\"31d6cfe0\",34:\"31d6cfe0\",35:\"31d6cfe0\",36:\"31d6cfe0\",37:\"31d6cfe0\",38:\"31d6cfe0\",39:\"31d6cfe0\",40:\"31d6cfe0\",41:\"31d6cfe0\",42:\"31d6cfe0\",43:\"31d6cfe0\",44:\"31d6cfe0\",45:\"31d6cfe0\",46:\"31d6cfe0\",47:\"31d6cfe0\",48:\"31d6cfe0\",49:\"31d6cfe0\",50:\"31d6cfe0\"}[e]+\".chunk.css\",f=o.p+n,a=document.getElementsByTagName(\"link\"),d=0;d<a.length;d++){var u=(l=a[d]).getAttribute(\"data-href\")||l.getAttribute(\"href\");if(\"stylesheet\"===l.rel&&(u===n||u===f))return t()}var i=document.getElementsByTagName(\"style\");for(d=0;d<i.length;d++){var l;if((u=(l=i[d]).getAttribute(\"data-href\"))===n||u===f)return t()}var s=document.createElement(\"link\");s.rel=\"stylesheet\",s.type=\"text/css\",s.onload=t,s.onerror=function(t){var n=t&&t.target&&t.target.src||f,a=new Error(\"Loading CSS chunk \"+e+\" failed.\\n(\"+n+\")\");a.code=\"CSS_CHUNK_LOAD_FAILED\",a.request=n,delete c[e],s.parentNode.removeChild(s),r(a)},s.href=f,document.getElementsByTagName(\"head\")[0].appendChild(s)})).then((function(){c[e]=0})));var r=f[e];if(0!==r)if(r)t.push(r[2]);else{var n=new Promise((function(t,n){r=f[e]=[t,n]}));t.push(r[2]=n);var a,d=document.createElement(\"script\");d.charset=\"utf-8\",d.timeout=120,o.nc&&d.setAttribute(\"nonce\",o.nc),d.src=function(e){return o.p+\"static/js/\"+({}[e]||e)+\".\"+{0:\"771fd782\",1:\"36a1edf7\",2:\"bbc391bd\",3:\"41d8a5c4\",4:\"b9ada093\",5:\"efd45f7b\",6:\"99758282\",10:\"50c0f6c9\",11:\"53abfbf2\",12:\"fb3073b3\",13:\"56b4c2b7\",14:\"19c9baf6\",15:\"97ffe365\",16:\"717a8b0d\",17:\"e899e4f1\",18:\"a5b1f0f4\",19:\"d62303cc\",20:\"e199be09\",21:\"6e474b96\",22:\"00dd7ac9\",23:\"328703ee\",24:\"4ca2fff0\",25:\"6ee7a695\",26:\"71f15046\",27:\"57207da9\",28:\"69768bf7\",29:\"f52daf26\",30:\"6930e509\",31:\"c0aeb095\",32:\"36efe60d\",33:\"506afe2e\",34:\"9ec6efc3\",35:\"07fc7c8a\",36:\"e54d8eb2\",37:\"e1c46019\",38:\"f2442de0\",39:\"84326772\",40:\"8aa6a699\",41:\"06c05e44\",42:\"e2a1777c\",43:\"e04c6eb9\",44:\"73bef6b2\",45:\"83a299b8\",46:\"e22103d0\",47:\"78d5b1b4\",48:\"d1c35da0\",49:\"0f02c10d\",50:\"c7ed7ce0\"}[e]+\".chunk.js\"}(e);var u=new Error;a=function(t){d.onerror=d.onload=null,clearTimeout(i);var r=f[e];if(0!==r){if(r){var n=t&&(\"load\"===t.type?\"missing\":t.type),c=t&&t.target&&t.target.src;u.message=\"Loading chunk \"+e+\" failed.\\n(\"+n+\": \"+c+\")\",u.name=\"ChunkLoadError\",u.type=n,u.request=c,r[1](u)}f[e]=void 0}};var i=setTimeout((function(){a({type:\"timeout\",target:d})}),12e4);d.onerror=d.onload=a,document.head.appendChild(d)}return Promise.all(t)},o.m=e,o.c=n,o.d=function(e,t,r){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},o.r=function(e){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(e,\"__esModule\",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&\"object\"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(o.r(r),Object.defineProperty(r,\"default\",{enumerable:!0,value:e}),2&t&&\"string\"!=typeof e)for(var n in e)o.d(r,n,function(t){return e[t]}.bind(null,n));return r},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,\"a\",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p=\"/\",o.oe=function(e){throw console.error(e),e};var d=this.webpackJsonposcer_web_front=this.webpackJsonposcer_web_front||[],u=d.push.bind(d);d.push=t,d=d.slice();for(var i=0;i<d.length;i++)t(d[i]);var l=u;r()}([])</script><script src=\"/static/js/9.2da4b7cc.chunk.js\"></script><script src=\"/static/js/main.6edcd9cc.chunk.js\"></script><div><div style=\"position: fixed; inset: 0px auto auto 50%; transform: translate(-50%, 0%); z-index: 100;\"><div></div></div></div><script src=\"https://accounts.google.com/gsi/client\" async=\"\" defer=\"\"></script><script src=\"https://fast.wistia.com/assets/external/E-v1.js\" async=\"\"></script><div id=\"fb-root\" class=\" fb_reset\"><div style=\"position: absolute; top: -10000px; width: 0px; height: 0px;\"><div></div></div></div><iframe name=\"__privateStripeMetricsController1610\" frameborder=\"0\" allowtransparency=\"true\" scrolling=\"no\" role=\"presentation\" allow=\"payment *\" src=\"https://js.stripe.com/v3/m-outer-3437aaddcdf6922d623e172c2d6f9278.html#url=https%3A%2F%2Fwww.oscer.ai%2F&amp;title=Oscer%20%7C%20Learn%20Clinical%20Reasoning%20-%20Free%20Sign%20Up&amp;referrer=&amp;muid=NA&amp;sid=NA&amp;version=6&amp;preview=false&amp;__shared_params__[version]=v3\" aria-hidden=\"true\" tabindex=\"-1\" style=\"border: none !important; margin: 0px !important; padding: 0px !important; width: 1px !important; min-width: 100% !important; overflow: hidden !important; display: block !important; visibility: hidden !important; position: fixed !important; height: 1px !important; pointer-events: none !important; user-select: none !important;\"></iframe><style id=\"wistia_22_style\" type=\"text/css\" class=\"wistia_injected_style\">\n", "@font-face {\n", "font-family: 'WistiaPlayerInterNumbersSemiBold';\n", "font-feature-settings: 'tnum' 1;\n", "src: url(data:application/x-font-woff;charset=utf-8;base64,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);\n", "}\n", "</style><style class=\"gleap-styles\">\n", "    .gleap-font, .gleap-font * {\n", "      font-style: normal;\n", "      font-variant-caps: normal;\n", "      font-variant-ligatures: normal;\n", "      font-variant-numeric: normal;\n", "      font-variant-east-asian: normal;\n", "      font-weight: normal;\n", "      font-stretch: normal;\n", "      font-size: 100%;\n", "      line-height: 1;\n", "      font-family: system-ui, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\";\n", "    }\n", "    .gleap-frame-container {\n", "      right: 20px;\n", "      bottom: 89px;\n", "      width: calc(100% - 40px);\n", "      max-width: 400px;\n", "      position: fixed;\n", "      z-index: 2147483631;\n", "      visibility: visible;\n", "      box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.16);\n", "      border-radius: 18px;\n", "      overflow: hidden;\n", "      animation-duration: .3s;\n", "      animation-fill-mode: both;\n", "      animation-name: gleapFadeInUp;\n", "      user-select: none;\n", "      pointer-events: none;\n", "      transition: max-width 0.3s ease-out;\n", "    }\n", "\n", "    [dir=rtl].gleap-frame-container {\n", "      right: auto;\n", "      left: 20px;\n", "      bottom: 89px;\n", "    }\n", "\n", "    .gleap-frame-container--loading iframe {\n", "      opacity: 0;\n", "    }\n", "\n", "    .gleap-frame-container--loading::before {\n", "      content: \" \";\n", "      position: fixed;\n", "      top: 0px;\n", "      left: 0px;\n", "      right: 0px;\n", "      height: 100%;\n", "      max-height: 380px;\n", "      background: linear-gradient(\n", "        130deg,\n", "        #0039a6 0%,\n", "        #1e5cc9 100%\n", "      );\n", "    }\n", "    \n", "    .gleap-frame-container--loading::after {\n", "      content: \" \";\n", "      position: fixed;\n", "      top: 0px;\n", "      left: 0px;\n", "      right: 0px;\n", "      height: 100%;\n", "      height: 100%;\n", "      max-height: 380px;\n", "      background: linear-gradient(\n", "        180deg,\n", "        transparent 60%,\n", "        #FFFFFF1A 70%,\n", "        #FFFFFF 100%\n", "      );\n", "    }\n", "\n", "    .gleap-frame-container--loading-nogradient::before {\n", "      max-height: 340px;\n", "      background: #1e5cc9 !important;\n", "    }\n", "\n", "    .gleap-frame-container--loading-nofade::after {\n", "      display: none !important;\n", "    }\n", "\n", "    .gleap-frame-container--survey {\n", "      bottom: 20px !important;\n", "    }\n", "\n", "    .gleap-frame-container--extended {\n", "      max-width: 690px !important;\n", "    }\n", "\n", "    .gleap-frame-container--survey-full {\n", "      position: fixed;\n", "      top: 0 !important;\n", "      left: 0 !important;\n", "      bottom: 0 !important;\n", "      right: 0 !important;\n", "      width: 100vw !important;\n", "      max-width: 100vw !important;\n", "      height: 100vh !important;\n", "      background-color: rgba(0, 0, 0, 0.5);\n", "      backdrop-filter: blur(6px);\n", "      display: flex !important;\n", "      justify-content: center !important;\n", "      align-items: center !important;\n", "      max-height: 100vh !important;\n", "      border-radius: 0 !important;\n", "      animation-name: none !important;\n", "    }\n", "\n", "    .gleap-frame-container--survey-full .gleap-frame-container-inner {\n", "      max-width: 640px !important;\n", "      width: calc(100% - 24px);\n", "      border-radius: 18px;\n", "      overflow: hidden;\n", "    }\n", "\n", "    .gleap-frame-container--classic {\n", "      right: 20px;\n", "      bottom: 20px;\n", "    }\n", "\n", "    [dir=rtl].gleap-frame-container--classic {\n", "      right: auto;\n", "      left: 20px;\n", "      bottom: 20px;\n", "    }\n", "\n", "    .gleap-frame-container--no-button {\n", "      bottom: 20px;\n", "    }\n", "\n", "    [dir=rtl].gleap-frame-container--classic-left {\n", "      bottom: 20px;\n", "    }\n", "\n", "    .gleap-frame-container--classic-left {\n", "      right: auto;\n", "      left: 20px;\n", "      bottom: 20px;\n", "    }\n", "\n", "    [dir=rtl].gleap-frame-container--classic-left {\n", "      left: auto;\n", "      right: 20px;\n", "      bottom: 20px;\n", "    }\n", "\n", "    .gleap-frame-container--modern-left {\n", "      right: auto;\n", "      left: 20px;\n", "      bottom: 89px;\n", "    }\n", "\n", "    [dir=rtl].gleap-frame-container--modern-left {\n", "      left: auto;\n", "      right: 20px;\n", "      bottom: 89px;\n", "    }\n", "\n", "    .gleap-frame-container--animate {\n", "      pointer-events: auto !important;\n", "    }\n", "\n", "    @keyframes gleapFadeInUp {\n", "      from {\n", "          opacity: 0;\n", "          transform: translate3d(0, 100%, 0);\n", "      }\n", "      to {\n", "          opacity: 1;\n", "          transform: translate3d(0, 0, 0);\n", "      }\n", "    }\n", "\n", "    @keyframes gleapFadeInUpMobile {\n", "      from {\n", "          opacity: 0;\n", "          transform: translate3d(0, 10%, 0);\n", "      }\n", "      to {\n", "          opacity: 1;\n", "          transform: translate3d(0, 0, 0);\n", "      }\n", "    }\n", "\n", "    .gleap-notification-container {\n", "      position: fixed;\n", "      bottom: 20px;\n", "      right: 20px;\n", "      z-index: 2147483630;\n", "      display: flex;\n", "      flex-direction: column;\n", "      align-items: flex-end;\n", "      width: 100%;\n", "      max-width: min(340px, 80vw);\n", "    }\n", "\n", "    .gleap-notification-container--left {\n", "      left: 24px;\n", "      right: initial !important;\n", "    }\n", "\n", "    .gleap-notification-container--no-button {\n", "      bottom: 20px;\n", "    }\n", "\n", "    .gleap-notification-item {\n", "      animation-duration: 0.7s;\n", "      animation-fill-mode: both;\n", "      animation-name: bbFadeInOpacity;\n", "    }\n", "\n", "    .gleap-notification-close {\n", "      border-radius: 100%;\n", "      width: 28px;\n", "      height: 28px;\n", "      background-color: #878787;\n", "      display: flex;\n", "      justify-content: center;\n", "      align-items: center;\n", "      margin-bottom: 8px;\n", "      cursor: pointer;\n", "      visibility: hidden;\n", "      pointer-events: none;\n", "    }\n", "\n", "    .gleap-notification-container:hover .gleap-notification-close {\n", "      visibility: visible;\n", "      pointer-events: auto;\n", "      animation-duration: 0.7s;\n", "      animation-fill-mode: both;\n", "      animation-name: bbFadeInOpacity;\n", "    }\n", "\n", "    @media only screen and (max-width: 450px) {\n", "      .gleap-notification-close {\n", "        visibility: visible;\n", "        pointer-events: auto;\n", "        animation-duration: 0.7s;\n", "        animation-fill-mode: both;\n", "        animation-name: bbFadeInOpacity;\n", "      }\n", "    }\n", "\n", "    .gleap-notification-close svg {\n", "      width: 45%;\n", "      height: 45%;\n", "      object-fit: contain;\n", "      fill: #FFFFFF;\n", "    }\n", "\n", "    .gleap-notification-item-news {\n", "      width: 100%;\n", "      cursor: pointer;\n", "    }\n", "\n", "    .gleap-notification-item-news-content {\n", "      align-items: flex-start;\n", "      display: flex;\n", "      flex-direction: column;\n", "      padding: 15px;\n", "    }\n", "\n", "    .gleap-notification-item-news-sender {\n", "      display: flex;\n", "      align-items: center;\n", "      color: #878787;\n", "      font-size: 14px;\n", "      font-weight: 400;\n", "    }\n", "\n", "    .gleap-notification-item-news-content-title {\n", "      font-size: 14px;\n", "      font-weight: 500;\n", "      line-height: 18px;\n", "      margin-bottom: 6px;\n", "      max-width: 100%;\n", "      overflow: hidden;\n", "      text-overflow: ellipsis;\n", "      white-space: nowrap;\n", "      color: #000000;\n", "    }\n", "\n", "    .gleap-notification-item-news-sender img {\n", "      border-radius: 100%;\n", "      height: 20px;\n", "      margin-right: 8px;\n", "      object-fit: cover;\n", "      width: 20px;\n", "    }\n", "\n", "    .gleap-notification-item-news-container {\n", "      display: flex;\n", "      animation: fadeIn;\n", "      animation-duration: .45s;\n", "      background-color: #FFFFFF;\n", "      border-radius: #878787;\n", "      box-sizing: border-box;\n", "      cursor: pointer;\n", "      flex-direction: column;\n", "      overflow: hidden;\n", "      box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.2);\n", "      border-radius: 11px;\n", "      margin-bottom: 12px;\n", "    }\n", "\n", "    .gleap-notification-item-news-image {\n", "      background-color: #878787;\n", "      height: 170px;\n", "      object-fit: cover;\n", "      width: 100%;\n", "    }\n", "\n", "    .gleap-notification-item-news:hover .gleap-notification-item-news-content-title {\n", "      color: #1e5cc9;\n", "    }\n", "\n", "    .gleap-notification-item {\n", "      display: flex;\n", "      align-items: flex-end;\n", "      cursor: pointer;\n", "    }\n", "\n", "    .gleap-notification-item img {\n", "      width: 32px;\n", "      height: 32px;\n", "      min-width: 32px;\n", "      border-radius: 100%;\n", "      object-fit: cover;\n", "      margin-right: 8px;\n", "      margin-bottom: 12px;\n", "      cursor: pointer;\n", "    }\n", "\n", "    .gleap-notification-item-container {\n", "      box-shadow: 0px 5px 30px rgba(0, 0, 0, 0.2);\n", "      border-radius: 11px;\n", "      border-bottom-left-radius: 0px;\n", "      padding: 20px;\n", "      background-color: #FFFFFF;\n", "      margin-bottom: 12px;\n", "      cursor: pointer;\n", "      font-size: 15px;\n", "      line-height: 21px;\n", "      color: #000000;\n", "      position: relative;\n", "    }\n", "\n", "    .gleap-notification-item-container::after {\n", "      content: \" \";\n", "      position: absolute;\n", "      bottom: 0px;\n", "      width: 0px;\n", "      height: 0px;\n", "      left: -6px;\n", "      border-style: solid;\n", "      border-width: 0px 0px 10px 6px;\n", "      border-color: transparent transparent #FFFFFF;\n", "    }\n", "\n", "    .gleap-notification-item-sender {\n", "      color: #878787;\n", "      line-height: 20px;\n", "    }\n", "\n", "    .gleap-notification-item-content {\n", "      line-height: 20px;\n", "      color: #000000;\n", "      margin-top: 4px;\n", "      min-width: min(200px, 50vw);\n", "      word-wrap: break-word;\n", "      word-break: break-word;\n", "    }\n", "\n", "    .gleap-frame-container-inner {\n", "      position: relative;\n", "      width: 100%;\n", "      height: calc(100vh - 150px);\n", "      max-height: 660px;\n", "    }\n", "\n", "    .gleap-frame-container-inner:before {\n", "      content: \" \";\n", "      position: absolute;\n", "      width: 100%;\n", "      height: calc(100% - 18px);\n", "      top: 18px;\n", "      background-color: #FFFFFF;\n", "      z-index: -1;\n", "    }\n", "    \n", "    .gleap-frame-container iframe {\n", "      height: 100% !important;\n", "      width: 100% !important;\n", "      max-width: 100% !important;\n", "      display: block;\n", "      pointer-events: auto;\n", "    }\n", "    \n", "    .gleap-frame-container--hidden {\n", "      display: none !important;\n", "      pointer-events: none;\n", "      animation: none !important;\n", "    }\n", "    \n", "    .bb-feedback-button {\n", "      margin: 0px;\n", "      position: fixed;\n", "      bottom: 20px;\n", "      right: 20px;\n", "      border-radius: 30px;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "      background-color: transparent;\n", "      color: #000000;\n", "      z-index: 2147483630;\n", "      box-sizing: border-box;\n", "      display: flex;\n", "      align-items: center;\n", "      padding: 0px;\n", "    }\n", "\n", "    [dir=rtl].bb-feedback-button {\n", "      bottom: 20px;\n", "      right: auto;\n", "      left: 20px;\n", "    }\n", "    \n", "    .bb-feedback-button--bottomleft {\n", "      bottom: 20px;\n", "      right: auto;\n", "      left: 20px;\n", "    }\n", "\n", "    [dir=rtl].bb-feedback-button--bottomleft {\n", "      bottom: 20px;\n", "      right: 20px;\n", "      left: auto;\n", "    }\n", "    \n", "    .bb-feedback-button--disabled {\n", "      display: none !important;\n", "    }\n", "\n", "    .bb-feedback-button--hidden {\n", "      display: none !important;\n", "    }\n", "    \n", "    .bb-feedback-button-text {\n", "      padding: 8px 12px;\n", "      display: flex;\n", "      flex-direction: column;\n", "      align-items: flex-start;\n", "      justify-content: center;\n", "      background-color: #fff;\n", "      border-radius: 8px;\n", "      box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.15);\n", "      position: relative;\n", "      z-index: 99;\n", "    }\n", "    \n", "    .bb-feedback-button-text:before {\n", "      content: \"\";\n", "      position: absolute;\n", "      box-shadow: rgba(0, 0, 0, 0.04) 6px 6px 5px;\n", "      transform: rotate(315deg);\n", "      bottom: 16px;\n", "      right: -4px;\n", "      border-width: 10px;\n", "      border-style: solid;\n", "      border-color: transparent #fff #fff transparent;\n", "    }\n", "    \n", "    .bb-feedback-button--bottomleft .bb-feedback-button-text:before {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-feedback-button-text:after {\n", "      content: \"\";\n", "      position: absolute;\n", "      bottom: 12px;\n", "      right: 0px;\n", "      background-color: #fff;\n", "      width: 5px;\n", "      height: 30px;\n", "    }\n", "    \n", "    .bb-feedback-button-text-title {\n", "      font-family: sans-serif;\n", "      font-size: 14px;\n", "      color: #666;\n", "      line-height: 18px;\n", "      max-width: 220px;\n", "    }\n", "    \n", "    .bb-feedback-button-text-title b {\n", "      color: #000000;\n", "      font-weight: 600;\n", "    }\n", "\n", "    .bb-notification-bubble {\n", "      position: absolute;\n", "      top: -6px;\n", "      right: -6px;\n", "      min-width: 22px;\n", "      padding: 0px 4px;\n", "      height: 22px;\n", "      border-radius: 22px;\n", "      background-color: red;\n", "      color: #fff;\n", "      font-size: 12px;\n", "      font-family: sans-serif;\n", "      text-align: center;\n", "      line-height: 22px;\n", "    }\n", "\n", "    .bb-notification-bubble--hidden {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-feedback-button-icon {\n", "      width: 54px;\n", "      height: 54px;\n", "      border-radius: 54px;\n", "      background-color: #485bff;\n", "      transition: box-shadow, transform 0.2s ease-in-out;\n", "      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.15), 0px 0px 20px rgba(0, 0, 0, 0.1);\n", "      position: relative;\n", "    }\n", "    \n", "    .bb-feedback-button-classic {\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "      top: 50%;\n", "      right: 0px;\n", "      position: fixed;\n", "      transform: rotate(-90deg) translate(50%, -50%);\n", "      transform-origin: 100% 50%;\n", "      padding: 9px 20px;\n", "      text-align: center;\n", "      background-color: #485bff;\n", "      border-top-left-radius: 8px;\n", "      border-top-right-radius: 8px;\n", "      font-family: sans-serif;\n", "      font-size: 16px;\n", "      color: #fff;\n", "      box-shadow: 0px 0px 14px 0px rgba(0, 0, 0, 0.25);\n", "    }\n", "\n", "    [dir=rtl].bb-feedback-button .bb-feedback-button-classic {\n", "      top: 50%;\n", "      left: 0px;\n", "      right: auto;\n", "      transform: rotate(90deg) translate(-50%, -100%);\n", "      transform-origin: 0% 0%;\n", "    }\n", "    \n", "    .bb-feedback-button-classic--left {\n", "      top: 50%;\n", "      left: 0px;\n", "      right: auto;\n", "      transform: rotate(90deg) translate(-50%, -100%);\n", "      transform-origin: 0% 0%;\n", "    }\n", "\n", "    [dir=rtl].bb-feedback-button .bb-feedback-button-classic--left {\n", "      top: 50%;\n", "      right: 0px;\n", "      left: auto;\n", "      transform: rotate(-90deg) translate(50%, -50%);\n", "      transform-origin: 100% 50%;\n", "    }\n", "    \n", "    .bb-feedback-button-classic--bottom {\n", "      top: auto;\n", "      bottom: 0px;\n", "      transform: none;\n", "      right: 20px;\n", "      left: auto;\n", "    }\n", "    \n", "    [dir=rtl].bb-feedback-button .bb-feedback-button-classic--bottom {\n", "      top: auto;\n", "      bottom: 0px;\n", "      transform: none;\n", "      left: 20px;\n", "      right: auto;\n", "    }\n", "\n", "    .bb-feedback-button--classic-button-style {\n", "      animation-duration: 0.2s;\n", "      animation-fill-mode: both;\n", "      animation-name: bbFadeInOpacity;\n", "    }\n", "    \n", "    .bb-feedback-button--open.bb-feedback-button--classic-button-style {\n", "      animation-duration: 0.2s;\n", "      animation-fill-mode: both;\n", "      animation-name: bbFadeOutRight;\n", "    }\n", "    \n", "    .bb-feedback-button .bb-logo-logo {\n", "      position: absolute;\n", "      width: 34px;\n", "      height: 34px;\n", "      top: 10px;\n", "      left: 10px;\n", "      object-fit: contain;\n", "      animation-duration: 0.3s;\n", "      animation-fill-mode: both;\n", "      animation-name: bb<PERSON><PERSON>In;\n", "    }\n", "    \n", "    .bb-feedback-button .bb-logo-arrowdown {\n", "      position: absolute;\n", "      width: 16px;\n", "      height: 16px;\n", "      top: 19px;\n", "      left: 19px;\n", "      object-fit: contain;\n", "      animation-duration: 0.3s;\n", "      animation-fill-mode: both;\n", "    }\n", "    \n", "    .bb-feedback-button .bb-logo-arrowdown {\n", "      animation-name: bb<PERSON><PERSON><PERSON><PERSON>;\n", "    }\n", "    \n", "    .bb-feedback-button--open .bb-logo-arrowdown {\n", "      animation-name: bb<PERSON><PERSON>In;\n", "    }\n", "    \n", "    .bb-feedback-button--open .bb-logo-logo {\n", "      animation-name: bb<PERSON><PERSON><PERSON><PERSON>;\n", "    }\n", "    \n", "    .bb-feedback-button-icon:hover {\n", "      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.25), 0px 0px 20px rgba(0, 0, 0, 0.2);\n", "      transform: scale(1.1);\n", "    }\n", "    \n", "    .bb-feedback-button--open .bb-feedback-button-text {\n", "      animation-name: bbFadeOutDown;\n", "    }\n", "    \n", "    .bb-feedback-button--open .bb-feedback-button-icon {\n", "      display: flex;\n", "    }\n", "    \n", "    .bb-capture-svg {\n", "      position: fixed;\n", "      z-index: 2147483614;\n", "      top: 0px;\n", "      left: 0px;\n", "      right: 0px;\n", "      width: 100%;\n", "      height: 100%;\n", "      padding: 0px;\n", "      margin: 0px;\n", "      cursor: crosshair;\n", "    }\n", "\n", "    .bb-capture-svg--preview {\n", "      cursor: auto !important;\n", "    }\n", "    \n", "    .bb-rec-on-circle {\n", "      animation-name: bbRecIconFade;\n", "      animation-duration: 2s;\n", "      animation-iteration-count: infinite;\n", "      animation-direction: alternate;\n", "    }\n", "    \n", "    .bb-rec-on-cont {\n", "      animation-name: bbRecIconContFade;\n", "      animation-duration: 2s;\n", "      animation-iteration-count: infinite;\n", "      animation-direction: alternate;\n", "    }\n", "    \n", "    .bb-capture-editor-drag-info {\n", "      position: fixed;\n", "      top: -200px;\n", "      left: 0px;\n", "      z-index: 2147483616;\n", "      transition: opacity 0.3s ease-in-out;\n", "    }\n", "    \n", "    .bb-capture-editor-drag-info svg {\n", "      width: 24px;\n", "      height: 24px;\n", "    }\n", "    \n", "    .bb-capture-editor-borderlayer {\n", "      position: fixed;\n", "      top: 0px;\n", "      left: 0px;\n", "      width: 100vw;\n", "      height: 100vh;\n", "      border: 4px solid #1e5cc9;\n", "      cursor: crosshair;\n", "      z-index: 2147483610;\n", "      box-sizing: border-box;\n", "      pointer-events: none;\n", "    }\n", "    \n", "    .bb-capture-editor-notrecording .bb-capture-editor-borderlayer {\n", "      background-color: rgba(0, 0, 0, 0.8);\n", "    }\n", "    \n", "    .bb-capture-editor-recording .bb-capture-dismiss {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-editor-item-inactive {\n", "      opacity: 0.3;\n", "      cursor: not-allowed !important;\n", "    }\n", "    \n", "    .bb-capture-editor-notrecording .bb-capture-toolbar-drawingitem {\n", "      opacity: 0.3;\n", "      cursor: not-allowed !important;\n", "    }\n", "    \n", "    .bb-capture-editor-notrecording .bb-capture-editor-drag-info {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-editor-notrecording .bb-capture-svg {\n", "      pointer-events: none !important;\n", "    }\n", "    \n", "    .bb-capture-toolbar {\n", "      position: fixed;\n", "      top: 20px;\n", "      left: 50%;\n", "      transform: translateX(-50%);\n", "      z-index: 2147483618;\n", "      background-color: #fff;\n", "      padding: 5px;\n", "      display: flex;\n", "      align-items: center;\n", "      border-radius: 8px;\n", "      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.15), 0px 0px 20px rgba(0, 0, 0, 0.1);\n", "      transition: opacity 0.3s ease-in-out;\n", "    }\n", "    \n", "    .bb-capture-dismiss {\n", "      position: fixed;\n", "      top: 0px;\n", "      right: 0px;\n", "      z-index: 2147483618;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "      width: 36px;\n", "      height: 36px;\n", "      display: flex;\n", "      justify-content: center;\n", "      align-items: center;\n", "      background-color: #1e5cc9;\n", "      border-bottom-left-radius: 5px;\n", "    }\n", "\n", "    [dir=rtl] .bb-capture-dismiss {\n", "      top: 0px;\n", "      left: 0px;\n", "      right: auto;\n", "      border-bottom-left-radius: 0px !important;\n", "      border-bottom-right-radius: 5px;\n", "    }\n", "    \n", "    .bb-capture-dismiss svg path {\n", "      fill: #ffffff;\n", "    }\n", "    \n", "    .bb-capture-dismiss svg {\n", "      width: 20px;\n", "      height: 20px;\n", "      object-fit: contain;\n", "    }\n", "    \n", "    .bb-capture-button-next {\n", "      font-family: sans-serif;\n", "      box-sizing: border-box;\n", "      font-weight: 600;\n", "      text-align: center;\n", "      width: auto;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "      margin: 0px;\n", "      line-height: 36px;\n", "      padding: 0px 12px;\n", "      font-size: 15px;\n", "      margin-left: 12px;\n", "    }\n", "\n", "    [dir=rtl].bb-capture-editor .bb-capture-button-next {\n", "      margin-left: auto;\n", "      margin-right: 12px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-spacer {\n", "      width: 1px;\n", "      height: 38px;\n", "      min-width: 1px;\n", "      margin: 0px 5px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item {\n", "      width: 42px;\n", "      height: 38px;\n", "      min-width: 42px;\n", "      display: flex;\n", "      align-items: center;\n", "      justify-content: center;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "      position: relative;\n", "      margin-right: 5px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item svg {\n", "      width: 23px;\n", "      height: 23px;\n", "      object-fit: contain;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-selectedcolor {\n", "      border-radius: 100%;\n", "      width: 20px;\n", "      height: 20px;\n", "      background-color: #db4035;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item[data-type=\"undo\"] svg {\n", "      width: 18px;\n", "      height: 18px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item[data-active=\"true\"] {\n", "      position: relative;\n", "    }\n", "    \n", "    .bb-capture-preview {\n", "      display: none;\n", "      background-color: rgba(0, 0, 0, 0.6);\n", "      position: fixed;\n", "      top: 0px;\n", "      left: 0px;\n", "      width: 100vw;\n", "      height: 100vh;\n", "      justify-content: center;\n", "      align-items: center;\n", "      z-index: 2147483620;\n", "    }\n", "    \n", "    .bb-capture-preview-inner {\n", "      background-color: #fff;\n", "      padding: 0px;\n", "      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.15), 0px 0px 20px rgba(0, 0, 0, 0.1);\n", "      border-radius: 8px;\n", "      flex-direction: column;\n", "      max-width: 640px;\n", "      width: 100%;\n", "      margin: 20px;\n", "    }\n", "    \n", "    .bb-capture-preview-inner video {\n", "      border-radius: 8px 8px 0px 0px;\n", "      display: block;\n", "      border: 0px;\n", "      outline: none;\n", "      width: 100%;\n", "      max-height: 60vh;\n", "    }\n", "    \n", "    .bb-capture-preview-buttons {\n", "      display: flex;\n", "      justify-content: space-between;\n", "      padding: 14px;\n", "    }\n", "    \n", "    .bb-capture-preview-retrybutton {\n", "      font-family: sans-serif;\n", "      border-radius: 21px;\n", "      box-sizing: border-box;\n", "      padding: 12px 26px;\n", "      font-size: 16px;\n", "      line-height: 19px;\n", "      font-weight: 600;\n", "      text-align: center;\n", "      margin-top: 0px;\n", "      margin-bottom: 0px;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "    }\n", "    \n", "    .bb-capture-preview-sendbutton {\n", "      font-family: sans-serif;\n", "      border-radius: 21px;\n", "      box-sizing: border-box;\n", "      padding: 12px 26px;\n", "      font-size: 16px;\n", "      line-height: 19px;\n", "      font-weight: 600;\n", "      text-align: center;\n", "      margin-top: 0px;\n", "      margin-bottom: 0px;\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "    }\n", "    \n", "    .bb-capture-preview-retrybutton:hover,\n", "    .bb-capture-preview-sendbutton:hover {\n", "      opacity: 0.9;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording {\n", "      margin-right: 0px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording svg {\n", "      width: 33px;\n", "      height: 33px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-colorpicker {\n", "      position: fixed;\n", "      top: 70px;\n", "      left: 50%;\n", "      transform: translateX(-50%);\n", "      z-index: 2147483618;\n", "      background-color: #fff;\n", "      display: none;\n", "      padding: 10px;\n", "      box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.15), 0px 0px 20px rgba(0, 0, 0, 0.1);\n", "      border-radius: 8px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-color {\n", "      width: 20px;\n", "      height: 20px;\n", "      border-radius: 100%;\n", "      margin-right: 12px;\n", "      box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.15);\n", "      cursor: pointer;\n", "      -webkit-tap-highlight-color: transparent;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-color:hover {\n", "      box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.25);\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-color:last-of-type {\n", "      margin-right: 0px;\n", "    }\n", "    \n", "    [dir=rtl].bb-capture-editor .bb-capture-toolbar-item-color {\n", "      margin-right: auto;\n", "      margin-left: 12px;\n", "    }\n", "    \n", "    [dir=rtl].bb-capture-editor .bb-capture-toolbar-item-color:last-of-type {\n", "      margin-right: auto;\n", "      margin-left: 0px;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording[data-active=\"true\"] svg:first-of-type {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording[data-active=\"true\"] svg:nth-of-type(2) {\n", "      display: block;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording[data-active=\"false\"] svg:first-of-type {\n", "      display: block;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-recording[data-active=\"false\"] svg:nth-of-type(2) {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item--active {\n", "      background-color: #eee;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item:hover svg {\n", "      opacity: 1;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item--active {\n", "      background-color: #f8f8f8;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item--active svg {\n", "      opacity: 1;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item--inactivecross::before {\n", "      content: \"\";\n", "      position: absolute;\n", "      top: 0;\n", "      right: 0;\n", "      bottom: 0;\n", "      left: 0;\n", "      height: 3px;\n", "      width: 26px;\n", "      margin: auto;\n", "      border-radius: 4px;\n", "      background-color: #e80000;\n", "      transform: rotate(45deg);\n", "    }\n", "    \n", "    .bb-capture-toolbar-item--inactivecross svg {\n", "      fill: #eee;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item-timer {\n", "      text-align: left;\n", "      line-height: 32px;\n", "      font-size: 14px;\n", "      font-family: sans-serif;\n", "      margin: 5px;\n", "      min-width: 40px;\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item .bb-tooltip {\n", "      background-color: #555;\n", "      color: #fff;\n", "      visibility: hidden;\n", "      font-size: 14px;\n", "      font-family: sans-serif;\n", "      text-align: center;\n", "      padding: 5px 10px;\n", "      position: absolute;\n", "      z-index: 1;\n", "      top: 45px;\n", "      left: 0px;\n", "      transform: translateX(calc(-50% + 21px));\n", "      opacity: 0;\n", "      transition: opacity 0.3s;\n", "      white-space: nowrap;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item .bb-tooltip::after {\n", "      content: \"\";\n", "      position: absolute;\n", "      bottom: 100%;\n", "      left: 50%;\n", "      margin-left: -5px;\n", "      border-width: 5px;\n", "      border-style: solid;\n", "      transform: rotate(180deg);\n", "      border-color: #555 transparent transparent transparent;\n", "    }\n", "    \n", "    .bb-capture-toolbar-item:hover .bb-tooltip {\n", "      visibility: visible;\n", "      opacity: 1;\n", "    }\n", "    \n", "    .bb-capture-options {\n", "      display: none;\n", "    }\n", "    \n", "    .bb-capture-options--active {\n", "      display: flex;\n", "    }\n", "\n", "    @keyframes bbFadeOutRight {\n", "      from {\n", "        opacity: 1;\n", "      }\n", "    \n", "      to {\n", "        opacity: 0;\n", "      }\n", "    }\n", "    \n", "    @keyframes bbFadeOutDown {\n", "      from {\n", "        opacity: 1;\n", "      }\n", "    \n", "      to {\n", "        opacity: 0;\n", "        transform: translate3d(0, 100%, 0);\n", "      }\n", "    }\n", "    \n", "    @keyframes bbFadeInOpacity {\n", "      from {\n", "        opacity: 0;\n", "      }\n", "    \n", "      to {\n", "        opacity: 1;\n", "      }\n", "    }\n", "    \n", "    @keyframes bbZoomOut {\n", "      from {\n", "        opacity: 1;\n", "      }\n", "    \n", "      50% {\n", "        opacity: 0;\n", "        transform: scale3d(0.3, 0.3, 0.3);\n", "      }\n", "    \n", "      to {\n", "        opacity: 0;\n", "      }\n", "    }\n", "    \n", "    @keyframes bbZoomIn {\n", "      from {\n", "        opacity: 0;\n", "        transform: scale3d(0.3, 0.3, 0.3);\n", "      }\n", "    \n", "      50% {\n", "        opacity: 1;\n", "      }\n", "    }\n", "    \n", "    @keyframes bbRecIconContFade {\n", "      0% {\n", "        fill: #b10802;\n", "      }\n", "      50% {\n", "        fill: #ff0000;\n", "      }\n", "      100% {\n", "        fill: #b10802;\n", "      }\n", "    }  \n", "    .bb-capture-preview-retrybutton {\n", "      color: #000000;\n", "      border-radius: 19px;\n", "      background-color: #f3f3f3;\n", "    }\n", "    .bb-capture-preview-retrybutton:hover {\n", "      background-color: #e1e1e1;\n", "    }\n", "    @keyframes bb-suc-fill {\n", "      100% {\n", "        box-shadow: inset 0px 0px 0px 30px #1e5cc9;\n", "      }\n", "    }\n", "    .bb-capture-toolbar-item-spacer {\n", "      background-color: #f3f3f3;\n", "    }\n", "    .bb-tooltip {\n", "      border-radius: 7px;\n", "    }\n", "    @keyframes bbRecIconFade {\n", "      0% {\n", "        fill: transparent;\n", "      }\n", "      50% {\n", "        fill: #e1e1e1;\n", "      }\n", "      100% {\n", "        fill: transparent;\n", "      }\n", "    }\n", "    .bb-capture-preview-sendbutton {\n", "      color: #ffffff;\n", "      background-color: #1e5cc9;\n", "      border-radius: 19px;\n", "    }\n", "    .bb-capture-button-next {\n", "      color: #ffffff;\n", "      background-color: #1e5cc9;\n", "      border-radius: 5px;\n", "    }\n", "    .bb-capture-preview-inner {\n", "      background-color: #FFFFFF;\n", "      border-radius: 7px;\n", "    }\n", "    .bb-capture-toolbar-item-timer {\n", "      color: #878787;\n", "    }\n", "    .bb-svg-path {\n", "      fill: #000000;\n", "    }\n", "    .bb-capture-toolbar-item {\n", "      border-radius: 7px;\n", "    }\n", "    .bb-capture-toolbar {\n", "      background-color: #FFFFFF;\n", "      border-radius: 7px;\n", "    }\n", "    .bb-capture-toolbar-item-colorpicker {\n", "      background-color: #FFFFFF;\n", "    }\n", "    .bb-capture-toolbar-item--active {\n", "      background-color: #f3f3f3;\n", "    }\n", "    .bb-feedback-button-classic {\n", "      border-top-left-radius: 7px;\n", "      border-top-right-radius: 7px;\n", "    }\n", "    .bb-logo-logo--default path {\n", "      fill: #ffffff;\n", "    }\n", "    .bb-logo-arrowdown {\n", "      fill: #ffffff;\n", "    }\n", "    .bb-feedback-button-icon {\n", "        background-color: #1e5cc9;\n", "    }\n", "    .bb-feedback-button-classic {\n", "      background-color: #1e5cc9;\n", "      color: #ffffff;\n", "    }\n", "\n", "    @media only screen and (max-width: 450px) {\n", "      .gleap-frame-container {\n", "        left: 0px;\n", "        right: 0px;\n", "        width: 100vw;\n", "        max-width: 100vw;\n", "        min-height: 100vh;\n", "        min-height: -webkit-fill-available;\n", "        top: 0px;\n", "        bottom: 0px;\n", "        border-radius: 0px;\n", "        animation-name: gleapFadeInUpMobile;\n", "      }\n", "\n", "      .gleap-frame-container-inner {\n", "        width: 100vw;\n", "        height: 100%;\n", "      }\n", "\n", "      .gleap-frame-container:not(.gleap-frame-container--survey):not(.gleap-frame-container--survey-full) .gleap-frame-container-inner {\n", "        max-height: initial !important;\n", "      }\n", "\n", "      .gleap-frame-container--survey {\n", "        height: auto !important;\n", "        top: initial !important;\n", "        bottom: 0px !important;\n", "        min-height: initial !important;\n", "      }\n", "\n", "      .gleap-frame-container--survey .gleap-frame-container-inner {\n", "        height: 100vh !important;\n", "      }\n", "\n", "      .bb-tooltip {\n", "        display: none !important;\n", "      }\n", "    \n", "      .bb-capture-toolbar-item-colorpicker {\n", "        top: 75px;\n", "      }\n", "    \n", "      .bb-capture-button-next {\n", "        margin-left: auto;\n", "      }\n", "    \n", "      .bb-capture-dismiss {\n", "        display: none;\n", "      }\n", "    \n", "      .bb-capture-toolbar {\n", "        top: 15px;\n", "        right: 15px;\n", "        left: 15px;\n", "        width: auto;\n", "        transform: none;\n", "      }\n", "    \n", "      .bb-capture-editor-drag-info {\n", "        display: none;\n", "      }\n", "    \n", "      .bb-capture-editor-borderlayer {\n", "        border-width: 4px;\n", "      }\n", "    }\n", "    \n", "    @media print {\n", "      .bb-feedback-button {\n", "        display: none !important;\n", "      }\n", "      \n", "      .gleap-frame-container {\n", "        display: none !important;\n", "      }\n", "    }\n", "    </style><div class=\"bb-feedback-button gleap-font gleap-hidden bb-feedback-button--disabled\" dir=\"ltr\"><div class=\"bb-feedback-button-icon\"><img class=\"bb-logo-logo\" src=\"https://cdn.gleap.io/static/1665645137394-d5e7abb2-5ffe-4d39-9ad4-37ee57c19153.png\" alt=\"Feedback Button\"><svg class=\"bb-logo-arrowdown\" fill=\"#fff\" width=\"100pt\" height=\"100pt\" version=\"1.1\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\">\n", "    <path d=\"m50 77.637c-1.3477 0-2.6953-0.51562-3.7266-1.543l-44.73-44.73c-2.0586-2.0586-2.0586-5.3945 0-7.4531 2.0586-2.0586 5.3945-2.0586 7.4531 0l41.004 41 41.004-41c2.0586-2.0586 5.3945-2.0586 7.4531 0 2.0586 2.0586 2.0586 5.3945 0 7.4531l-44.73 44.727c-1.0312 1.0312-2.3789 1.5469-3.7266 1.5469z\"></path>\n", "   </svg></div><div class=\"bb-notification-bubble bb-notification-bubble--hidden\"></div></div><div class=\"gleap-notification-container gleap-font gleap-notification-container--no-button\"></div><iframe height=\"0\" width=\"0\" style=\"display: none; visibility: hidden;\"></iframe><iframe id=\"_hjSafeContext_10678287\" title=\"_hjSafeContext\" tabindex=\"-1\" aria-hidden=\"true\" src=\"about:blank\" style=\"display: none !important; width: 1px !important; height: 1px !important; opacity: 0 !important; pointer-events: none !important;\"></iframe><style id=\"wistia_163_style\" type=\"text/css\" class=\"wistia_injected_style\">\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    src: url(data:application/x-font-woff;charset=utf-8;base64,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);\n", "    unicode-range: U+0-3B,U+3F-5A,U+61-7A,U+C1,U+C9,U+D1,U+E1,U+E9,E+F1,U+2014,U+2026,U+2192,U+21BA,U+2713,U+2717;\n", "  }\n", "\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    src:  url(https://fast.wistia.com/assets/external/fonts/Inter-Extended.woff) format('woff2');\n", "    unicode-range: U+3C-3E,U+5B-60,U+7B-7E,U+A0-C0,U+C2-C8,U+CA-D0,U+D2-E8,U+EA-F0,U+F2-2013,U+2015-2025,U+2026-2191,U+2193-21B9,U+21BB-2712,U+2714-2716,U+2718-10FFFF;\n", "  }\n", "\n", "  /* cyrillic-ext */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Cyrillic-Extended.woff) format('woff');\n", "    unicode-range: U+0460-052F, U+1C80-1C88, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n", "  }\n", "  /* cyrillic */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Cyrillic.woff) format('woff');\n", "    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n", "  }\n", "  /* greek-ext */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Greek-Extended.woff) format('woff');\n", "    unicode-range: U+1F00-1FFF;\n", "  }\n", "  /* greek */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Greek.woff) format('woff');\n", "    unicode-range: U+0370-03FF;\n", "  }\n", "  /* vietnamese */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Vietnamese.woff) format('woff');\n", "    unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+1EA0-1EF9, U+20AB;\n", "  }\n", "  /* latin-ext */\n", "  @font-face {\n", "    font-family: WistiaPlayerInter;\n", "    font-style: normal;\n", "    font-weight: 400;\n", "    src: url(https://fast.wistia.com/assets/external/fonts/Inter-Latin-Extended.woff) format('woff');\n", "    unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;\n", "</style><iframe id=\"intercom-frame\" style=\"position: absolute !important; opacity: 0 !important; width: 1px !important; height: 1px !important; top: 0 !important; left: 0 !important; border: none !important; display: block !important; z-index: -1 !important; pointer-events: none;\" aria-hidden=\"true\" tabindex=\"-1\" title=\"Intercom\"></iframe></body></html>\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "# Start the browser\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # Wait until the new page loads (e.g. check for email input or unique element on sign in page)\n", "    WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.TAG_NAME, \"form\"))\n", "    )\n", "\n", "    # Dump the page source\n", "    page_source = driver.page_source\n", "    print(page_source)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "b9551902-0c3c-4754-8fa6-873942856803", "metadata": {}, "source": ["### With <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "23291f64-2e7c-48fe-b77e-961184cbd380", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the sign-in page loads (look for a form or iframe)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.TAG_NAME, \"body\"))\n", "    )\n", "\n", "    # 4. Dump the main page source\n", "    print(\"\\n=== MAIN PAGE SOURCE ===\\n\")\n", "    print(driver.page_source)\n", "\n", "    # 5. Check if there are any iframes\n", "    iframes = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "    print(f\"\\nFound {len(iframes)} iframe(s).\\n\")\n", "\n", "    for idx, iframe in enumerate(iframes):\n", "        driver.switch_to.frame(iframe)\n", "        print(f\"\\n=== IFRAME {idx} SOURCE ===\\n\")\n", "        print(driver.page_source[:2000])  # print first 2000 chars (to avoid console flood)\n", "        driver.switch_to.default_content()\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "d788fd70-3518-4e45-9cfe-0e4452adec3b", "metadata": {}, "source": ["## Fill out cred fields"]}, {"cell_type": "code", "execution_count": null, "id": "87e878fb-1ec7-4b35-9706-8802c034f782", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the email input field is present\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    # 4. Fill in test values (do not click sign in)\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "\n", "    password_input.clear()\n", "    password_input.send_keys(\"TestPassword123!\")\n", "\n", "    print(\"✅ Test email and password have been filled in.\")\n", "\n", "finally:\n", "    # Comment this out if you want to see the filled fields before browser closes\n", "    # driver.quit()\n", "    pass\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "ac4cae1f-2596-454d-b6fa-71ebcd48d5c5", "metadata": {}, "source": ["<img src=\"./screenshots/Screenshot 2025-08-28 102946.png\" alt=\"filling out creds\" width=\"500\"/>"]}, {"cell_type": "markdown", "id": "ec06c29f-7768-46a8-a7ed-0c0768ffd09b", "metadata": {}, "source": ["## Sign in"]}, {"cell_type": "code", "execution_count": null, "id": "441bc55f-1b50-47c7-9d7f-36aa761eea73", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the email input field is present\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    # 4. Fill in test values\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 5. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    print(\"✅ Attempted to sign in with test credentials.\")\n", "\n", "finally:\n", "    # Leave browser open for debugging — close manually when ready\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "abc902bb-ebf0-4277-bfc9-8db44805b658", "metadata": {}, "source": ["## Open Scripts page"]}, {"cell_type": "code", "execution_count": null, "id": "cb44fb7f-9c25-41ac-8f66-99a477e75dfa", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. <PERSON><PERSON> \"Sign In\"\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. <PERSON>ll in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 4. Submit login\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 5. Wait until dashboard loads (HOME tab visible)\n", "    WebDriverWait(driver, 20).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//a[@data-tut='reactour__tab__home']\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" tab\n", "    scripts_tab = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//a[@data-tut='reactour__tab__scripts']\"))\n", "    )\n", "    scripts_tab.click()\n", "\n", "    print(\"✅ Navigated to the Scripts page.\")\n", "\n", "finally:\n", "    # Comment out if you want to inspect browser manually\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "f7bc031d-d584-431c-9dab-78de20835f53", "metadata": {}, "source": ["### Clean up code"]}, {"cell_type": "code", "execution_count": null, "id": "4a6a6225-a048-47c1-8260-457dd8a028a4", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads (like header or list)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "\n", "    print(\"✅ Logged in and navigated directly to Scripts page.\")\n", "\n", "finally:\n", "    # Leave browser open for inspection\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "4ec4cd67-b0a2-4c2d-94fb-d6585098b65d", "metadata": {}, "source": ["## Dump script cards"]}, {"cell_type": "code", "execution_count": 1, "id": "562be4a6-31c2-40bc-8183-e5b004fe8b6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Logged in and navigated directly to Scripts page.\n", "✅ Extracted patient names and saved to patients.xlsx\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "import pandas as pd\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads (like header or list)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "\n", "    print(\"✅ Logged in and navigated directly to Scripts page.\")\n", "\n", "        # 6. Wait for script cards to load\n", "    cards = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "    )\n", "    \n", "    # 7. Extract patient names\n", "    patient_names = []\n", "    for card in cards:\n", "        try:\n", "            name = card.find_element(By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "            patient_names.append(name)\n", "        except:\n", "            continue\n", "    \n", "    # 8. Save to Excel\n", "    df = pd.DataFrame(patient_names, columns=[\"Patient Name\"])\n", "    df.to_excel(\"patients.xlsx\", index=False)\n", "    \n", "    print(\"✅ Extracted patient names and saved to patients.xlsx\")\n", "\n", "finally:\n", "    # Leave browser open for inspection\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "fecef6e8-4d84-44c1-a19a-1bbbd7250028", "metadata": {}, "source": ["## Visit all pages"]}, {"cell_type": "code", "execution_count": 1, "id": "1504633d-3162-4a63-b852-9cab7962291e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Logged in and navigated to Scripts page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 11 patients from current page.\n", "✅ Extracted all patient names across pages and saved to patients.xlsx\n"]}], "source": ["import time\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "    print(\"✅ Logged in and navigated to Scripts page.\")\n", "\n", "    patient_names = []\n", "\n", "    while True:\n", "        # 6. Wait for script cards to load\n", "        cards = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_all_elements_located((By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "        )\n", "\n", "        # 7. Extract patient names on this page\n", "        for card in cards:\n", "            try:\n", "                name = card.find_element(By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "                patient_names.append(name)\n", "            except:\n", "                continue\n", "\n", "        print(f\"✅ Scraped {len(cards)} patients from current page.\")\n", "\n", "        # 8. Try to go to next page\n", "        try:\n", "            next_button = driver.find_element(By.XPATH, \"//button[@aria-label='Go to next page']\")\n", "            if \"disabled\" in next_button.get_attribute(\"class\"):\n", "                break  # no more pages\n", "            next_button.click()\n", "            time.sleep(2)  # wait for new page to load\n", "        except:\n", "            break  # no next button found → stop\n", "\n", "    # 9. Save all patients to Excel\n", "    df = pd.DataFrame(patient_names, columns=[\"Patient Name\"])\n", "    df.to_excel(\"patients.xlsx\", index=False)\n", "\n", "    print(\"✅ Extracted all patient names across pages and saved to patients.xlsx\")\n", "\n", "finally:\n", "    pass  # keep browser open for inspection\n"]}, {"cell_type": "markdown", "id": "9034d33f-56b8-499d-94bf-5fb76e497822", "metadata": {}, "source": ["## Dump script case contents"]}, {"cell_type": "markdown", "id": "029fc5a3-af4e-43df-a4f6-824da073c103", "metadata": {}, "source": ["### navigate tabs"]}, {"cell_type": "code", "execution_count": null, "id": "a3b20dfe-115d-478a-8ca2-aec2d735d425", "metadata": {}, "outputs": [], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab and dump content\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        # Grab the visible content panel\n", "        try:\n", "            content = driver.find_element(\n", "                By.XPATH, \"//div[contains(@class,'react-tabs__tab-panel') and not(contains(@style,'display: none'))]\"\n", "            ).text\n", "        except:\n", "            content = \"(No content found)\"\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "        print(content)\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "markdown", "id": "d0fd8be1-de28-4885-90db-b695eaf152d0", "metadata": {}, "source": ["### dump doctors information & script"]}, {"cell_type": "code", "execution_count": null, "id": "6e3e4317-ec1b-42c3-8809-7a73996990bf", "metadata": {}, "outputs": [], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "        \n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "        \n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "        \n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "        \n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "        \n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "        \n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip() for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "        \n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "                \n", "        elif \"Marking Rubric\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Marking Rubric (raw) =====\\n\", raw_text)\n", "        \n", "            # Optional: split by headings like \"History\", \"Examination\", \"Diagnosis\" if present\n", "            lines = raw_text.split(\"\\n\")\n", "            sections = {}\n", "            current = \"General\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line.endswith(\":\"):  # headings usually end with colon\n", "                    current = line.strip(\":\")\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "markdown", "id": "dec638d4-2680-4616-a3b8-afa3f961c518", "metadata": {}, "source": ["### dump all"]}, {"cell_type": "code", "execution_count": 1, "id": "1035de6d-a53e-439a-af66-cfca51168a44", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "                        # Get the full text content from the summary\n", "                        summary_text = summary.text.strip()\n", "\n", "                        # Split by lines and clean up\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # Parse the content - look for title and score patterns\n", "                        title = \"\"\n", "                        score = None\n", "\n", "                        # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "                        full_text = ' '.join(lines)\n", "\n", "                        # Try to find score pattern in the full text\n", "                        score_match = re.search(\n", "                            r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "                        if score_match:\n", "                            score = {\n", "                                'current': int(score_match.group(1)),\n", "                                'total': int(score_match.group(2))\n", "                            }\n", "                            # Remove the score part to get the title\n", "                            title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                                           '', full_text).strip()\n", "                        else:\n", "                            # No score found, the whole text is the title\n", "                            title = full_text\n", "\n", "                        # Fallback: if title is empty, try the first line\n", "                        if not title and lines:\n", "                            title = lines[0]\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "\n", "                    except Exception as e:\n", "                        print(f\"Error parsing accordion summary: {e}\")\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                top_accordions = panel_element.find_elements(\n", "                    By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                # Filter to get only the main categories (not nested ones)\n", "                main_categories = []\n", "                for accordion in top_accordions:\n", "                    try:\n", "                        # Check if this accordion has a parent accordion - if so, skip it\n", "                        parent_accordion = accordion.find_element(\n", "                            By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                        # If we found a parent accordion, this is nested, so skip\n", "                        continue\n", "                    except:\n", "                        # No parent accordion found, this is a top-level one\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            main_categories.append(parsed)\n", "\n", "                return main_categories\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Deduplicate the data to remove hierarchy conflicts\n", "            def deduplicate_rubric_data(categories):\n", "                \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "                def collect_all_subcategory_titles(cats):\n", "                    \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "                    titles = set()\n", "                    for cat in cats:\n", "                        if 'subcategories' in cat:\n", "                            for subcat in cat['subcategories']:\n", "                                titles.add(subcat['title'])\n", "                                # Recursively collect from deeper levels\n", "                                titles.update(\n", "                                    collect_all_subcategory_titles([subcat]))\n", "                    return titles\n", "\n", "                def clean_category(category, all_subcat_titles):\n", "                    \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "                    cleaned = {\n", "                        'title': category['title']\n", "                    }\n", "\n", "                    # Add score if present\n", "                    if 'score' in category:\n", "                        cleaned['score'] = category['score']\n", "\n", "                    # Process subcategories first (recursively)\n", "                    if 'subcategories' in category:\n", "                        cleaned_subcats = []\n", "\n", "                        # Collect titles of subcategories that have their own subcategories\n", "                        # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "                        parent_subcats = set()\n", "                        for subcat in category['subcategories']:\n", "                            if 'subcategories' in subcat:\n", "                                for nested in subcat['subcategories']:\n", "                                    # Only add if it's not a self-reference\n", "                                    if nested['title'] != subcat['title']:\n", "                                        parent_subcats.add(nested['title'])\n", "\n", "                        # First, identify the best version of each duplicate title\n", "                        title_to_best_subcat = {}\n", "                        for subcat in category['subcategories']:\n", "                            title = subcat['title']\n", "                            if title not in title_to_best_subcat:\n", "                                title_to_best_subcat[title] = subcat\n", "                            else:\n", "                                # Compare with existing: prefer the one with subcategories\n", "                                existing = title_to_best_subcat[title]\n", "                                current_has_subcats = 'subcategories' in subcat\n", "                                existing_has_subcats = 'subcategories' in existing\n", "\n", "                                if current_has_subcats and not existing_has_subcats:\n", "                                    # Current is better (has subcategories)\n", "                                    title_to_best_subcat[title] = subcat\n", "                                elif current_has_subcats and existing_has_subcats:\n", "                                    # Both have subcategories, prefer the one with more subcategories\n", "                                    if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                                        title_to_best_subcat[title] = subcat\n", "\n", "                        # Now process the best version of each subcategory\n", "                        for title, best_subcat in title_to_best_subcat.items():\n", "                            # Skip if this title is a subcategory of another subcategory at this level\n", "                            if title not in parent_subcats:\n", "                                cleaned_subcat = clean_category(\n", "                                    best_subcat, all_subcat_titles)\n", "                                cleaned_subcats.append(cleaned_subcat)\n", "\n", "                        if cleaned_subcats:\n", "                            cleaned['subcategories'] = cleaned_subcats\n", "\n", "                    # Process items - only include items that are NOT subcategory titles\n", "                    if 'items' in category:\n", "                        # If this category has subcategories, don't include items that belong to subcategories\n", "                        if 'subcategories' in cleaned:\n", "                            # Collect all items that belong to subcategories\n", "                            subcat_items = set()\n", "                            for subcat in cleaned['subcategories']:\n", "                                if 'items' in subcat:\n", "                                    subcat_items.update(subcat['items'])\n", "                                # Also collect items from deeper subcategories\n", "\n", "                                def collect_deep_items(cat):\n", "                                    items = set()\n", "                                    if 'items' in cat:\n", "                                        items.update(cat['items'])\n", "                                    if 'subcategories' in cat:\n", "                                        for sc in cat['subcategories']:\n", "                                            items.update(\n", "                                                collect_deep_items(sc))\n", "                                    return items\n", "                                subcat_items.update(collect_deep_items(subcat))\n", "\n", "                            # Only keep items that don't belong to any subcategory\n", "                            filtered_items = []\n", "                            for item in category['items']:\n", "                                if item not in subcat_items and item not in all_subcat_titles:\n", "                                    filtered_items.append(item)\n", "\n", "                            if filtered_items:\n", "                                cleaned['items'] = filtered_items\n", "                        else:\n", "                            # No subcategories, keep all items\n", "                            cleaned['items'] = category['items']\n", "\n", "                    return cleaned\n", "\n", "                # First pass: collect all subcategory titles\n", "                all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "                # Second pass: clean each category and remove duplicates\n", "                cleaned_categories = []\n", "                processed_titles = set()\n", "\n", "                for category in categories:\n", "                    title = category['title']\n", "                    # Only process if this title hasn't been seen as a subcategory\n", "                    if title not in all_subcat_titles and title not in processed_titles:\n", "                        cleaned = clean_category(category, all_subcat_titles)\n", "                        cleaned_categories.append(cleaned)\n", "                        processed_titles.add(title)\n", "\n", "                return cleaned_categories\n", "\n", "            # Clean the data\n", "            clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            formatted_output = format_rubric_display(clean_rubric_data)\n", "            for line in formatted_output:\n", "                print(line)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "e097c205-b5ec-427f-b9fc-fbd8f21209ac", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "Found main category: Shortness Of Breath\n", "Error extracting data from HOPC: name 'deduplicate_rubric_data' is not defined\n", "\n", "===== Processing Assoc. =====\n", "Error extracting data from Assoc.: name 'deduplicate_rubric_data' is not defined\n", "\n", "===== Processing PMHx =====\n", "Found main category: Medication History\n", "Found main category: Past Medical History\n", "Error extracting data from PMHx: name 'deduplicate_rubric_data' is not defined\n", "\n", "===== Processing FMHx =====\n", "Error extracting data from FMHx: name 'deduplicate_rubric_data' is not defined\n", "\n", "===== Processing Social =====\n", "Found main category: Social History\n", "Error extracting data from Social: name 'deduplicate_rubric_data' is not defined\n", "\n", "===== Processing Basics =====\n", "Error extracting data from Basics: name 'deduplicate_rubric_data' is not defined\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n", "\n", "===== Medical History Summary =====\n", "HOPC: 0 main sections\n", "Assoc.: 0 main sections\n", "PMHx: 0 main sections\n", "FMHx: 0 main sections\n", "Social: 0 main sections\n", "Basics: 0 main sections\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "                        # Get the full text content from the summary\n", "                        summary_text = summary.text.strip()\n", "\n", "                        # Split by lines and clean up\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # Parse the content - look for title and score patterns\n", "                        title = \"\"\n", "                        score = None\n", "\n", "                        # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "                        full_text = ' '.join(lines)\n", "\n", "                        # Try to find score pattern in the full text\n", "                        score_match = re.search(\n", "                            r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "                        if score_match:\n", "                            score = {\n", "                                'current': int(score_match.group(1)),\n", "                                'total': int(score_match.group(2))\n", "                            }\n", "                            # Remove the score part to get the title\n", "                            title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                                           '', full_text).strip()\n", "                        else:\n", "                            # No score found, the whole text is the title\n", "                            title = full_text\n", "\n", "                        # Fallback: if title is empty, try the first line\n", "                        if not title and lines:\n", "                            title = lines[0]\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "\n", "                    except Exception as e:\n", "                        print(f\"Error parsing accordion summary: {e}\")\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                # Strategy: Look for accordions that are direct children of the main container\n", "                try:\n", "                    # First try to find the main container\n", "                    main_container = panel_element.find_element(\n", "                        By.XPATH, \".//div[contains(@class, 'jss85')]\")\n", "                    # Find direct child accordions (main categories)\n", "                    main_accordions = main_container.find_elements(\n", "                        By.XPATH, \"./div[contains(@class, 'MuiAccordion-root')]\")\n", "                except:\n", "                    # Fallback: use the original approach but with better filtering\n", "                    main_accordions = panel_element.find_elements(\n", "                        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                    # Filter out nested ones\n", "                    filtered_accordions = []\n", "                    for accordion in main_accordions:\n", "                        try:\n", "                            # Check if this accordion has a parent accordion - if so, skip it\n", "                            parent_accordion = accordion.find_element(\n", "                                By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                            # If we found a parent accordion, this is nested, so skip\n", "                            continue\n", "                        except:\n", "                            # No parent accordion found, this is a top-level one\n", "                            filtered_accordions.append(accordion)\n", "                    main_accordions = filtered_accordions\n", "\n", "                # Parse all main categories\n", "                main_categories = []\n", "                for accordion in main_accordions:\n", "                    try:\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            # Skip categories with generic names like 'default'\n", "                            if parsed['title'].lower() not in ['default', 'untitled', '']:\n", "                                main_categories.append(parsed)\n", "                                print(\n", "                                    f\"Found main category: {parsed['title']}\")\n", "                            else:\n", "                                print(\n", "                                    f\"Skipping generic category: {parsed['title']}\")\n", "                    except Exception as e:\n", "                        print(f\"Error parsing main accordion: {e}\")\n", "                        continue\n", "\n", "                return main_categories\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "                # Extract data from this medical history tab\n", "                try:\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "                    all_rubric_data[med_tab_name] = clean_rubric_data\n", "\n", "                    print(f\"\\n===== {med_tab_name} (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Error extracting data from {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "\n", "            # Print summary of all medical history categories\n", "            print(f\"\\n===== Medical History Summary =====\")\n", "            for category, data in all_rubric_data.items():\n", "                count = len(data)\n", "                print(f\"{category}: {count} main sections\")\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Deduplicate the data to remove hierarchy conflicts\n", "            def deduplicate_rubric_data(categories):\n", "                \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "                def collect_all_subcategory_titles(cats):\n", "                    \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "                    titles = set()\n", "                    for cat in cats:\n", "                        if 'subcategories' in cat:\n", "                            for subcat in cat['subcategories']:\n", "                                titles.add(subcat['title'])\n", "                                # Recursively collect from deeper levels\n", "                                titles.update(\n", "                                    collect_all_subcategory_titles([subcat]))\n", "                    return titles\n", "\n", "                def clean_category(category, all_subcat_titles):\n", "                    \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "                    cleaned = {\n", "                        'title': category['title']\n", "                    }\n", "\n", "                    # Add score if present\n", "                    if 'score' in category:\n", "                        cleaned['score'] = category['score']\n", "\n", "                    # Process subcategories first (recursively)\n", "                    if 'subcategories' in category:\n", "                        cleaned_subcats = []\n", "\n", "                        # Collect titles of subcategories that have their own subcategories\n", "                        # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "                        parent_subcats = set()\n", "                        for subcat in category['subcategories']:\n", "                            if 'subcategories' in subcat:\n", "                                for nested in subcat['subcategories']:\n", "                                    # Only add if it's not a self-reference\n", "                                    if nested['title'] != subcat['title']:\n", "                                        parent_subcats.add(nested['title'])\n", "\n", "                        # First, identify the best version of each duplicate title\n", "                        title_to_best_subcat = {}\n", "                        for subcat in category['subcategories']:\n", "                            title = subcat['title']\n", "                            if title not in title_to_best_subcat:\n", "                                title_to_best_subcat[title] = subcat\n", "                            else:\n", "                                # Compare with existing: prefer the one with subcategories\n", "                                existing = title_to_best_subcat[title]\n", "                                current_has_subcats = 'subcategories' in subcat\n", "                                existing_has_subcats = 'subcategories' in existing\n", "\n", "                                if current_has_subcats and not existing_has_subcats:\n", "                                    # Current is better (has subcategories)\n", "                                    title_to_best_subcat[title] = subcat\n", "                                elif current_has_subcats and existing_has_subcats:\n", "                                    # Both have subcategories, prefer the one with more subcategories\n", "                                    if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                                        title_to_best_subcat[title] = subcat\n", "\n", "                        # Now process the best version of each subcategory\n", "                        for title, best_subcat in title_to_best_subcat.items():\n", "                            # Skip if this title is a subcategory of another subcategory at this level\n", "                            if title not in parent_subcats:\n", "                                cleaned_subcat = clean_category(\n", "                                    best_subcat, all_subcat_titles)\n", "                                cleaned_subcats.append(cleaned_subcat)\n", "\n", "                        if cleaned_subcats:\n", "                            cleaned['subcategories'] = cleaned_subcats\n", "\n", "                    # Process items - only include items that are NOT subcategory titles\n", "                    if 'items' in category:\n", "                        # If this category has subcategories, don't include items that belong to subcategories\n", "                        if 'subcategories' in cleaned:\n", "                            # Collect all items that belong to subcategories\n", "                            subcat_items = set()\n", "                            for subcat in cleaned['subcategories']:\n", "                                if 'items' in subcat:\n", "                                    subcat_items.update(subcat['items'])\n", "                                # Also collect items from deeper subcategories\n", "\n", "                                def collect_deep_items(cat):\n", "                                    items = set()\n", "                                    if 'items' in cat:\n", "                                        items.update(cat['items'])\n", "                                    if 'subcategories' in cat:\n", "                                        for sc in cat['subcategories']:\n", "                                            items.update(\n", "                                                collect_deep_items(sc))\n", "                                    return items\n", "                                subcat_items.update(collect_deep_items(subcat))\n", "\n", "                            # Only keep items that don't belong to any subcategory\n", "                            filtered_items = []\n", "                            for item in category['items']:\n", "                                if item not in subcat_items and item not in all_subcat_titles:\n", "                                    filtered_items.append(item)\n", "\n", "                            if filtered_items:\n", "                                cleaned['items'] = filtered_items\n", "                        else:\n", "                            # No subcategories, keep all items\n", "                            cleaned['items'] = category['items']\n", "\n", "                    return cleaned\n", "\n", "                # First pass: collect all subcategory titles\n", "                all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "                # Second pass: clean each category and remove duplicates\n", "                cleaned_categories = []\n", "                processed_titles = set()\n", "\n", "                for category in categories:\n", "                    title = category['title']\n", "                    # Only process if this title hasn't been seen as a subcategory\n", "                    if title not in all_subcat_titles and title not in processed_titles:\n", "                        cleaned = clean_category(category, all_subcat_titles)\n", "                        cleaned_categories.append(cleaned)\n", "                        processed_titles.add(title)\n", "\n", "                return cleaned_categories\n", "\n", "            # Clean the data\n", "            clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3f037f32-80cf-4b23-918c-5464f49142b3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "\n", "===== Processing Assoc. =====\n", "\n", "===== Marking Rubric (structured) =====\n", "\n", "===== Processing PMHx =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Medication History 0/1\n", "    - Prescription Medications 0/1\n", "            Inhalers\n", "            Asking Generally\n", "            Antihypertensives\n", "\n", "===== Processing FMHx =====\n", "\n", "===== Marking Rubric (structured) =====\n", "\n", "===== Processing Social =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Social History 0/2\n", "    - Alcohol History 0/1\n", "            Drinking Status\n", "            Amount Currently\n", "            Amount at Baseline\n", "    - Smoking History 0/1\n", "            Smoking Status\n", "            Amount Currently\n", "            Amount in the Past\n", "    - Recreational Drug History 0/1\n", "            User Status\n", "\n", "===== Processing Basics =====\n", "\n", "===== Marking Rubric (structured) =====\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def extract_score_from_text(text):\n", "        \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "        score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "        if score_match:\n", "            return {\n", "                'current': int(score_match.group(1)),\n", "                'total': int(score_match.group(2))\n", "            }\n", "        return None\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = {\n", "                    'current': int(score_match.group(1)),\n", "                    'total': int(score_match.group(2))\n", "                }\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Find all top-level accordions in the panel\n", "    top_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # Filter to get only the main categories (not nested ones)\n", "    main_categories = []\n", "    for accordion in top_accordions:\n", "        try:\n", "            # Check if this accordion has a parent accordion - if so, skip it\n", "            parent_accordion = accordion.find_element(\n", "                By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "            # If we found a parent accordion, this is nested, so skip\n", "            continue\n", "        except:\n", "            # No parent accordion found, this is a top-level one\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                main_categories.append(parsed)\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # First pass: collect all subcategory titles\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category and remove duplicates\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Only process if this title hasn't been seen as a subcategory\n", "        if title not in all_subcat_titles and title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score['current']}/{score['total']}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "markdown", "id": "8f9fefb6-355b-455c-b0bb-c273b107e889", "metadata": {}, "source": ["# handle cases without main headings"]}, {"cell_type": "code", "execution_count": 1, "id": "8d87df20-7464-4352-a5f9-8221528cd88a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "Found main accordion: Shortness Of Breath\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "\n", "===== Processing Assoc. =====\n", "No main accordions found, looking for alternative content structures...\n", "Found 15 direct checkbox items\n", "Found nested accordion: Cough\n", "Found nested accordion: Fever\n", "Found nested accordion: <PERSON><PERSON><PERSON>\n", "Found nested accordion: Wheeze\n", "Found nested accordion: Fatigue\n", "Found nested accordion: Swelling\n", "Found nested accordion: Chest Pain\n", "Found nested accordion: Weight Loss\n", "Found nested accordion: Coryzal Symptoms\n", "Found nested accordion: Altered Bowel Habits\n", "Found nested accordion: Waking up <PERSON> of Breath\n", "Found nested accordion: Shortness of Breath when Laying Down\n", "Panel has content but no recognizable structure (HTML length: 22696)\n", "\n", "===== Marking Rubric (structured) =====\n", "- Items\n", "        <PERSON><PERSON>\n", "        Fever\n", "        <PERSON><PERSON><PERSON>\n", "        Wheeze\n", "        Fatigue\n", "        Chest Pain\n", "        Weight Loss\n", "        Sneezing\n", "        <PERSON><PERSON>\n", "        Nasal <PERSON>harge\n", "        Nasal Congestion\n", "        Defecating Blood\n", "        Black and Tarry Stools\n", "        Waking up Short of Breath\n", "        Shortness of Breath when Laying Down\n", "- Cough 0/1\n", "        <PERSON><PERSON>\n", "- Fever 0/1\n", "        Fever\n", "- Pallor 0/1\n", "        <PERSON><PERSON><PERSON>\n", "- Wheeze 0/1\n", "        Wheeze\n", "- Fatigue 0/1\n", "        Fatigue\n", "- Swelling 0/1\n", "- Chest Pain 0/1\n", "        Chest Pain\n", "- Weight Loss 0/1\n", "        Weight Loss\n", "- Coryzal Symptoms 0/1\n", "        Sneezing\n", "        <PERSON><PERSON>\n", "        Nasal <PERSON>harge\n", "        Nasal Congestion\n", "- Altered Bowel Habits 0/1\n", "        Defecating Blood\n", "        Black and Tarry Stools\n", "- Waking up <PERSON> of Breath 0/1\n", "        Waking up Short of Breath\n", "- Shortness of Breath when Laying Down 0/1\n", "        Shortness of Breath when Laying Down\n", "\n", "===== Processing PMHx =====\n", "Found main accordion: Medication History\n", "Found main accordion: Past Medical History\n", "\n", "===== Marking Rubric (structured) =====\n", "- Medication History 0/1\n", "    - Prescription Medications 0/1\n", "            Inhalers\n", "            Asking Generally\n", "            Antihypertensives\n", "\n", "===== Processing FMHx =====\n", "No main accordions found, looking for alternative content structures...\n", "Found 4 direct checkbox items\n", "Found nested accordion: Family History\n", "Panel has content but no recognizable structure (HTML length: 5456)\n", "\n", "===== Marking Rubric (structured) =====\n", "- Items\n", "        COPD\n", "        Asthma\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "- Family History 0/1\n", "        COPD\n", "        Asthma\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "\n", "===== Processing Social =====\n", "Found main accordion: Social History\n", "\n", "===== Marking Rubric (structured) =====\n", "- Social History 0/2\n", "    - Alcohol History 0/1\n", "            Drinking Status\n", "            Amount Currently\n", "            Amount at Baseline\n", "    - Smoking History 0/1\n", "            Smoking Status\n", "            Amount Currently\n", "            Amount in the Past\n", "    - Recreational Drug History 0/1\n", "            User Status\n", "\n", "===== Processing Basics =====\n", "No main accordions found, looking for alternative content structures...\n", "Found 4 direct checkbox items\n", "Found nested accordion: Introduction\n", "Found nested accordion: Patient Identity\n", "Found nested accordion: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "Panel has content but no recognizable structure (HTML length: 7119)\n", "\n", "===== Marking Rubric (structured) =====\n", "- Items\n", "        Introduction\n", "        Preferred Name\n", "        Patient Identity\n", "        Presenting <PERSON><PERSON><PERSON><PERSON>\n", "- Introduction 0/1\n", "        Introduction\n", "- Patient Identity 0/1\n", "        Preferred Name\n", "        Patient Identity\n", "- Presenting <PERSON><PERSON><PERSON> 0/1\n", "        Presenting <PERSON><PERSON><PERSON><PERSON>\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def extract_score_from_text(text):\n", "        \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "        score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "        if score_match:\n", "            return {\n", "                'current': int(score_match.group(1)),\n", "                'total': int(score_match.group(2))\n", "            }\n", "        return None\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = {\n", "                    'current': int(score_match.group(1)),\n", "                    'total': int(score_match.group(2))\n", "                }\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Strategy 1: Look for main accordions (big headings)\n", "    top_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # Filter to get only the main categories (not nested ones)\n", "    main_categories = []\n", "    for accordion in top_accordions:\n", "        try:\n", "            # Check if this accordion has a parent accordion - if so, skip it\n", "            parent_accordion = accordion.find_element(\n", "                By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "            # If we found a parent accordion, this is nested, so skip\n", "            continue\n", "        except:\n", "            # No parent accordion found, this is a top-level one\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                main_categories.append(parsed)\n", "                print(f\"Found main accordion: {parsed['title']}\")\n", "\n", "    # Strategy 2: If no main accordions found, look for alternative content structures\n", "    if not main_categories:\n", "        print(\"No main accordions found, looking for alternative content structures...\")\n", "\n", "        # Look for direct checkboxes (flat structure without main headings)\n", "        checkboxes = panel_element.find_elements(\n", "            By.XPATH, \".//input[@type='checkbox']\")\n", "        if checkboxes:\n", "            items = []\n", "            for checkbox in checkboxes:\n", "                name = checkbox.get_attribute('name')\n", "                if name and name.strip():\n", "                    items.append(name.strip())\n", "\n", "            if items:\n", "                # Create a category for direct items\n", "                direct_category = {\n", "                    'title': 'Items',\n", "                    'items': items\n", "                }\n", "                main_categories.append(direct_category)\n", "                print(f\"Found {len(items)} direct checkbox items\")\n", "\n", "        # Look for nested accordions that might not have a main container\n", "        nested_accordions = panel_element.find_elements(\n", "            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "        for accordion in nested_accordions:\n", "            try:\n", "                parsed = parse_accordion(accordion)\n", "                if parsed and parsed.get('title'):\n", "                    # Avoid duplicates from the main accordion search above\n", "                    if not any(cat.get('title') == parsed['title'] for cat in main_categories):\n", "                        main_categories.append(parsed)\n", "                        print(f\"Found nested accordion: {parsed['title']}\")\n", "            except Exception as e:\n", "                print(f\"Error parsing nested accordion: {e}\")\n", "                continue\n", "\n", "        # Look for any text content that might indicate items or structure\n", "        text_content = panel_element.text.strip()\n", "        if text_content and not main_categories:\n", "            print(\n", "                f\"Found text content (first 200 chars): {text_content[:200]}...\")\n", "\n", "            # Try to parse text content for potential items\n", "            lines = [line.strip()\n", "                     for line in text_content.split('\\n') if line.strip()]\n", "            if lines:\n", "                print(f\"Found {len(lines)} text lines\")\n", "                # Could potentially create items from text lines if they follow a pattern\n", "\n", "        # Look for other possible content structures\n", "        content_divs = panel_element.find_elements(\n", "            By.XPATH, \".//div[normalize-space(text())]\")\n", "        if content_divs and not main_categories:\n", "            print(f\"Found {len(content_divs)} content divs\")\n", "            # Show first 5 for debugging\n", "            for i, div in enumerate(content_divs[:5]):\n", "                div_text = div.text.strip()\n", "                if div_text and len(div_text) > 3:  # Skip very short text\n", "                    print(f\"  Content div {i+1}: {div_text[:100]}\")\n", "\n", "        # Strategy 3: Look for any form elements or interactive content\n", "        form_elements = panel_element.find_elements(\n", "            By.XPATH, \".//input | .//select | .//textarea | .//button\")\n", "        if form_elements and not main_categories:\n", "            print(f\"Found {len(form_elements)} form elements\")\n", "            for i, element in enumerate(form_elements[:3]):\n", "                element_type = element.get_attribute(\n", "                    'type') or element.tag_name\n", "                element_name = element.get_attribute(\n", "                    'name') or element.get_attribute('id') or 'unnamed'\n", "                print(f\"  Form element {i+1}: {element_type} - {element_name}\")\n", "\n", "        # Strategy 4: Check if the panel is truly empty or just not loaded\n", "        panel_html = panel_element.get_attribute('innerHTML')\n", "        if not panel_html.strip():\n", "            print(\"Panel appears to be completely empty (no HTML content)\")\n", "        elif len(panel_html.strip()) < 50:\n", "            print(f\"Panel has minimal content: {panel_html.strip()}\")\n", "        else:\n", "            print(\n", "                f\"Panel has content but no recognizable structure (HTML length: {len(panel_html)})\")\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # First pass: collect all subcategory titles\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category and remove duplicates\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Only process if this title hasn't been seen as a subcategory\n", "        if title not in all_subcat_titles and title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score['current']}/{score['total']}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "markdown", "id": "af676709-9209-445a-a066-55778cd3118b", "metadata": {}, "source": ["## Scrape all one patient"]}, {"cell_type": "code", "execution_count": null, "id": "e337d22d-fcdd-4ffb-8ba1-9ec448d1c281", "metadata": {}, "outputs": [], "source": ["import time\n", "import json\n", "from datetime import datetime\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = int(score_match.group(2))\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Strategy 1: Look for main accordions (big headings)\n", "    all_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # First pass: collect all potential main categories with their nesting info\n", "    potential_categories = []\n", "\n", "    for accordion in all_accordions:\n", "        try:\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                title = parsed['title']\n", "\n", "                # Check if this is a true nested accordion or a separate main category\n", "                try:\n", "                    parent_accordion = accordion.find_element(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                    # Even if it has a parent, it might still be a main category\n", "                    # Check the nesting level and content to decide\n", "                    nesting_level = len(accordion.find_elements(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\"))\n", "\n", "                    # Generic approach: determine if this should be treated as a main category\n", "                    # Based on nesting level and relative importance within the structure\n", "\n", "                    # score = parsed.get('score', {})\n", "                    # score_total = score.get('total', 0) if score else 0\n", "                    score_total = parsed.get('score', 0)\n", "\n", "                    should_include = False\n", "\n", "                    if nesting_level <= 1:\n", "                        # Top-level or first-level nested - always include as main categories\n", "                        should_include = True\n", "                    elif nesting_level == 2:\n", "                        # Second-level nested - include if it seems significant\n", "                        # Use relative scoring: if this category's score is significant compared to others\n", "                        # or if it has substantial content (items or subcategories)\n", "                        has_content = ('items' in parsed and len(parsed['items']) > 0) or \\\n", "                            ('subcategories' in parsed and len(\n", "                                parsed['subcategories']) > 0)\n", "\n", "                        # Include if it has a meaningful score (> 0) or substantial content\n", "                        if score_total > 0 or has_content:\n", "                            should_include = True\n", "                    elif nesting_level == 3:\n", "                        # Third-level nested - only include if it has significant content\n", "                        # This catches deeply nested but important categories\n", "                        has_substantial_content = ('items' in parsed and len(parsed['items']) >= 3) or \\\n", "                            ('subcategories' in parsed and len(\n", "                                parsed['subcategories']) >= 2)\n", "\n", "                        if score_total > 0 and has_substantial_content:\n", "                            should_include = True\n", "\n", "                    if should_include:\n", "                        # Store potential category with nesting info for later processing\n", "                        potential_categories.append({\n", "                            'parsed': parsed,\n", "                            'title': title,\n", "                            'nesting_level': nesting_level,\n", "                            'score_total': score_total\n", "                        })\n", "\n", "                except:\n", "                    # No parent accordion found, this is definitely a top-level one\n", "                    # score = parsed.get('score', {})\n", "                    # score_total = score.get('total', 0) if score else 0\n", "                    score_total = parsed.get('score', 0)\n", "\n", "                    potential_categories.append({\n", "                        'parsed': parsed,\n", "                        'title': title,\n", "                        'nesting_level': 0,  # Top-level\n", "                        'score_total': score_total\n", "                    })\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion: {e}\")\n", "            continue\n", "\n", "    # Helper function to check if a title is a subcategory of any potential category\n", "    def _is_subcategory_of_any(title, potential_cats):\n", "        for cat_info in potential_cats:\n", "            category = cat_info['parsed']\n", "            if _is_subcategory_of_recursive(title, category):\n", "                return True, category.get('title', 'Unknown')\n", "        return False, None\n", "\n", "    def _is_subcategory_of_recursive(title, category):\n", "        if 'subcategories' in category:\n", "            for subcat in category['subcategories']:\n", "                if subcat.get('title') == title:\n", "                    return True\n", "                # Check recursively in nested subcategories\n", "                if _is_subcategory_of_recursive(title, subcat):\n", "                    return True\n", "        return False\n", "\n", "    # Second pass: determine which categories should be main categories\n", "    # Sort by nesting level (top-level first) and score (higher scores first)\n", "    potential_categories.sort(key=lambda x: (\n", "        x['nesting_level'], -x['score_total']))\n", "\n", "    main_categories = []\n", "    final_processed_titles = {}\n", "\n", "    for cat_info in potential_categories:\n", "        parsed = cat_info['parsed']\n", "        title = cat_info['title']\n", "        nesting_level = cat_info['nesting_level']\n", "        score_total = cat_info['score_total']\n", "\n", "        # Check if this category is already a subcategory of ANY potential main category\n", "        # (not just the ones we've already processed)\n", "        # BUT exclude self-references (e.g., \"Past Medical History\" containing \"Past Medical History\")\n", "        is_subcategory_elsewhere, parent_title = _is_subcategory_of_any(\n", "            title, potential_categories)\n", "\n", "        # Special case: if the parent title is the same as the current title,\n", "        # this might be a self-reference, so we need to check if this is the main category\n", "        if is_subcategory_elsewhere and parent_title == title:\n", "            # This is a self-reference case like \"Past Medical History\" containing \"Past Medical History\"\n", "            # Keep the one with the higher score as the main category\n", "            current_score = score_total\n", "\n", "            # Find the parent category's score\n", "            parent_score = 0\n", "            for other_cat_info in potential_categories:\n", "                if other_cat_info['title'] == parent_title and other_cat_info != cat_info:\n", "                    parent_score = other_cat_info['score_total']\n", "                    break\n", "\n", "            if current_score >= parent_score:\n", "                # This category has equal or higher score, so it should be the main one\n", "                is_subcategory_elsewhere = False\n", "\n", "        if not is_subcategory_elsewhere:\n", "            # Add a suffix to distinguish multiple instances of the same title\n", "            if title in final_processed_titles:\n", "                final_processed_titles[title] += 1\n", "                unique_title = f\"{title} ({final_processed_titles[title]})\"\n", "                parsed['title'] = unique_title\n", "            else:\n", "                final_processed_titles[title] = 1\n", "\n", "            main_categories.append(parsed)\n", "\n", "    # Strategy 2: If no main accordions found, look for alternative content structures\n", "    if not main_categories:\n", "        print(\"No main accordions found, looking for alternative content structures...\")\n", "\n", "        # Look for direct checkboxes (flat structure without main headings)\n", "        checkboxes = panel_element.find_elements(\n", "            By.XPATH, \".//input[@type='checkbox']\")\n", "        if checkboxes:\n", "            items = []\n", "            for checkbox in checkboxes:\n", "                name = checkbox.get_attribute('name')\n", "                if name and name.strip():\n", "                    items.append(name.strip())\n", "\n", "            if items:\n", "                # Create a category for direct items\n", "                direct_category = {\n", "                    'title': 'Items',\n", "                    'items': items\n", "                }\n", "                main_categories.append(direct_category)\n", "                # print(f\"Found {len(items)} direct checkbox items\")\n", "\n", "        # Look for nested accordions that might not have a main container\n", "        # (This is now less needed since Strategy 1 handles nested accordions better)\n", "        nested_accordions = panel_element.find_elements(\n", "            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "        for accordion in nested_accordions:\n", "            try:\n", "                parsed = parse_accordion(accordion)\n", "                if parsed and parsed.get('title'):\n", "                    # Avoid duplicates from the main accordion search above\n", "                    # Check both exact title match and numbered versions\n", "                    existing_titles = [cat.get('title', '')\n", "                                       for cat in main_categories]\n", "                    base_title = parsed['title']\n", "\n", "                    # Check if this title (or a numbered version) already exists\n", "                    title_exists = any(\n", "                        existing_title == base_title or\n", "                        existing_title.startswith(f\"{base_title} (\")\n", "                        for existing_title in existing_titles\n", "                    )\n", "\n", "                    if not title_exists:\n", "                        main_categories.append(parsed)\n", "            except Exception as e:\n", "                print(f\"Error parsing nested accordion: {e}\")\n", "                continue\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # For the multi-category extraction approach, we want to keep all extracted categories\n", "    # as main categories, even if they appear as subcategories elsewhere\n", "    # This is different from the original single-category approach\n", "\n", "    # First pass: collect all subcategory titles (for internal deduplication within categories)\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category but keep all extracted main categories\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Process all categories that were explicitly extracted as main categories\n", "        # Don't filter based on subcategory appearance since we want them as main categories\n", "        if title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "def save_to_json(data, filename):\n", "    \"\"\"Save data to JSON file with proper formatting\"\"\"\n", "    try:\n", "        with open(filename, 'w', encoding='utf-8') as f:\n", "            json.dump(data, f, indent=2, ensure_ascii=False)\n", "        print(f\"Data saved to {filename}\")\n", "    except Exception as e:\n", "        print(f\"Error saving to JSON: {e}\")\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # Initialize the main data structure\n", "    script_data = {\n", "        \"metadata\": {\n", "            \"scraped_at\": datetime.now().isoformat(),\n", "            \"script_url\": driver.current_url\n", "        },\n", "        \"tabs\": {}\n", "    }\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "\n", "            # Clean up the sections\n", "            cleaned_sections = {}\n", "            for k, v in sections.items():\n", "                cleaned_sections[k] = v.strip()\n", "\n", "            script_data[\"tabs\"][\"doctor_information\"] = cleaned_sections\n", "\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in cleaned_sections.items():\n", "                print(f\"\\n{k}:\\n{v}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            script_data[\"tabs\"][\"script\"] = sections\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            marking_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        marking_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "                    # Store in the structured format\n", "                    marking_rubric_data[med_tab_name] = clean_rubric_data\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    marking_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "            # Add marking rubric data to the main structure\n", "            script_data[\"tabs\"][\"marking_rubric\"] = marking_rubric_data\n", "\n", "    # Save all data to JSON file\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    filename = f\"oscer_data.json\"\n", "    save_to_json(script_data, filename)\n", "\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(script_data, f, indent=4, ensure_ascii=False)\n", "    print(f\"Pretty-printed data saved to {filename}\")\n", "\n", "    print(f\"\\n===== JSON Structure Summary =====\")\n", "    print(f\"Total tabs processed: {len(script_data['tabs'])}\")\n", "    # for tab_key, tab_data in script_data[\"tabs\"].items():\n", "    #     print(f\"- {tab_key}: {tab_data['type']}\")\n", "    #     if tab_data['type'] == 'marking_rubric':\n", "    #         for category, category_data in tab_data['medical_history_categories'].items():\n", "    #             num_categories = len(category_data['categories'])\n", "    #             print(f\"  - {category}: {num_categories} categories\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6c730de7-3858-4e81-bb4c-b4b44e6af0e4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Waiting 0.7 seconds...\n", "Waiting 0.8 seconds...\n", "Waiting 1.2 seconds...\n", "Waiting 1.0 seconds...\n", "Waiting 0.5 seconds...\n", "Waiting 0.6 seconds...\n", "Waiting 0.5 seconds...\n", "Waiting 0.6 seconds...\n", "Waiting 0.8 seconds...\n", "Saved patient data to oscer_cases\\7\\1dd8a1f1-57fa-4850-b24f-2bf49604859d.json\n", "Waiting 2.3 seconds...\n", "Waiting 0.8 seconds...\n", "Waiting 0.9 seconds...\n", "Waiting 1.0 seconds...\n", "Waiting 0.8 seconds...\n", "Waiting 0.8 seconds...\n", "Waiting 1.0 seconds...\n", "Waiting 0.6 seconds...\n", "Waiting 0.9 seconds...\n", "Waiting 0.6 seconds...\n", "Saved patient data to oscer_cases\\7\\49ddfe7e-6045-47da-a1c0-2956e6433510.json\n", "Waiting 2.2 seconds...\n", "Waiting 0.6 seconds...\n", "Waiting 1.2 seconds...\n"]}, {"ename": "TimeoutException", "evalue": "Message: \n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mTimeoutException\u001b[39m                          Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 222\u001b[39m\n\u001b[32m    220\u001b[39m \u001b[38;5;66;03m# Now extract sections: each <p id=\"...\"> followed by its <ul>\u001b[39;00m\n\u001b[32m    221\u001b[39m sections = {}\n\u001b[32m--> \u001b[39m\u001b[32m222\u001b[39m headings = \u001b[43mWebDriverWait\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdriver\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m60\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43muntil\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    223\u001b[39m \u001b[43m    \u001b[49m\u001b[43mEC\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpresence_of_all_elements_located\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    224\u001b[39m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[43mBy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mXPATH\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43mf\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mpanel_xpath\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[33;43m//div[@id=\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mcontainerElement\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m]//p[@id]\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    225\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    227\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m heading \u001b[38;5;129;01min\u001b[39;00m headings:\n\u001b[32m    228\u001b[39m     title = heading.text.strip()\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\selenium\\webdriver\\support\\wait.py:138\u001b[39m, in \u001b[36mWebDriverWait.until\u001b[39m\u001b[34m(self, method, message)\u001b[39m\n\u001b[32m    136\u001b[39m         \u001b[38;5;28;01<PERSON>ak\u001b[39;00m\n\u001b[32m    137\u001b[39m     time.sleep(\u001b[38;5;28mself\u001b[39m._poll)\n\u001b[32m--> \u001b[39m\u001b[32m138\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m TimeoutException(message, screen, stacktrace)\n", "\u001b[31mTimeoutException\u001b[39m: Message: \n"]}], "source": ["import json\n", "import uuid\n", "import os\n", "import time\n", "import random\n", "from datetime import datetime\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "from utils import extract_rubric_data, deduplicate_rubric_data\n", "\n", "\n", "def human_like_delay(min_seconds=1, max_seconds=3):\n", "    \"\"\"Add random delay to mimic human behavior and avoid rate limiting\"\"\"\n", "    delay = random.uniform(min_seconds, max_seconds)\n", "    print(f\"Waiting {delay:.1f} seconds...\")\n", "    time.sleep(delay)\n", "\n", "\n", "def save_patient_data(patient_uuid, script_data, page_number):\n", "    \"\"\"Save individual patient data to organized folder structure\"\"\"\n", "    # Create the main oscer_cases directory if it doesn't exist\n", "    base_dir = \"oscer_cases\"\n", "    if not os.path.exists(base_dir):\n", "        os.makedirs(base_dir)\n", "\n", "    # Create the page directory if it doesn't exist\n", "    page_dir = os.path.join(base_dir, str(page_number))\n", "    if not os.path.exists(page_dir):\n", "        os.makedirs(page_dir)\n", "\n", "    # Save the patient data as uuid.json\n", "    filename = os.path.join(page_dir, f\"{patient_uuid}.json\")\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(script_data, f, indent=4, ensure_ascii=False)\n", "\n", "    print(f\"Saved patient data to {filename}\")\n", "\n", "\n", "# Setup Chrome options with anti-detection measures\n", "options = Options()\n", "options.add_argument(\"--start-minimized\")\n", "options.add_argument(\"--disable-blink-features=AutomationControlled\")\n", "options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n", "options.add_experimental_option('useAutomationExtension', False)\n", "options.add_argument(\n", "    \"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\")\n", "\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "# Execute script to remove webdriver property\n", "driver.execute_script(\n", "    \"Object.defineProperty(navigator, 'webdriver', {get: () => undefined})\")\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 60).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    page = 6\n", "    last_page = False\n", "    while True:\n", "        i = 0\n", "        if last_page:\n", "            break  # all cards of last page scraped\n", "        while True:\n", "            driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "            # Wait for the scripts page to load by waiting for script cards\n", "            WebDriverWait(driver, 60).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "            )\n", "            for count in range(page):\n", "                next_button = driver.find_element(\n", "                    By.XPATH, \"//button[@aria-label='Go to next page']\")\n", "                if \"disabled\" in next_button.get_attribute(\"class\"):\n", "                    last_page = True\n", "\n", "                next_button.click()\n", "\n", "                # Wait for new page to load by waiting for script cards to refresh\n", "                WebDriverWait(driver, 60).until(\n", "                    EC.presence_of_element_located(\n", "                        (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "                )\n", "\n", "            cards = WebDriverWait(driver, 60).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"(//div[contains(@class, 'jss308')])\"))\n", "            )\n", "            # cards = cards[:1]\n", "\n", "            if i > len(cards) - 1:\n", "                page += 1\n", "                break\n", "\n", "            card = cards[i]\n", "            patient_uuid = uuid.uuid4()\n", "            name = card.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "            actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "            actions.move_to_element(card).perform()\n", "\n", "            # 7. Click \"Open Script\" button that appears\n", "            open_button = WebDriverWait(driver, 60).until(\n", "                EC.element_to_be_clickable(\n", "                    (By.XPATH, f\"(//span[text()='Open Script'])[{i+1}]\"))\n", "            )\n", "            open_button.click()\n", "\n", "            # 8. Wait for tab list to load (using tour-id which is stable)\n", "            tabs = WebDriverWait(driver, 60).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "            )\n", "\n", "            # Initialize the main data structure\n", "            script_data = {\n", "                \"metadata\": {\n", "                    \"name\": name,\n", "                    \"scraped_at\": datetime.now().isoformat(),\n", "                    \"script_url\": driver.current_url\n", "                },\n", "                \"tabs\": {}\n", "            }\n", "\n", "            # 9. Iterate through each tab\n", "            for tab in tabs:\n", "                # e.g. \"tab-Doctor Information\"\n", "                tab_name = tab.get_attribute(\"tour-id\")\n", "                tab.click()\n", "\n", "                # Add small delay after clicking tab\n", "                human_like_delay(0.5, 1.5)\n", "\n", "                # Wait for the tab content to load by waiting for the active panel\n", "                WebDriverWait(driver, 60).until(\n", "                    EC.presence_of_element_located((\n", "                        By.XPATH,\n", "                        \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                    ))\n", "                )\n", "\n", "                if \"Doctor Information\" in tab_name:\n", "                    active_panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((\n", "                            By.XPATH,\n", "                            \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                        ))\n", "                    )\n", "                    raw_text = active_panel.text\n", "\n", "                    # Structure by headings\n", "                    sections = {}\n", "                    lines = raw_text.split(\"\\n\")\n", "                    current = \"Intro\"\n", "                    sections[current] = \"\"\n", "                    for line in lines:\n", "                        if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                            current = line\n", "                            sections[current] = \"\"\n", "                        else:\n", "                            sections[current] += line + \" \"\n", "\n", "                    # Clean up the sections\n", "                    cleaned_sections = {}\n", "                    for k, v in sections.items():\n", "                        cleaned_sections[k] = v.strip()\n", "\n", "                    script_data[\"tabs\"][\"doctor_information\"] = cleaned_sections\n", "\n", "                elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "                    # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    # Inside that panel, wait for the content container\n", "                    container = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "                    )\n", "\n", "                    # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "                    sections = {}\n", "                    headings = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_all_elements_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "                    )\n", "\n", "                    for heading in headings:\n", "                        title = heading.text.strip()\n", "                        try:\n", "                            ul = heading.find_element(\n", "                                By.XPATH, \"following-sibling::ul[1]\")\n", "                            items = [li.text.strip()\n", "                                     for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                            sections[title] = items\n", "                        except:\n", "                            sections[title] = []\n", "\n", "                    script_data[\"tabs\"][\"script\"] = sections\n", "\n", "                elif \"Marking Rubric\" in tab_name:\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    medical_history_tabs = panel.find_elements(\n", "                        By.XPATH, \".//li[@role='tab']\")\n", "\n", "                    target_categories = ['HOPC', 'Assoc.',\n", "                                         'PMHx', 'FMHx', 'Social', 'Basics']\n", "                    marking_rubric_data = {}\n", "\n", "                    for med_tab in medical_history_tabs:\n", "                        med_tab_name = med_tab.text.strip()\n", "\n", "                        if med_tab_name not in target_categories:\n", "                            continue\n", "\n", "                        try:\n", "                            med_tab.click()\n", "                            # Add small delay after clicking medical tab\n", "                            human_like_delay(0.5, 1.0)\n", "                            # Wait for the medical tab content to load\n", "                            WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, f\"//div[@role='tabpanel' and @id='{med_tab.get_attribute('aria-controls')}']\"))\n", "                            )\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                            continue\n", "\n", "                        med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                        med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                        try:\n", "                            med_panel = WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, med_panel_xpath))\n", "                            )\n", "\n", "                            if not med_panel.text.strip():\n", "                                print(f\"No content found for {med_tab_name}\")\n", "                                marking_rubric_data[med_tab_name] = []\n", "                                continue\n", "\n", "                            rubric_data = extract_rubric_data(med_panel)\n", "                            clean_rubric_data = deduplicate_rubric_data(\n", "                                rubric_data)\n", "\n", "                            marking_rubric_data[med_tab_name] = clean_rubric_data\n", "\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not find panel for {med_tab_name}: {e}\")\n", "                            marking_rubric_data[med_tab_name] = []\n", "                            continue\n", "\n", "                    script_data[\"tabs\"][\"marking_rubric\"] = marking_rubric_data\n", "\n", "            # Save individual patient data to organized folder structure\n", "            save_patient_data(patient_uuid, script_data, page + 1)\n", "            i += 1\n", "\n", "            # Add delay between processing cards to avoid rate limiting\n", "            if i < len(cards):  # Don't delay after the last card\n", "                human_like_delay(2, 5)  # 2-5 second delay between cards\n", "\n", "    print(\"All patient data has been saved to individual files in oscer_cases folder\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": null, "id": "64b7f591-88ef-4c9b-b816-de9781fc8aca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}