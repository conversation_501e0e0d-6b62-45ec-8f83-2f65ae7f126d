import time
import json
import uuid
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager

# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")
driver = webdriver.Chrome(service=Service(
    ChromeDriverManager().install()), options=options)


def extract_rubric_data(panel_element):
    """Extract hierarchical rubric data without using dynamic jss classes"""
    import re

    def parse_accordion(accordion_elem, level=0):
        """Parse a single accordion element"""
        result = {}

        # Find summary element using stable MUI class
        try:
            summary = accordion_elem.find_element(
                By.XPATH, ".//div[contains(@class, 'MuiAccordionSummary-root')]")

            # Get the full text content from the summary
            summary_text = summary.text.strip()

            # Split by lines and clean up
            lines = [line.strip() for line in summary_text.split(
                '\n') if line.strip()]

            # Parse the content - look for title and score patterns
            title = ""
            score = None

            # The summary text might be like: "Quality\n0\n/1" or "Quality 0/1"
            full_text = ' '.join(lines)

            # Try to find score pattern in the full text
            score_match = re.search(
                r'(\d+)\s*/\s*(\d+)', full_text)
            if score_match:
                score = int(score_match.group(2))
                # Remove the score part to get the title
                title = re.sub(r'\s*\d+\s*/\s*\d+\s*',
                               '', full_text).strip()
            else:
                # No score found, the whole text is the title
                title = full_text

            # Fallback: if title is empty, try the first line
            if not title and lines:
                title = lines[0]

            result['title'] = title
            if score:
                result['score'] = score

        except Exception as e:
            print(f"Error parsing accordion summary: {e}")
            pass

        # Find details element and look for nested content
        try:
            details = accordion_elem.find_element(
                By.XPATH, ".//div[contains(@class, 'MuiAccordionDetails-root')]")

            # Look for checkboxes (leaf items)
            checkboxes = details.find_elements(
                By.XPATH, ".//input[@type='checkbox']")
            if checkboxes:
                items = []
                for checkbox in checkboxes:
                    name = checkbox.get_attribute('name')
                    if name and name.strip():
                        items.append(name.strip())
                if items:
                    result['items'] = items

            # Look for nested accordions (subcategories)
            nested_accordions = details.find_elements(
                By.XPATH, ".//div[contains(@class, 'MuiAccordion-root')]")
            if nested_accordions:
                subcategories = []
                for nested in nested_accordions:
                    try:
                        nested_result = parse_accordion(
                            nested, level + 1)
                        if nested_result and nested_result.get('title'):
                            subcategories.append(nested_result)
                    except:
                        continue
                if subcategories:
                    result['subcategories'] = subcategories
        except:
            pass

        return result

    # Strategy 1: Look for main accordions (big headings)
    all_accordions = panel_element.find_elements(
        By.XPATH, ".//div[contains(@class, 'MuiAccordion-root')]")

    # First pass: collect all potential main categories with their nesting info
    potential_categories = []

    for accordion in all_accordions:
        try:
            parsed = parse_accordion(accordion)
            if parsed and parsed.get('title'):
                title = parsed['title']

                # Check if this is a true nested accordion or a separate main category
                try:
                    parent_accordion = accordion.find_element(
                        By.XPATH, "ancestor::div[contains(@class, 'MuiAccordion-root')]")

                    # Even if it has a parent, it might still be a main category
                    # Check the nesting level and content to decide
                    nesting_level = len(accordion.find_elements(
                        By.XPATH, "ancestor::div[contains(@class, 'MuiAccordion-root')]"))

                    # Generic approach: determine if this should be treated as a main category
                    # Based on nesting level and relative importance within the structure

                    # score = parsed.get('score', {})
                    # score_total = score.get('total', 0) if score else 0
                    score_total = parsed.get('score', 0)

                    should_include = False

                    if nesting_level <= 1:
                        # Top-level or first-level nested - always include as main categories
                        should_include = True
                    elif nesting_level == 2:
                        # Second-level nested - include if it seems significant
                        # Use relative scoring: if this category's score is significant compared to others
                        # or if it has substantial content (items or subcategories)
                        has_content = ('items' in parsed and len(parsed['items']) > 0) or \
                            ('subcategories' in parsed and len(
                                parsed['subcategories']) > 0)

                        # Include if it has a meaningful score (> 0) or substantial content
                        if score_total > 0 or has_content:
                            should_include = True
                    elif nesting_level == 3:
                        # Third-level nested - only include if it has significant content
                        # This catches deeply nested but important categories
                        has_substantial_content = ('items' in parsed and len(parsed['items']) >= 3) or \
                            ('subcategories' in parsed and len(
                                parsed['subcategories']) >= 2)

                        if score_total > 0 and has_substantial_content:
                            should_include = True

                    if should_include:
                        # Store potential category with nesting info for later processing
                        potential_categories.append({
                            'parsed': parsed,
                            'title': title,
                            'nesting_level': nesting_level,
                            'score_total': score_total
                        })

                except:
                    # No parent accordion found, this is definitely a top-level one
                    # score = parsed.get('score', {})
                    # score_total = score.get('total', 0) if score else 0
                    score_total = parsed.get('score', 0)

                    potential_categories.append({
                        'parsed': parsed,
                        'title': title,
                        'nesting_level': 0,  # Top-level
                        'score_total': score_total
                    })

        except Exception as e:
            print(f"Error parsing accordion: {e}")
            continue

    # Helper function to check if a title is a subcategory of any potential category
    def _is_subcategory_of_any(title, potential_cats):
        for cat_info in potential_cats:
            category = cat_info['parsed']
            if _is_subcategory_of_recursive(title, category):
                return True, category.get('title', 'Unknown')
        return False, None

    def _is_subcategory_of_recursive(title, category):
        if 'subcategories' in category:
            for subcat in category['subcategories']:
                if subcat.get('title') == title:
                    return True
                # Check recursively in nested subcategories
                if _is_subcategory_of_recursive(title, subcat):
                    return True
        return False

    # Second pass: determine which categories should be main categories
    # Sort by nesting level (top-level first) and score (higher scores first)
    potential_categories.sort(key=lambda x: (
        x['nesting_level'], -x['score_total']))

    main_categories = []
    final_processed_titles = {}

    for cat_info in potential_categories:
        parsed = cat_info['parsed']
        title = cat_info['title']
        nesting_level = cat_info['nesting_level']
        score_total = cat_info['score_total']

        # Check if this category is already a subcategory of ANY potential main category
        # (not just the ones we've already processed)
        # BUT exclude self-references (e.g., "Past Medical History" containing "Past Medical History")
        is_subcategory_elsewhere, parent_title = _is_subcategory_of_any(
            title, potential_categories)

        # Special case: if the parent title is the same as the current title,
        # this might be a self-reference, so we need to check if this is the main category
        if is_subcategory_elsewhere and parent_title == title:
            # This is a self-reference case like "Past Medical History" containing "Past Medical History"
            # Keep the one with the higher score as the main category
            current_score = score_total

            # Find the parent category's score
            parent_score = 0
            for other_cat_info in potential_categories:
                if other_cat_info['title'] == parent_title and other_cat_info != cat_info:
                    parent_score = other_cat_info['score_total']
                    break

            if current_score >= parent_score:
                # This category has equal or higher score, so it should be the main one
                is_subcategory_elsewhere = False

        if not is_subcategory_elsewhere:
            # Add a suffix to distinguish multiple instances of the same title
            if title in final_processed_titles:
                final_processed_titles[title] += 1
                unique_title = f"{title} ({final_processed_titles[title]})"
                parsed['title'] = unique_title
            else:
                final_processed_titles[title] = 1

            main_categories.append(parsed)

    # Strategy 2: If no main accordions found, look for alternative content structures
    if not main_categories:
        print("No main accordions found, looking for alternative content structures...")

        # Look for direct checkboxes (flat structure without main headings)
        checkboxes = panel_element.find_elements(
            By.XPATH, ".//input[@type='checkbox']")
        if checkboxes:
            items = []
            for checkbox in checkboxes:
                name = checkbox.get_attribute('name')
                if name and name.strip():
                    items.append(name.strip())

            if items:
                # Create a category for direct items
                direct_category = {
                    'title': 'Items',
                    'items': items
                }
                main_categories.append(direct_category)
                # print(f"Found {len(items)} direct checkbox items")

        # Look for nested accordions that might not have a main container
        # (This is now less needed since Strategy 1 handles nested accordions better)
        nested_accordions = panel_element.find_elements(
            By.XPATH, ".//div[contains(@class, 'MuiAccordion-root')]")
        for accordion in nested_accordions:
            try:
                parsed = parse_accordion(accordion)
                if parsed and parsed.get('title'):
                    # Avoid duplicates from the main accordion search above
                    # Check both exact title match and numbered versions
                    existing_titles = [cat.get('title', '')
                                       for cat in main_categories]
                    base_title = parsed['title']

                    # Check if this title (or a numbered version) already exists
                    title_exists = any(
                        existing_title == base_title or
                        existing_title.startswith(f"{base_title} (")
                        for existing_title in existing_titles
                    )

                    if not title_exists:
                        main_categories.append(parsed)
            except Exception as e:
                print(f"Error parsing nested accordion: {e}")
                continue

    return main_categories


def deduplicate_rubric_data(categories):
    """Remove duplicate entries where items appear as both subcategories and standalone items"""

    def collect_all_subcategory_titles(cats):
        """Collect all titles that appear as subcategories"""
        titles = set()
        for cat in cats:
            if 'subcategories' in cat:
                for subcat in cat['subcategories']:
                    titles.add(subcat['title'])
                    # Recursively collect from deeper levels
                    titles.update(
                        collect_all_subcategory_titles([subcat]))
        return titles

    def clean_category(category, all_subcat_titles):
        """Clean a single category by removing duplicate items and subcategories"""
        cleaned = {
            'title': category['title']
        }

        # Add score if present
        if 'score' in category:
            cleaned['score'] = category['score']

        # Process subcategories first (recursively)
        if 'subcategories' in category:
            cleaned_subcats = []

            # Collect titles of subcategories that have their own subcategories
            # BUT exclude self-references (e.g., "Time Course" containing "Time Course")
            parent_subcats = set()
            for subcat in category['subcategories']:
                if 'subcategories' in subcat:
                    for nested in subcat['subcategories']:
                        # Only add if it's not a self-reference
                        if nested['title'] != subcat['title']:
                            parent_subcats.add(nested['title'])

            # First, identify the best version of each duplicate title
            title_to_best_subcat = {}
            for subcat in category['subcategories']:
                title = subcat['title']
                if title not in title_to_best_subcat:
                    title_to_best_subcat[title] = subcat
                else:
                    # Compare with existing: prefer the one with subcategories
                    existing = title_to_best_subcat[title]
                    current_has_subcats = 'subcategories' in subcat
                    existing_has_subcats = 'subcategories' in existing

                    if current_has_subcats and not existing_has_subcats:
                        # Current is better (has subcategories)
                        title_to_best_subcat[title] = subcat
                    elif current_has_subcats and existing_has_subcats:
                        # Both have subcategories, prefer the one with more subcategories
                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):
                            title_to_best_subcat[title] = subcat

            # Now process the best version of each subcategory
            for title, best_subcat in title_to_best_subcat.items():
                # Skip if this title is a subcategory of another subcategory at this level
                if title not in parent_subcats:
                    cleaned_subcat = clean_category(
                        best_subcat, all_subcat_titles)
                    cleaned_subcats.append(cleaned_subcat)

            if cleaned_subcats:
                cleaned['subcategories'] = cleaned_subcats

        # Process items - only include items that are NOT subcategory titles
        if 'items' in category:
            # If this category has subcategories, don't include items that belong to subcategories
            if 'subcategories' in cleaned:
                # Collect all items that belong to subcategories
                subcat_items = set()
                for subcat in cleaned['subcategories']:
                    if 'items' in subcat:
                        subcat_items.update(subcat['items'])
                    # Also collect items from deeper subcategories

                    def collect_deep_items(cat):
                        items = set()
                        if 'items' in cat:
                            items.update(cat['items'])
                        if 'subcategories' in cat:
                            for sc in cat['subcategories']:
                                items.update(
                                    collect_deep_items(sc))
                        return items
                    subcat_items.update(collect_deep_items(subcat))

                # Only keep items that don't belong to any subcategory
                filtered_items = []
                for item in category['items']:
                    if item not in subcat_items and item not in all_subcat_titles:
                        filtered_items.append(item)

                if filtered_items:
                    cleaned['items'] = filtered_items
            else:
                # No subcategories, keep all items
                cleaned['items'] = category['items']

        return cleaned

    # For the multi-category extraction approach, we want to keep all extracted categories
    # as main categories, even if they appear as subcategories elsewhere
    # This is different from the original single-category approach

    # First pass: collect all subcategory titles (for internal deduplication within categories)
    all_subcat_titles = collect_all_subcategory_titles(categories)

    # Second pass: clean each category but keep all extracted main categories
    cleaned_categories = []
    processed_titles = set()

    for category in categories:
        title = category['title']
        # Process all categories that were explicitly extracted as main categories
        # Don't filter based on subcategory appearance since we want them as main categories
        if title not in processed_titles:
            cleaned = clean_category(category, all_subcat_titles)
            cleaned_categories.append(cleaned)
            processed_titles.add(title)

    return cleaned_categories


def format_rubric_display(categories, indent=0):
    """Format rubric data for display"""
    output = []
    indent_str = "    " * indent

    for category in categories:
        title = category.get('title', 'Unknown')
        score = category.get('score')

        if score:
            score_str = f" {score}"
        else:
            score_str = ""

        output.append(f"{indent_str}- {title}{score_str}")

        # Add subcategories
        if 'subcategories' in category:
            sub_output = format_rubric_display(
                category['subcategories'], indent + 1)
            output.extend(sub_output)

        # Add items (checkboxes)
        if 'items' in category:
            for item in category['items']:
                output.append(f"{indent_str}        {item}")

    return output


def save_to_json(data, filename):
    """Save data to JSON file with proper formatting"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"Data saved to {filename}")
    except Exception as e:
        print(f"Error saving to JSON: {e}")


try:
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. After login, go directly to Scripts page
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )
    driver.get("https://www.oscer.ai/dashboard/scripts")

    # 5. Wait until script cards load
    first_card = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located(
            (By.XPATH, "(//div[contains(@class, 'jss308')])[1]"))
    )
    print(f"first_card: {first_card}")
    # exit(0)

    # 6. Hover over the first card to reveal "Open Script"
    actions = ActionChains(driver)
    actions.move_to_element(first_card).perform()

    # 7. Click "Open Script" button that appears
    open_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "(//span[text()='Open Script'])[1]"))
    )
    open_button.click()

    # 8. Wait for tab list to load (using tour-id which is stable)
    tabs = WebDriverWait(driver, 10).until(
        EC.presence_of_all_elements_located(
            (By.XPATH, "//ul[@role='tablist']//div[@tour-id]"))
    )

    # Initialize the main data structure
    script_data = {
        "metadata": {
            "scraped_at": datetime.now().isoformat(),
            "script_url": driver.current_url
        },
        "tabs": {}
    }

    # 9. Iterate through each tab
    for tab in tabs:
        # e.g. "tab-Doctor Information"
        tab_name = tab.get_attribute("tour-id")
        tab.click()
        time.sleep(2)  # allow content to load

        print(f"\n===== {tab_name} =====")

        if "Doctor Information" in tab_name:
            active_panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((
                    By.XPATH,
                    "//div[@role='tabpanel' and not(contains(@style,'display: none'))]"
                ))
            )
            raw_text = active_panel.text
            print("\n===== Doctor Information (raw) =====\n", raw_text)

            # Structure by headings
            sections = {}
            lines = raw_text.split("\n")
            current = "Intro"
            sections[current] = ""
            for line in lines:
                if line in ["Requirements", "Finished?", "Ready to see your results?"]:
                    current = line
                    sections[current] = ""
                else:
                    sections[current] += line + " "

            # Clean up the sections
            cleaned_sections = {}
            for k, v in sections.items():
                cleaned_sections[k] = v.strip()

            script_data["tabs"]["doctor_information"] = cleaned_sections

            print("\n===== Doctor Information (structured) =====")
            for k, v in cleaned_sections.items():
                print(f"\n{k}:\n{v}")

        elif "Script" in tab_name:
            # Find the associated panel id from the selected tab's parent <li role="tab">
            tab_li = tab.find_element(By.XPATH, "ancestor::li[@role='tab']")
            panel_id = tab_li.get_attribute("aria-controls")

            # Wait for the correct tabpanel by id (e.g., react-tabs-3)
            panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
            panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, panel_xpath))
            )

            # Inside that panel, wait for the content container
            container = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located(
                    (By.XPATH, f"{panel_xpath}//div[@id='containerElement']"))
            )

            # Now extract sections: each <p id="..."> followed by its <ul>
            sections = {}
            headings = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located(
                    (By.XPATH, f"{panel_xpath}//div[@id='containerElement']//p[@id]"))
            )

            for heading in headings:
                title = heading.text.strip()
                try:
                    ul = heading.find_element(
                        By.XPATH, "following-sibling::ul[1]")
                    items = [li.text.strip()
                             for li in ul.find_elements(By.TAG_NAME, "li")]
                    sections[title] = items
                except:
                    sections[title] = []

            script_data["tabs"]["script"] = sections

            # Print nicely (keeps your original output style)
            print("\n===== Script (structured) =====")
            for section, items in sections.items():
                print(f"\n{section}:")
                for item in items:
                    print(f" - {item}")

        elif "Marking Rubric" in tab_name:

            # Find the associated panel id from the selected tab's parent <li role="tab">
            tab_li = tab.find_element(By.XPATH, "ancestor::li[@role='tab']")
            panel_id = tab_li.get_attribute("aria-controls")

            # Wait for the correct tabpanel by id
            panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
            panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, panel_xpath))
            )

            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)
            medical_history_tabs = panel.find_elements(
                By.XPATH, ".//li[@role='tab']")

            # Define the medical history categories we want to process
            target_categories = ['HOPC', 'Assoc.',
                                 'PMHx', 'FMHx', 'Social', 'Basics']

            marking_rubric_data = {}

            for med_tab in medical_history_tabs:
                med_tab_name = med_tab.text.strip()

                # Only process the medical history categories
                if med_tab_name not in target_categories:
                    print(f"Skipping medical history tab: {med_tab_name}")
                    continue

                print(f"\n===== Processing {med_tab_name} =====")

                # Click the medical history tab
                try:
                    med_tab.click()
                    time.sleep(2)  # Wait for content to load
                except Exception as e:
                    print(
                        f"Could not click medical history tab {med_tab_name}: {e}")
                    continue

                # Find the associated panel for this medical history tab
                med_panel_id = med_tab.get_attribute("aria-controls")
                med_panel_xpath = f"//div[@role='tabpanel' and @id='{med_panel_id}']"

                try:
                    med_panel = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located(
                            (By.XPATH, med_panel_xpath))
                    )

                    # Check if the panel has content
                    if not med_panel.text.strip():
                        print(f"No content found for {med_tab_name}")
                        marking_rubric_data[med_tab_name] = []
                        continue

                    rubric_data = extract_rubric_data(med_panel)
                    clean_rubric_data = deduplicate_rubric_data(rubric_data)

                    # Store in the structured format
                    marking_rubric_data[med_tab_name] = clean_rubric_data

                    print("\n===== Marking Rubric (structured) =====")
                    formatted_output = format_rubric_display(clean_rubric_data)
                    for line in formatted_output:
                        print(line)

                except Exception as e:
                    print(f"Could not find panel for {med_tab_name}: {e}")
                    marking_rubric_data[med_tab_name] = []
                    continue

            # Add marking rubric data to the main structure
            script_data["tabs"]["marking_rubric"] = marking_rubric_data

    # Save all data to JSON file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"oscer_data.json"
    save_to_json(script_data, filename)

    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(script_data, f, indent=4, ensure_ascii=False)
    print(f"Pretty-printed data saved to {filename}")

    print(f"\n===== JSON Structure Summary =====")
    print(f"Total tabs processed: {len(script_data['tabs'])}")
    # for tab_key, tab_data in script_data["tabs"].items():
    #     print(f"- {tab_key}: {tab_data['type']}")
    #     if tab_data['type'] == 'marking_rubric':
    #         for category, category_data in tab_data['medical_history_categories'].items():
    #             num_categories = len(category_data['categories'])
    #             print(f"  - {category}: {num_categories} categories")

finally:
    pass  # keep browser open for debugging
