{"metadata": {"name": "Naseefa, 36M", "scraped_at": "2025-09-03T00:25:14.502124", "script_url": "https://www.oscer.ai/pwf/script/uHntsSELRJ0h"}, "tabs": {"doctor_information": {"Intro": "<PERSON><PERSON><PERSON> is a 36 year old male who has presented today to the emergency department with a headache. Please take a history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON>, I’m 36 years old and I’m a lawyer. My pronouns are he/him.", "I’ve come into the hospital because I’ve had this excruciating headache for the past 24 hours!"], "Persona": ["I’m most concerned about having to miss work, I’ve already taken time off work recently because of pneumonia.", "When the fever and the rash started I got really concerned and got a friend to bring me into the hospital."], "HOPC": ["I had this terrible headache that won’t go away, it’s been there for the past 24 hours or so.", "The headache is not on one side specifically, I’m having generalized pain all around my head.", "It feels like a throbbing pain all over my head.", "It all came on quite suddenly, I started feeling terrible out of nowhere!", "If I had to give it a number, this headache is an 8/10, it’s excruciating!", "I’ve been taking paracetamol every 4 hours for the last 24 hours, but it hasn’t made a difference at all!", "I didn’t notice anything strange before the headache came on.", "My neck has started to feel awfully stiff, and bending it really aggravates the pain in my head.", "Being in the waiting room was awful, the lights feel like they’re boring a hole in my head!", "It’s made me feel totally nauseous as well, I’ve vomited several times.", "I haven’t noticed anything unusual about the amount, colour or odour of the vomits.", "I’ve been feeling feverish too, I measured my temperature at home and it was 38.7. That was when I thought I should come into hospital.", "Recently I had pneumonia, but that all resolved itself a few days ago.", "I haven’t noticed any blood in my vomit."], "PMHx": ["Aside from the bout of pneumonia I had recently, I don’t have any significant past medical conditions thankfully.", "I had to take a course of antibiotics to clear up the pneumonia, and then I’ve taken panadol for the headache, but I’m not on any medications usually.", "I don’t have any allergies.", "I don’t use any other supplements or over the counter medications.", "My vaccinations should be all up to date."], "FMHx": ["Dad takes blood pressure tablets for hypertension, but his doctor says it’s all been well controlled after he made a few changes in his life", "Other than that, there’s no major medical conditions in my family."], "SHx": ["I’m a social drinker, I drink 4 to 5 beers on a Friday night with my colleagues.", "I don’t smoke, I tried a few cigarettes when I was younger but never got into it.", "I smoked some cannabis in my early twenties when I was at University, but I’ve never done any other recreational drugs.", "I find my work pretty stressful, I have to do quite long hours, that’s why I can’t afford any more time off with a headache like this!", "I don’t have a partner, I live at home by myself.", "I eat a well-balanced diet, I do a lot of meal preparation because work is always so busy.", "We’ve got a gym at the office, so I get to exercise regularly which is nice."]}, "marking_rubric": {"HOPC": [{"title": "Headache", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Tension", "Throbbing", "Thunderclap", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Sided", "Asking Generally", "Generalised or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Ability to Sleep", "Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Darkness", "Quietness", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Stress", "Caffeine", "Injuries", "Medications", "Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Crying", "score": 1, "items": ["Crying"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Jaw Pain", "score": 1, "items": ["Jaw Pain"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Neck Pain", "score": 1, "items": ["Neck Pain"]}, {"title": "Scalp Pain", "score": 1, "items": ["Scalp Pain"]}, {"title": "Neck Stiffness", "score": 1, "items": ["Neck Stiffness"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Blurry Vision", "Asking Generally"]}, {"title": "Nasal <PERSON>harge", "score": 1}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Speech Difficulty", "score": 1, "items": ["Speech Difficulty"]}, {"title": "Sensitive to Light", "score": 1, "items": ["Sensitive to Light"]}, {"title": "Sensitive to Sound", "score": 1, "items": ["Sensitive to Sound"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Agitation", "Confusion", "Irritability"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Migraine", "Asking Generally", "Cluster Headaches"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally", "Marfan's Syndrome"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications", "Oestrogen Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Migraine", "Asking Generally", "Cluster Headaches", "Neurological Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Meningitis", "Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}], "Basics": []}}}