{"metadata": {"name": "Adelaide, 42F", "scraped_at": "2025-09-05T12:46:12.345574", "script_url": "https://www.oscer.ai/pwf/script/dPZpASwfzumV"}, "tabs": {"doctor_information": {"Intro": "You are a medical student working at the local general practice. Your next patient is <PERSON>, a 42 year old female (she/her) who presents with a neck lump. Please take a focused history from Adelaide with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 42 year-old female (pronouns she/her).", "I have come to visit the doctors about this neck lump."], "Persona": ["I'm quite anxious about what this lump could be as I'm a bit of a hypochondriac.", "Normally, I am a very outgoing person and I'm have never been afraid to share my mind."], "HOPC": ["I noticed my neck looked a little bit swollen in the mirror a coule of weeks ago. Having felt around the area, I realised there was a number of small lumps.", "There are multiple lumps which are located just to the right of the middle of my neck. I haven't noticed any lumps anywhere else.", "The lumps are not tender when I feel them, but they feel soft.", "For the 2 months prior to now, I've struggled to deal with heat.", "I have noticed that I have lost about 4kg over the same period of time, yet I haven't changed my diet or intake of food.", "I now find it very difficult to get a full nights sleep, I seem to toss and turn but never truely get any rest.", "Looking in the mirror at times, I can see that my face even looks quite red even if I haven't exerted myself.", "At times I feel quite anxious and my heart feels like its racing uncontrollably.", "My period seems to have ceased but I thought that may have just been related to me going through menopause early."], "PMHx": ["I have had a clean bill of health for the majority of my life.", "I was diagnosed with asthma as a child, but it has never given me any problems.", "I have never had any form of surgery before, but it fascinates me what it must be like going under anaesthesia!", "I have only ever visited family in hospital, never been admitted myself.", "I don't take any regular medications, only the occassional ibuprofen or paracetamol as needed.", "I have no allergies and my immunisations are up to date including the COVID-19 vaccine."], "FMHx": ["My father is 72-years-old and doing well.", "My mother is 68-years-old and was diagonosed with T2DM about 20 years ago, yet has is otherwise well. She has had no complications from her diabetes.", "I am an only child, so I have a very close relationship with my parents."], "SHx": ["I live at home alone with my golden retriever named <PERSON>.", "In terms of work, I am a digital artist by day but I am also heavily involved in cryptocurrency. Recently, I even created my own line of non-fudgible tokens (NFTs) as a new medium to sell my art.", "To get my exercise, I tend to go to the gym between 3-4 times per week. I mainly do high-intensity interval training (HIIT).", "My diet largely consists of lots of fish and fresh vegetables. I'm not a fan of large quantities of red-meat or processed foods.", "I have been feeling more hungry and eating more over the last couple of months despite the weight change.", "I haven't had alcohol for about 5-years. Prior to that I would have the ocassional wine on the weekends.", "I definitely don't smoke, I find it disgusting!", "I previously lived in Brazil, but then moved to Australia 15 years ago.", "I haven't travelled anywhere for a long time."]}, "marking_rubric": {"HOPC": [{"title": "Neck Lump", "score": 11, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 1, "items": ["Painful", "Mobility", "Regularity", "Consistency", "Asking Generally", "Moves on Swallowing"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Quantitative", "score": 1, "items": ["Size"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Bulging Eyes", "score": 1, "items": ["Bulging Eyes", "Asking Generally"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Skin Changes", "score": 1, "items": ["Depigmentation", "Asking Generally"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Appetite Change", "score": 1, "items": ["Increased Appetite"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Asking Generally"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Anxiousness", "Asking Generally"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}, {"title": "Altered Menstrual Cycle", "score": 1, "items": ["Asking Generally", "Irregular Menstrual Cycle"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Asking Generally"]}], "PMHx": [{"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Thyroxine", "Asking Generally", "Past Medications"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Endocrine Disease History", "score": 1, "items": ["Thyroid Cancer", "Thyroid Disease", "Asking Generally", "Multiple Endocrine Neoplasia"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Infectious Mononucleosis"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Thyroid Cancer", "Grave's Disease", "Hyperthyroidism", "Thyroid Disease", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally", "Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}