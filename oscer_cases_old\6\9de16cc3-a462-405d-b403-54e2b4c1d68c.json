{"metadata": {"name": "<PERSON>, 23F", "scraped_at": "2025-09-03T00:41:30.146177", "script_url": "https://www.oscer.ai/pwf/script/crq5E22NeTsz"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 23 year old female, who has presented today to general practice complaining of a cough. You have been asked to see <PERSON>, and to take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m a 23 year old female and I work as a running coach. My pronouns are she/her.", "I’ve come in to see the doctor because I’ve had this cough that I can’t seem to shake.", "I’ve also been having periods of feeling wheezy, and like I can’t catch my breath."], "Persona": ["Feeling short of breath isn’t a nice feeling at all, I find it a bit scary when it happens.", "I’m a running coach, so I want to be able to work and train without it affecting me!"], "HOPC": ["I have had this really annoying cough, it feels like I have to constantly clear my throat.", "It’s been going on for the past few months, but I notice it a lot more during winter, especially in the morning!", "Sometimes I bring up a bit of white sputum too.", "I’m not bringing up a great deal of sputum, it's just little bits and pieces.", "I've never brought up any blood.", "I find I’m getting episodes of feeling a bit wheezy, particularly at night!", "I can also have periods where I feel wheezy after being around cats and those cold running mornings.", "I’ve also been having episodes where I’m feeling breathless.", "It feels like I can’t get enough air in, no matter what I do.", "On the cold mornings when I’m running, sometimes I feel like I need an hour before I can get my breath back!", "I always find all these things get especially bad when I’m visiting my grandma, she has a pet cat.", "The winter mornings when I’m coaching running also seem to make everything worse.", "I find it can be worse after exercising too, some of my long distance runs can get pretty intense and I just can’t train at the level I used to anymore.", "I haven’t found anything that’s been particularly effective at making things better, only leaning forward for the breathlessness.", "I’ve also been pretty tired lately, I feel like this is all really taking it out of me.", "I have not had a fever or any night sweats.", "I wasn’t sick before this cough started.", "I have noticed any changes in my smell or taste.", "I have not had any chest or leg pain.", "I have not noticed any unusual swelling anywhere."], "PMHx": ["The last few years it feels like I’m struggling with hay fever more than ever, I never look forward to spring because I just get constantly sniffly!", "I broke my arm when I was a child but apart from that, I’ve never been to hospital before.", "I take the oral contraceptive pill, but I’m not on any other regular medications.", "I take antihistamines from the pharmacy in spring-time.", "I take a vegetarian multivitamin, but no other supplements.", "All of my immunisations are up to date.", "I don’t think I’m properly allergic to anything. I guess I get some hay fever symptoms around my grandma's cats also, but I’ve never been officially diagnosed."], "FMHx": ["My <PERSON> had troubles with some low thyroid level a few years ago, but she’s been perfectly healthy and well since it was diagnosed!", "Everyone else in my family is healthy.", "I have two siblings, brother and sister, who are both healthy."], "SHx": ["I work as a running coach, and it can get pretty cold on some morning runs!", "I keep pretty active through work, and I still do a lot of training in the gym.", "I’ve been finding it hard to train in the gym like I used to, I can feel so short of breath during it.", "I’ve never had a cigarette before.", "I’m only really a social drinker, especially because I have to get up early for training.", "On the weekends if I’m having a night out I might drink 3 or 4 glasses of wine, but that’s it for the week.", "I’ve never tried any recreational drugs, I’ve always been conscious of what I put in my body!", "I’ve been a vegetarian since I was a teenager. But I think I eat quite a well-balanced diet, I get lots of leafy greens and protein from other places, plus my multivitamin.", "I live out of home now, I’m in a share house with some friends who are great, they’re the ones that encouraged me to come in.", "I haven’t traveled anywhere recently."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 13, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Character", "score": 1, "items": ["Coughing Fit", "Asking Generally", "Productive Cough"]}, {"title": "Sputum Amount", "score": 1, "items": ["Asking Generally", "Cups or Teaspoons", "Frequency of Sputum"]}, {"title": "Sputum Colour", "score": 1, "items": ["White", "Red or Pink", "Green, Yellow or Brown"]}, {"title": "Sputum Character", "score": 1, "items": ["Blood", "Frothy", "<PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Work", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Allergens", "Positional", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Morning or Night-time", "Season or Temperature", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Chest Tightness", "score": 1, "items": ["Chest Tightness"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON>sal <PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Steroids", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Dust", "<PERSON><PERSON>", "Animals", "Hayfever", "Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Pneumonia", "Asking Generally", "Respiratory Infections"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asthma", "<PERSON><PERSON><PERSON>", "Hayfever", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Passive Exposure"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}