{"metadata": {"name": "<PERSON>, 60F", "scraped_at": "2025-09-05T12:12:49.634201", "script_url": "https://www.oscer.ai/pwf/script/867I0eBy74XG"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You have been asked to see <PERSON>, a 60 year old female presenting with knee pain. Please take a history with the aim of establishing a provisional diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm a 60 year old female.", "I work as a kindergarten teacher.", "I've come to the doctors to get the pain in my knees checked out."], "Persona": ["I am worried this is going to eventually prevent me from working.", "I adore the kids but I'm having an awful time at work trying to bend down and pick up after them all day.", "If I can't work at the kindergarten I'm not sure what else I'd do, I'm 60 years old now."], "HOPC": ["For the past 6 months, I've been noticing that my knees have become painful and swollen.", "The problem is in both of my knees.", "The knee trouble came on gradually.", "The pain has been getting worse", "I also have stiffness in my knees.", "The stiffness in my knees has really been affecting my mobility.", "The pain is at its absolute worst in the mornings affecting I wake up, but after about an hour or so it starts to improve. The stiffness improves over a similar time.", "I'm having to wake up earlier just so I make it to the kindergarten on time, I'm finding getting dressed and ready for the day really hard with the pain in my knees.", "I've been using paracetamol for the pain, but it's just not helping anymore.", "The only thing that helps with the pain is warmth, so I've been having an early morning bath to give me some relief.", "My knees are a little bit red.", "I do not suffer from psoriasis or have any rashes.", "I have not injured my knees.", "I have not had any fevers.", "I have been feeling well apart from the knee pain."], "PMHx": ["I was diagnosed with high cholesterol 4 years ago.", "I take a 40mg tablet of atorvostatin once a day.", "I was also diagnosed with an underactive thyroid in my early fifties.", "I take a 80mcg tablet of levothyroxin a day for my thyroid.", "I take 1g of paracetamol three times a day for my knee pain."], "FMHx": ["My mother has arthritis through her hands and wrists, I can't remember what type they call it. My father has a bit of high blood pressure but other than that they're both healthy and well. I'm an only child, I don't have any other siblings."], "SHx": ["I live at home with my husband, our kids have all moved out now.", "We've got a son and a daughter, and we've just got our first grandchild.", "I work as a kindergarten teacher still, I love my job.", "I very occasionally drink alcohol, I have maybe one or two glasses of wine a week.", "I've never been a smoker.", "The kids keep me active running around all day, but otherwise I'm part of a walking group with some friends. We walk together 3 times a week.", "My diet is good, I've always loved cooking."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 15, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Other Joints", "Asking Generally", "Localised or Generalised"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Walk", "Asking Generally", "Ability to Exercise", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Morning", "Resting", "Exercise", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Morning", "Exercise", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wounds", "score": 1, "items": ["Wounds"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Skin Heat", "score": 1, "items": ["Skin Heat"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Joint Redness", "score": 1, "items": ["Joint Redness"]}, {"title": "Knee Deformity", "score": 1, "items": ["Tophus"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Knee Instability", "score": 1, "items": ["Knee Instability"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Asking Generally", "Septic Arthritis"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Rheumatological Disease History", "score": 1, "items": ["Joint Disease", "Rheumatoid Arthritis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Regularity", "Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Psoriatic Arthritis", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Meat-Rich Diet", "Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Heavy Lifting", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Functional History", "score": 1, "subcategories": [{"title": "Mobility", "score": 1, "items": ["Walking", "Mobility"]}, {"title": "Community Activities Of Daily Living", "score": 1, "items": ["Ability to Drive"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}