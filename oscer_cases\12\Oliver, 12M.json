{"metadata": {"name": "<PERSON>, 12M", "scraped_at": "2025-09-05T13:01:13.284851", "script_url": "https://www.oscer.ai/pwf/script/9mUZkf0HIL8Z"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based at a General Practice clinic. <PERSON> is a 12 year old male (he/him) presenting to your GP complaining of epistaxis. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I am a 12 year old male (he/him).", "I've come into the GP clinic because I've been having nose bleeds all the time and it's really inconvenient. My <PERSON> dropped me off to speak with you, she's had to head to the post office to send off some packages."], "Persona": ["My biggest concern is how annoying the nose bleeds are, they come at really inconvenient times."], "HOPC": ["I've always seemed to get frequent nose bleeds for as long as I can remember. I suppose I first remember them when I was 6 or 7 years old.", "The nose bleeds just come on out of nowhere.", "The nose bleeds also seem like they last longer than other people's, they can be really hard to stop.", "The nose bleeds last for 15 minutes or so when they happen.", "I think I average one or two nose bleeds every month.", "I also find that I bruise quite eaisly.", "I have always been a clumsy child, which is why we thought I bruised more than the other kids my age.", "I get bruises all the time. For example, if I bump into something it would leave me with a big bruise when no one else would get one.", "I had to have a tooth pulled out because it was infected, and I had a lot of bleeding after that which the dentist said wasn't normal.", "I haven't injured myself or my nose recently.", "I do not pick my nose.", "I haven't had any night sweats, fevers, or weight loss.", "I have not been unwell with any infections.", "I haven't noticed any lumps or bumps anywhere over my body.", "I haven't had a rash.", "I haven't had any dizziness or shortness of breath.", "I haven't had any changes when I go to the toilet, everything is the usual colour."], "PMHx": ["I don't have any medical conditions, and I have never had surgery or been to hospital before.", "I don't take any medications.", "I don't have any allergies, and I got all of my immunisations as a child.", "I never had any developmental issues as a kid."], "FMHx": ["I think my great grandfather on my Mother's side was always prone to bruises and nose bleeds like me I think. Other than that, my Mother and Father don't have any medical conditions, and neither does my older sister."], "SHx": ["I live at home with my parents.", "I feel safe and well supported at home.", "I just started going to high school this year.", "I play tennis as a hobby, and like to catch up with friends after school.", "My diet is good I think, it's just whatever my <PERSON> feeds us all. Like normally meat and some vegetables.", "I have never tried smoking or alcohol before, they don't seem very nice."]}, "marking_rubric": {"HOPC": [{"title": "Epistaxis", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Blood in Throat", "Asking Generally"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Side"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to Stop Nosebleeds"]}, {"title": "Quantitative", "score": 1, "items": ["Amount", "Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Diurnal Variation", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Warm Weather", "Foreign Object", "Context at Onset", "Lifestyle Changes", "Sneezing or Coughing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Bleeding", "score": 1}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Bone Pain", "score": 1, "items": ["Bone Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Bleeding Gums", "score": 1, "items": ["Bleeding Gums"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Joint Bleeding", "score": 1, "items": ["Joint Bleeding"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Urinating Blood", "Asking Generally"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Defecating Blood", "Black and Tarry Stools"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Haemophilia", "Asking Generally", "Bleeding Disorders", "<PERSON>"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Diet at Baseline"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Domestic Abuse", "score": 1, "items": ["Domestic Abuse"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}