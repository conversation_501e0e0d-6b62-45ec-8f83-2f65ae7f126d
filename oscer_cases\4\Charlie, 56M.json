{"metadata": {"name": "Charlie, 56M", "scraped_at": "2025-09-03T00:21:08.619900", "script_url": "https://www.oscer.ai/pwf/script/ucyWcbJowVQ3"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice asked to see <PERSON>. He is a 56 year old man presenting with a cough. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>. My pronouns are he/him.", "I'm a 56 year old man and I work as a librarian.", "I've come to the GP today because I have a cough that has not gone away."], "Persona": ["I'm usually a pretty relaxed and happy person.", "My biggest pet peeve is people folding pages in books."], "HOPC": ["I've been experiencing a cough that hasn't been going away.", "I think I've had it for 3 months now.", "It is a dry cough. I think it's gotten worse.", "I've never brought up any phlegm or blood.", "I initially just had a tickling sensation at the back of my throat and then I had the cough.", "Now it feels more like a scratching sensation but both make me want to cough.", "I don't feel short of breath.", "My ankles aren't more swollen than usual.", "I haven't felt nauseated or vomited.", "I don't have a fever or a wheeze.", "I don't know anyone around me who's been sick.", "I haven't lost any weight, felt fatigued or have night sweats.", "I was born in Australia and I've never been exposed to tuberculosis as far as I'm aware.", "I am not immunocompromised."], "PMHx": ["I'm usually pretty healthy, I don't have any underlying medical conditions.", "I don't have any allergies that I'm aware of.", "I've been taking Lisinopril for the past 4 months. I take 5mg daily for my blood pressure.", "I take the occasional course of antibiotics for chest infections I get most winters.", "I haven't had a chest infection in over 6 months.", "I have had all my vaccinations."], "FMHx": ["My father has had high blood pressure since he was 62. He's 78 now and manages it well with tablets.", "My mother is well. I don't have any brothers or sisters.", "I'm not aware of anything running in the family."], "SHx": ["I work as a librarian.", "I've never had any issues with dust from the books and even now am not perturbed by the dust.", "I have never worked with asbestos or any dangerous chemicals or fumes.", "I've been happily married for almost 30 years and live at home with my husband, who I dearly love.", "I have no pets.", "I've never smoked in my life.", "I drink some wine once in a while. I have 1-2 glasses once a month.", "I eat a relatively balanced diet.", "I don't do much exercise, I prefer to read books and spend my time relaxing.", "I'm slightly overweight.", "I've never traveled overseas."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Whooping", "Asking Generally", "Productive Cough"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to Stop Coughing"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Smoking", "Allergens", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Morning or Night-time", "Season or Temperature", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Chest Tightness", "score": 1, "items": ["Chest Tightness"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Immunodeficiency History", "score": 1, "items": ["Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Duration", "Inhalers", "ACE Inhibitor", "Asking Generally", "Antihypertensives"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["TB Tests", "Spirometry", "Allergy Testing"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}