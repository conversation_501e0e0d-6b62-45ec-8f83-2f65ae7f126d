{"metadata": {"name": "Alberto, 55M", "scraped_at": "2025-09-05T12:50:51.663705", "script_url": "https://www.oscer.ai/pwf/script/AiqpewObyEqZ"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based in the Emergency Department. <PERSON> is a 55 year old man (he/him) who has presented complaining of toe pain. Please take a history from <PERSON> to establish a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "My pronouns are he/him.", "I am 55 years old."], "Persona": ["I've migrated from Spain 10 years ago. My English isn't perfect.", "I'm jovial and friendly.", "I like to make my own health decisions."], "HOPC": ["Uh, I've been having this terrible toe pain. Give me some morphine and let me go!", "The toe pain is only on my left big toe.", "The toe pain is only on one side.", "The toe pain feels like it's in my joint.", "This is the most excruciating pain I've felt. I have cried multiple times; I'm a man that doesn't cry.", "The pain feels 1000 needles in my toe. It's a really sharp pain.", "I would rate the pain 200/10. I can't simply bear this.", "The pain came on last night, before I was getting ready to go to bed.", "The pain gradually worsened such that I could not sleep. That's why I have come in at 4am to the emergency department!", "The pain has been constant since it started.", "The pain has been getting worse, that's why I'm here!", "I've never had pain like this.", "I haven't injured any where in my body.", "I'm not sure what has caused this. It came out of nowhere!", "It's winter and I like to sleep with multiple blankets, but even the thinnest sheet brings on the pain.", "I took some aspirin when I first got the pain but believe it or not I think it has made the pain even worse!", "My left big toe looks swollen, red and warm.", "I don't have any fever.", "I don't have any pain in any of my other joints.", "I don't have any visual disturbance, eye discharge or red eyes", "I haven't had any recent bowel changes"], "PMHx": ["I'm pretty healthy. I used to have hypertension but thanks to my great self-care I think it has now been resolved!", "I haven't visited the GP in at least 5 years. I like to make my own treatment and decisions.", "I take 1 tablet of 75mg aspirin tablet in the morning. I've heard it prevents clots.", "I'm not on any diuretics.", "I don't take any supplements.", "I have never had any surgery and have never been hospitalised in the past.", "I don't believe in vaccines. If you are happy, you'll be healthy.", "I don't have any allergies."], "FMHx": ["My parents passed away when I was very young, so I don't know much about their health.", "My older brother is way too focused on diseases and that's why he gets diagnosed with one weekly! Apparently, he has had a heart attack, a stroke, high blood pressure. Oh, and \"The disease of Kings\" as he calls it. It gives him joint pains by his description. Poor lad!"], "SHx": ["I eat a diet rich in meat. I work as a butcher so I make sure I get some protein into every meal including breakfast.", "I drink 2 cans of beer each night. On the weekend though it can definitely go up to half a dozen beers with mates.", "I smoke 2 cigars a day. I make sure they're Cuban. When I don't smoke cigars, I smoke 6 cigarettes a day.", "I live with my son, <PERSON>. He is 24 years old.", "I'm not sexually active."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally", "Clicking, Grinding or Locking"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Ability to Bend or Move", "Ability to do Physical Activity", "Ability to Palpate or Wear Shoes"]}, {"title": "Quantitative", "score": 1, "items": ["Range of Motion", "Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally", "Physical Activity", "Morning or Night-time"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Injuries", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Morning or Night-time"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Skin Heat", "score": 1, "items": ["Skin Heat"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Joint Redness", "score": 1, "items": ["Joint Redness"]}, {"title": "Toe Deformity", "score": 1, "items": ["Tophus"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Painful Urination"]}, {"title": "Morning Stiffness", "score": 1, "items": ["Morning Stiffness"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 2, "items": ["<PERSON><PERSON><PERSON>", "Diuretics", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Fractures", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Septic Arthritis"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Rheumatological Disease History", "score": 1, "items": ["Joint Disease"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Gout", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Meat-Rich Diet", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}]}}}