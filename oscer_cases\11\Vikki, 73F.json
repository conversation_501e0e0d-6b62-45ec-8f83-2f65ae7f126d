{"metadata": {"name": "<PERSON><PERSON><PERSON>, 73F", "scraped_at": "2025-09-05T12:56:16.822248", "script_url": "https://www.oscer.ai/pwf/script/PgxwYPj867lR"}, "tabs": {"doctor_information": {"Intro": "<PERSON><PERSON><PERSON> is a 73-year-old woman presenting to the GP with pain in her hip. You are a final year medical student on your GP rotation, please take a history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON>, I am 73 years old, and my pronouns are she/her.", "I have come to see the GP because I have been experiencing some pain in my hip."], "Persona": ["I am usually a very fit and healthy 73-year-old.", "Life is very relaxing for me. Nothing in my life stresses me out at the moment.", "I am a very easy-going person.", "I weigh 60 kilograms and I am 165 centimetres tall, my BMI is around 22.", "I see the doctor every 6 months for monitoring of my blood pressure."], "HOPC": ["I have been experiencing some pain in my hip.", "I have had pain in my hip for a number of years but it is starting to become increasingly bothersome.", "The pain does not move anywhere else.", "The pain feels deep and sharp.", "The pain is at its worst when I do a lot of walking.", "The pain subsides a little bit when I rest.", "The pain is present every day.", "The pain has been increasing in severity for the last 12 months.", "My hip is also quite stiff.", "The stiffness is worse in the morning and usually lasts about half an hour.", "The pain can be quite bad, up to an 8/10 at its worst.", "I do not have pain in any other joints.", "I have not injured my leg.", "There is no obvious swelling or bruising around my hip.", "The pain is beginning to affect my activities of daily living. I find it hard to walk around the shops.", "I have not been unwell recently.", "I have not travelled recently.", "No one around me has been sick recently.", "My skin has not changed colour.", "I do not have a fever, diarrhoea, a rash or any ulcers in my mouth.", "I have not had any pain in my eyes."], "PMHx": ["I have been told that I have mild hypertension, I manage this with exercise.", "I don’t take any prescription medications or supplements.", "I have tried ibuprofen for my pain which helps a little bit.", "I have never had an operation.", "I don’t have any allergies.", "I am up to date with all my vaccinations."], "FMHx": ["No on in my family has had similar issues.", "There is no significant medical history in my family."], "SHx": ["I am a retired school teacher.", "I am quite fit, I used to try to walk 5 or 6 kilometres a day, though I can’t do that anymore because of this hip pain.", "I am quite health conscious, my diet is very good.", "I live at home with my husband <PERSON>.", "<PERSON> and I have two grown up children. We also have a dog called <PERSON><PERSON>.", "I don't smoke cigarettes, I have never smoked.", "I don’t drink any alcohol.", "I have never tried recreational drugs, I have never injected anything.", "I feel very safe at home.", "I would say my mental health is good."]}, "marking_rubric": {"HOPC": [{"title": "Hip Pain", "score": 13, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Locking", "Clicking", "Grinding", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Other Joints", "Asking Generally."]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Move", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Range of Motion Currently", "Walking Distance Currently", "Walking Distance at Baseline"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Episode Frequency", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Resting", "Movement", "Cold Weather", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Injuries", "score": 1, "items": ["Injuries"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Hand Pain", "score": 1, "items": ["Hand Pain"]}, {"title": "Elbow Pain", "score": 1, "items": ["Elbow Pain"]}, {"title": "Deformities", "score": 1, "items": ["Hand", "Knee", "Finger"]}, {"title": "Finger Pain", "score": 1, "items": ["Finger Pain"]}, {"title": "Hip Redness", "score": 1, "items": ["Hip Redness"]}, {"title": "Hand Redness", "score": 1, "items": ["Hand Redness"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON> Redness", "score": 1, "items": ["<PERSON><PERSON> Redness"]}, {"title": "Hip Stiffness", "score": 1, "items": ["Hip Stiffness"]}, {"title": "Joint Locking", "score": 1, "items": ["Joint Locking"]}, {"title": "Finger <PERSON>", "score": 1, "items": ["Finger <PERSON>"]}, {"title": "Hand Stiffness", "score": 1, "items": ["Hand Stiffness"]}, {"title": "<PERSON><PERSON> Stiffness", "score": 1, "items": ["<PERSON><PERSON> Stiffness"]}, {"title": "Fin<PERSON>", "score": 1, "items": ["Fin<PERSON>"]}, {"title": "Walking Difficulty", "score": 1, "items": ["<PERSON><PERSON><PERSON>ait"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Adherence", "Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally", "Joint Replacements"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally", "Joint Replacements"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally", "Psoriatic Arthritis", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 2, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount", "Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}]}, {"title": "Functional History", "score": 2, "subcategories": [{"title": "Mobility", "score": 1, "items": ["Walking", "Mobility"]}, {"title": "Domestic Activities Of Daily Living", "score": 1, "items": ["Cooking", "Cleaning", "Domestic Activities"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Asking Generally"]}, {"title": "Community Activities Of Daily Living", "score": 1, "items": ["Shopping", "Ability to Drive"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "<PERSON><PERSON><PERSON>", "Confidentiality"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}]}}}