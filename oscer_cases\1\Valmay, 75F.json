{"metadata": {"name": "<PERSON><PERSON><PERSON>, 75F", "scraped_at": "2025-09-02T23:54:48.066697", "script_url": "https://www.oscer.ai/pwf/script/E0BZqbIE95j2"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the Emergency Department. Your next patient is <PERSON><PERSON><PERSON>, a 75 year old woman who has presented with back pain. Take a detailed history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON> and I’m a 75-year-old woman.", "My pronouns are she/her.", "I’ve recently gotten really severe back pain that is stopping me from doing a lot.", "I went to my GP this morning and she immediately recommended that I come to the Emergency Department."], "Persona": ["I’m an adventurous woman and am pretty easy going.", "I only went to the doctor this morning because my symptoms got worse and I wasn't in great shape."], "HOPC": ["I have developed really severe back pain today.", "It feels sharp and hot and it radiates down to my right leg.", "It is in the lower part of my back right above my bottom.", "For the past 3 years, I have had on and off lower back pain which sometimes goes to my right leg.", "It started 2 days ago but within the past day it has gotten a lot worse.", "Heavy lifting and standing worsen my back pain.", "I get a little bit of pain when someone pushes my spine really hard.", "I've had back pain for years but 2 days ago I had a really severe episode of back pain that hasn't gone away; that episode came on in about half an hour.", "I had pins and needles in my right leg before but it feels numb right now.", "Walking is a bit hard for me because my right leg keeps dragging on the floor.", "I feel numb around my bottom.", "I have had urinary incontinence twice this morning.", "It is very difficult for me to initiate my urine.", "I have also been incontinent with my bowels.", "I have felt that I haven’t completely emptied my bowels.", "My right leg really hurts.", "I haven’t had any trauma to my back.", "I haven’t been constipated.", "I don’t have diabetes.", "I’ve also been feeling some pain in my abdomen under my belly button.", "I got the abdominal pain a few hours after losing control of my urine.", "I don’t have a fever, weight loss or night sweats."], "PMHx": ["I have osteoporosis. I was diagnosed about 10 years ago.", "I take cholecalciferol 300 mg daily and I've been having 6 monthly denosumab injections.", "I was in a motor vehicle accident 5 years ago where my 11th disc and my wrist broke.", "I had a broken spine a couple of years ago. I think it was the 11th disc. I have had some plates put in.", "I have also fractured my wrist bone and had to get surgery for it. I think they got a bone graft from my arm.", "I don't have any allergies.", "My vaccinations are up to date. I've had two of my Pfizer covid 19 vaccinations."], "FMHx": ["My mother passed away due to dementia a few years ago at 95.", "My father died very young in the war.", "I don’t have any other medical conditions in my family."], "SHx": ["I live with my wife <PERSON>.", "When I was 10 I used to steal my mum’s cigarettes and smoke. Thankfully stopped soon after after my mum whooped me. Since then no smoking for me!", "I like my port wine on Sundays with my friends. I usually drink 2 or 3 glasses.", "I have never used any recreational drugs.", "I try to have a healthy diet.", "My back pain doesn't allow me to do any exercise.", "I used to be a travel agent for 30 years but retired about 10 years ago.", "I feel well supported."]}, "marking_rubric": {"HOPC": [{"title": "Back Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Spasms", "Electric", "Shooting", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Lower Back", "Upper Back", "Asking Generally", "Diffuse or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Leg", "Knee", "Thigh", "Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Move", "Ability to Sleep", "Asking Generally", "Ability to Bend Over", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "First Noticed", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Ice or Heat", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Injuries", "Movement", "Night-time", "Positional", "Context at Onset", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "Falls", "score": 1, "items": ["Falls"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Back Stiffness", "score": 1, "items": ["Back Stiffness"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "<PERSON><PERSON>ness", "score": 1, "items": ["<PERSON><PERSON>ness"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Urinary Retention", "Urinary Incontinence"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Faecal Incontinence"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Bone Cancer", "Breast Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Previous Hospitalisations"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Injuries", "Fractures", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Asking Generally", "Past Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["Investigations", "Cancer Screening"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Back Surgery", "Spinal Surgery", "Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Back Surgery", "Spinal Surgery", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Fractures", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount", "Heavy Lifting", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}]}}}