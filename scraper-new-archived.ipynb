{"cells": [{"cell_type": "markdown", "id": "bb662325-50e1-4da2-84d4-dc3850e07704", "metadata": {}, "source": ["# Oscer Medical Cases Scraper"]}, {"cell_type": "markdown", "id": "2a1a7877-cb4a-46bf-baf8-a8173098fb32", "metadata": {}, "source": ["## Open signin page from homepage"]}, {"cell_type": "code", "execution_count": 2, "id": "2d4c4941-2a0f-4774-a53b-03a74e90aa1b", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "import time\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "# Start the browser\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "    time.sleep(3)  # wait for page load (better to use WebDriverWait in production)\n", "\n", "    # Find and click the Sign In button\n", "    sign_in_button = driver.find_element(By.LINK_TEXT, \"Sign In\")\n", "    sign_in_button.click()\n", "\n", "    # Wait to see the login page\n", "    time.sleep(5)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "e3835d1b-cf43-4ba5-8dc5-64035563cd39", "metadata": {}, "source": ["## Dump Signin page contents"]}, {"cell_type": "code", "execution_count": null, "id": "cedbf91e-49ad-488f-ab4e-527270feae2f", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "# Start the browser\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # Wait until the new page loads (e.g. check for email input or unique element on sign in page)\n", "    WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.TAG_NAME, \"form\"))\n", "    )\n", "\n", "    # Dump the page source\n", "    page_source = driver.page_source\n", "    print(page_source)\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "b9551902-0c3c-4754-8fa6-873942856803", "metadata": {}, "source": ["### With <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "id": "23291f64-2e7c-48fe-b77e-961184cbd380", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the sign-in page loads (look for a form or iframe)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.TAG_NAME, \"body\"))\n", "    )\n", "\n", "    # 4. Dump the main page source\n", "    print(\"\\n=== MAIN PAGE SOURCE ===\\n\")\n", "    print(driver.page_source)\n", "\n", "    # 5. Check if there are any iframes\n", "    iframes = driver.find_elements(By.TAG_NAME, \"iframe\")\n", "    print(f\"\\nFound {len(iframes)} iframe(s).\\n\")\n", "\n", "    for idx, iframe in enumerate(iframes):\n", "        driver.switch_to.frame(iframe)\n", "        print(f\"\\n=== IFRAME {idx} SOURCE ===\\n\")\n", "        print(driver.page_source[:2000])  # print first 2000 chars (to avoid console flood)\n", "        driver.switch_to.default_content()\n", "\n", "finally:\n", "    driver.quit()\n"]}, {"cell_type": "markdown", "id": "d788fd70-3518-4e45-9cfe-0e4452adec3b", "metadata": {}, "source": ["## Fill out cred fields"]}, {"cell_type": "code", "execution_count": 3, "id": "87e878fb-1ec7-4b35-9706-8802c034f782", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Test email and password have been filled in.\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the email input field is present\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    # 4. Fill in test values (do not click sign in)\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "\n", "    password_input.clear()\n", "    password_input.send_keys(\"TestPassword123!\")\n", "\n", "    print(\"✅ Test email and password have been filled in.\")\n", "\n", "finally:\n", "    # Comment this out if you want to see the filled fields before browser closes\n", "    # driver.quit()\n", "    pass\n"]}, {"attachments": {}, "cell_type": "markdown", "id": "ac4cae1f-2596-454d-b6fa-71ebcd48d5c5", "metadata": {}, "source": ["<img src=\"./screenshots/Screenshot 2025-08-28 102946.png\" alt=\"filling out creds\" width=\"500\"/>"]}, {"cell_type": "markdown", "id": "ec06c29f-7768-46a8-a7ed-0c0768ffd09b", "metadata": {}, "source": ["## Sign in"]}, {"cell_type": "code", "execution_count": 1, "id": "441bc55f-1b50-47c7-9d7f-36aa761eea73", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Attempted to sign in with test credentials.\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. Wait until \"Sign In\" button is clickable and click it\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. Wait until the email input field is present\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    # 4. Fill in test values\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 5. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    print(\"✅ Attempted to sign in with test credentials.\")\n", "\n", "finally:\n", "    # Leave browser open for debugging — close manually when ready\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "abc902bb-ebf0-4277-bfc9-8db44805b658", "metadata": {}, "source": ["## Open Scripts page"]}, {"cell_type": "code", "execution_count": 3, "id": "cb44fb7f-9c25-41ac-8f66-99a477e75dfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Navigated to the Scripts page.\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the site\n", "    driver.get(\"https://www.oscer.ai/\")\n", "\n", "    # 2. <PERSON><PERSON> \"Sign In\"\n", "    sign_in_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.LINK_TEXT, \"Sign In\"))\n", "    )\n", "    sign_in_button.click()\n", "\n", "    # 3. <PERSON>ll in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 4. Submit login\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 5. Wait until dashboard loads (HOME tab visible)\n", "    WebDriverWait(driver, 20).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//a[@data-tut='reactour__tab__home']\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> \"<PERSON><PERSON><PERSON>\" tab\n", "    scripts_tab = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//a[@data-tut='reactour__tab__scripts']\"))\n", "    )\n", "    scripts_tab.click()\n", "\n", "    print(\"✅ Navigated to the Scripts page.\")\n", "\n", "finally:\n", "    # Comment out if you want to inspect browser manually\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "f7bc031d-d584-431c-9dab-78de20835f53", "metadata": {}, "source": ["### Clean up code"]}, {"cell_type": "code", "execution_count": null, "id": "4a6a6225-a048-47c1-8260-457dd8a028a4", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads (like header or list)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "\n", "    print(\"✅ Logged in and navigated directly to Scripts page.\")\n", "\n", "finally:\n", "    # Leave browser open for inspection\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "4ec4cd67-b0a2-4c2d-94fb-d6585098b65d", "metadata": {}, "source": ["## Dump script cards"]}, {"cell_type": "code", "execution_count": 1, "id": "562be4a6-31c2-40bc-8183-e5b004fe8b6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Logged in and navigated directly to Scripts page.\n", "✅ Extracted patient names and saved to patients.xlsx\n"]}], "source": ["from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "import pandas as pd\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads (like header or list)\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "\n", "    print(\"✅ Logged in and navigated directly to Scripts page.\")\n", "\n", "        # 6. Wait for script cards to load\n", "    cards = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "    )\n", "    \n", "    # 7. Extract patient names\n", "    patient_names = []\n", "    for card in cards:\n", "        try:\n", "            name = card.find_element(By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "            patient_names.append(name)\n", "        except:\n", "            continue\n", "    \n", "    # 8. Save to Excel\n", "    df = pd.DataFrame(patient_names, columns=[\"Patient Name\"])\n", "    df.to_excel(\"patients.xlsx\", index=False)\n", "    \n", "    print(\"✅ Extracted patient names and saved to patients.xlsx\")\n", "\n", "finally:\n", "    # Leave browser open for inspection\n", "    # driver.quit()\n", "    pass\n"]}, {"cell_type": "markdown", "id": "fecef6e8-4d84-44c1-a19a-1bbbd7250028", "metadata": {}, "source": ["## Visit all pages"]}, {"cell_type": "code", "execution_count": 2, "id": "1504633d-3162-4a63-b852-9cab7962291e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Logged in and navigated to Scripts page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 15 patients from current page.\n", "✅ Scraped 11 patients from current page.\n"]}, {"ename": "PermissionError", "evalue": "[<PERSON><PERSON><PERSON> 13] Permission denied: 'patients.xlsx'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>ermissionE<PERSON>r\u001b[39m                           <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 81\u001b[39m\n\u001b[32m     79\u001b[39m     \u001b[38;5;66;03m# 9. Save all patients to Excel\u001b[39;00m\n\u001b[32m     80\u001b[39m     df = pd.DataFrame(patient_names, columns=[\u001b[33m\"\u001b[39m\u001b[33mPatient Name\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m---> \u001b[39m\u001b[32m81\u001b[39m     \u001b[43mdf\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_excel\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mpatients.xlsx\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     83\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m✅ Extracted all patient names across pages and saved to patients.xlsx\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     85\u001b[39m \u001b[38;5;28;01mfinally\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\util\\_decorators.py:333\u001b[39m, in \u001b[36mdeprecate_nonkeyword_arguments.<locals>.decorate.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    327\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(args) > num_allow_args:\n\u001b[32m    328\u001b[39m     warnings.warn(\n\u001b[32m    329\u001b[39m         msg.format(arguments=_format_argument_list(allow_args)),\n\u001b[32m    330\u001b[39m         \u001b[38;5;167;01mFutureWarning\u001b[39;00m,\n\u001b[32m    331\u001b[39m         stacklevel=find_stack_level(),\n\u001b[32m    332\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m333\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\core\\generic.py:2417\u001b[39m, in \u001b[36mNDFrame.to_excel\u001b[39m\u001b[34m(self, excel_writer, sheet_name, na_rep, float_format, columns, header, index, index_label, startrow, startcol, engine, merge_cells, inf_rep, freeze_panes, storage_options, engine_kwargs)\u001b[39m\n\u001b[32m   2404\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mformats\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mexcel\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ExcelFormatter\n\u001b[32m   2406\u001b[39m formatter = ExcelFormatter(\n\u001b[32m   2407\u001b[39m     df,\n\u001b[32m   2408\u001b[39m     na_rep=na_rep,\n\u001b[32m   (...)\u001b[39m\u001b[32m   2415\u001b[39m     inf_rep=inf_rep,\n\u001b[32m   2416\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m2417\u001b[39m \u001b[43mformatter\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   2418\u001b[39m \u001b[43m    \u001b[49m\u001b[43mexcel_writer\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2419\u001b[39m \u001b[43m    \u001b[49m\u001b[43msheet_name\u001b[49m\u001b[43m=\u001b[49m\u001b[43msheet_name\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2420\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstartrow\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstartrow\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2421\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstartcol\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstartcol\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2422\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfreeze_panes\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfreeze_panes\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2423\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2424\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2425\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   2426\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\io\\formats\\excel.py:943\u001b[39m, in \u001b[36mExcelFormatter.write\u001b[39m\u001b[34m(self, writer, sheet_name, startrow, startcol, freeze_panes, engine, storage_options, engine_kwargs)\u001b[39m\n\u001b[32m    941\u001b[39m     need_save = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    942\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m943\u001b[39m     writer = \u001b[43mExcelWriter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    944\u001b[39m \u001b[43m        \u001b[49m\u001b[43mwriter\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    945\u001b[39m \u001b[43m        \u001b[49m\u001b[43mengine\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    946\u001b[39m \u001b[43m        \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    947\u001b[39m \u001b[43m        \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    948\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    949\u001b[39m     need_save = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    951\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py:61\u001b[39m, in \u001b[36mOpenpyxlWriter.__init__\u001b[39m\u001b[34m(self, path, engine, date_format, datetime_format, mode, storage_options, if_sheet_exists, engine_kwargs, **kwargs)\u001b[39m\n\u001b[32m     57\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mopenpyxl\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mworkbook\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m Workbook\n\u001b[32m     59\u001b[39m engine_kwargs = combine_kwargs(engine_kwargs, kwargs)\n\u001b[32m---> \u001b[39m\u001b[32m61\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m     62\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     63\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     64\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     65\u001b[39m \u001b[43m    \u001b[49m\u001b[43mif_sheet_exists\u001b[49m\u001b[43m=\u001b[49m\u001b[43mif_sheet_exists\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     66\u001b[39m \u001b[43m    \u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mengine_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m     67\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     69\u001b[39m \u001b[38;5;66;03m# ExcelWriter replaced \"a\" by \"r+\" to allow us to first read the excel file from\u001b[39;00m\n\u001b[32m     70\u001b[39m \u001b[38;5;66;03m# the file and later write to it\u001b[39;00m\n\u001b[32m     71\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mr+\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._mode:  \u001b[38;5;66;03m# Load from existing workbook\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\io\\excel\\_base.py:1246\u001b[39m, in \u001b[36mExcelWriter.__init__\u001b[39m\u001b[34m(self, path, engine, date_format, datetime_format, mode, storage_options, if_sheet_exists, engine_kwargs)\u001b[39m\n\u001b[32m   1242\u001b[39m \u001b[38;5;28mself\u001b[39m._handles = IOHandles(\n\u001b[32m   1243\u001b[39m     cast(IO[\u001b[38;5;28mbytes\u001b[39m], path), compression={\u001b[33m\"\u001b[39m\u001b[33mcompression\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28;01mNone\u001b[39;00m}\n\u001b[32m   1244\u001b[39m )\n\u001b[32m   1245\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(path, ExcelWriter):\n\u001b[32m-> \u001b[39m\u001b[32m1246\u001b[39m     \u001b[38;5;28mself\u001b[39m._handles = \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1247\u001b[39m \u001b[43m        \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\n\u001b[32m   1248\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1249\u001b[39m \u001b[38;5;28mself\u001b[39m._cur_sheet = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1251\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m date_format \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~\\miniconda3\\envs\\311\\Lib\\site-packages\\pandas\\io\\common.py:882\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    873\u001b[39m         handle = \u001b[38;5;28mopen\u001b[39m(\n\u001b[32m    874\u001b[39m             handle,\n\u001b[32m    875\u001b[39m             ioargs.mode,\n\u001b[32m   (...)\u001b[39m\u001b[32m    878\u001b[39m             newline=\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    879\u001b[39m         )\n\u001b[32m    880\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    881\u001b[39m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m882\u001b[39m         handle = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    883\u001b[39m     handles.append(handle)\n\u001b[32m    885\u001b[39m \u001b[38;5;66;03m# Convert BytesIO or file objects passed with an encoding\u001b[39;00m\n", "\u001b[31mPermissionError\u001b[39m: [Err<PERSON> 13] Permission denied: 'patients.xlsx'"]}], "source": ["import time\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until something from Scripts page loads\n", "    WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"//div[contains(text(), 'SCRIPTS')]\"))\n", "    )\n", "    print(\"✅ Logged in and navigated to Scripts page.\")\n", "\n", "    patient_names = []\n", "\n", "    while True:\n", "        # 6. Wait for script cards to load\n", "        cards = WebDriverWait(driver, 10).until(\n", "            EC.presence_of_all_elements_located((By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "        )\n", "\n", "        # 7. Extract patient names on this page\n", "        for card in cards:\n", "            try:\n", "                name = card.find_element(By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "                patient_names.append(name)\n", "            except:\n", "                continue\n", "\n", "        print(f\"✅ Scraped {len(cards)} patients from current page.\")\n", "\n", "        # 8. Try to go to next page\n", "        try:\n", "            next_button = driver.find_element(By.XPATH, \"//button[@aria-label='Go to next page']\")\n", "            if \"disabled\" in next_button.get_attribute(\"class\"):\n", "                break  # no more pages\n", "            next_button.click()\n", "            time.sleep(2)  # wait for new page to load\n", "        except:\n", "            break  # no next button found → stop\n", "\n", "    # 9. Save all patients to Excel\n", "    df = pd.DataFrame(patient_names, columns=[\"Patient Name\"])\n", "    df.to_excel(\"patients.xlsx\", index=False)\n", "\n", "    print(\"✅ Extracted all patient names across pages and saved to patients.xlsx\")\n", "\n", "finally:\n", "    pass  # keep browser open for inspection\n"]}, {"cell_type": "markdown", "id": "9034d33f-56b8-499d-94bf-5fb76e497822", "metadata": {}, "source": ["## Dump script case contents"]}, {"cell_type": "markdown", "id": "029fc5a3-af4e-43df-a4f6-824da073c103", "metadata": {}, "source": ["### navigate tabs"]}, {"cell_type": "code", "execution_count": 3, "id": "a3b20dfe-115d-478a-8ca2-aec2d735d425", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "(No content found)\n", "\n", "===== tab-Script =====\n", "(No content found)\n", "\n", "===== tab-Marking Rubric =====\n", "(No content found)\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab and dump content\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        # Grab the visible content panel\n", "        try:\n", "            content = driver.find_element(\n", "                By.XPATH, \"//div[contains(@class,'react-tabs__tab-panel') and not(contains(@style,'display: none'))]\"\n", "            ).text\n", "        except:\n", "            content = \"(No content found)\"\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "        print(content)\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "markdown", "id": "d0fd8be1-de28-4885-90db-b695eaf152d0", "metadata": {}, "source": ["### dump doctors information"]}, {"cell_type": "code", "execution_count": 1, "id": "642ce869-459a-4885-aeb2-881a264532d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Marking Rubric (raw) =====\n", " \n", "\n", "===== Marking Rubric (structured) =====\n", "\n", "General:\n", "\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "        \n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "        \n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "        \n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "        \n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "        \n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "        \n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip() for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "        \n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "                \n", "        elif \"Marking Rubric\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Marking Rubric (raw) =====\\n\", raw_text)\n", "        \n", "            # Optional: split by headings like \"History\", \"Examination\", \"Diagnosis\" if present\n", "            lines = raw_text.split(\"\\n\")\n", "            sections = {}\n", "            current = \"General\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line.endswith(\":\"):  # headings usually end with colon\n", "                    current = line.strip(\":\")\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "6e3e4317-ec1b-42c3-8809-7a73996990bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Marking Rubric (raw) =====\n", " \n", "\n", "===== Marking Rubric (structured) =====\n", "\n", "General:\n", "\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "        \n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "        \n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "        \n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "        \n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "        \n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "        \n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip() for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "        \n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "                \n", "        elif \"Marking Rubric\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Marking Rubric (raw) =====\\n\", raw_text)\n", "        \n", "            # Optional: split by headings like \"History\", \"Examination\", \"Diagnosis\" if present\n", "            lines = raw_text.split(\"\\n\")\n", "            sections = {}\n", "            current = \"General\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line.endswith(\":\"):  # headings usually end with colon\n", "                    current = line.strip(\":\")\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "954a8a17-6b98-4975-b0b0-fd30996cc3b1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Marking Rubric (structured) =====\n", "DEBUG: found 13 accordions at depth=0\n", "DEBUG: header[0] text='Shortness Of Breath\n", "0\n", "/16'\n", "DEBUG: found 27 checkboxes under 'Shortness Of Breath\n", "0\n", "/16'\n", "  DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[1] 'Ability to Sleep' checked=False\n", "  DEBUG: checkbox[2] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[3] 'Affecting Daily Life' checked=False\n", "  DEBUG: checkbox[4] 'Scale out of 10' checked=False\n", "  DEBUG: checkbox[5] 'Exercise Capacity' checked=False\n", "  DEBUG: checkbox[6] 'Onset' checked=False\n", "  DEBUG: checkbox[7] 'Offset' checked=False\n", "  DEBUG: checkbox[8] 'Duration' checked=False\n", "  DEBUG: checkbox[9] 'Change Over Time' checked=False\n", "  DEBUG: checkbox[10] 'Onset of Worsening' checked=False\n", "  DEBUG: checkbox[11] 'Frequency of Episodes' checked=False\n", "  DEBUG: checkbox[12] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[13] 'Resting' checked=False\n", "  DEBUG: checkbox[14] 'Positional' checked=False\n", "  DEBUG: checkbox[15] 'Medications' checked=False\n", "  DEBUG: checkbox[16] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[17] 'Ideas' checked=False\n", "  DEBUG: checkbox[18] 'Allergies' checked=False\n", "  DEBUG: checkbox[19] 'Positional' checked=False\n", "  DEBUG: checkbox[20] 'Context at Onset' checked=False\n", "  DEBUG: checkbox[21] 'Lifestyle Changes' checked=False\n", "  DEBUG: checkbox[22] 'Physical Activity' checked=False\n", "  DEBUG: checkbox[23] 'Infectious Contacts' checked=False\n", "  DEBUG: checkbox[24] 'Waking up Short of Breath' checked=False\n", "  DEBUG: checkbox[25] 'Shortness of Breath when Laying Down' checked=False\n", "  DEBUG: checkbox[26] 'Asking Generally about Aggravating Factors' checked=False\n", "  DEBUG: found 12 accordions at depth=1\n", "  DEBUG: header[0] text=''\n", "  DEBUG: found 27 checkboxes under ''\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[1] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[2] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[3] 'Affecting Daily Life' checked=False\n", "    DEBUG: checkbox[4] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[5] 'Exercise Capacity' checked=False\n", "    DEBUG: checkbox[6] 'Onset' checked=False\n", "    DEBUG: checkbox[7] 'Offset' checked=False\n", "    DEBUG: checkbox[8] 'Duration' checked=False\n", "    DEBUG: checkbox[9] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[10] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[11] 'Frequency of Episodes' checked=False\n", "    DEBUG: checkbox[12] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[13] 'Resting' checked=False\n", "    DEBUG: checkbox[14] 'Positional' checked=False\n", "    DEBUG: checkbox[15] 'Medications' checked=False\n", "    DEBUG: checkbox[16] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[17] 'Ideas' checked=False\n", "    DEBUG: checkbox[18] 'Allergies' checked=False\n", "    DEBUG: checkbox[19] 'Positional' checked=False\n", "    DEBUG: checkbox[20] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[21] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[22] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[23] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[24] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[25] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[26] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 11 accordions at depth=2\n", "    DEBUG: header[0] text='Quality\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Quality\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 1 accordions at depth=3\n", "      DEBUG: header[0] text='Character\n", "0\n", "/1'\n", "      DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "        DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "    DEBUG: header[1] text='Character\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[2] text='Severity\n", "0\n", "/3'\n", "    DEBUG: found 5 checkboxes under 'Severity\n", "0\n", "/3'\n", "      DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "      DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "      DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "      DEBUG: checkbox[3] 'Scale out of 10' checked=False\n", "      DEBUG: checkbox[4] 'Exercise Capacity' checked=False\n", "      DEBUG: found 2 accordions at depth=3\n", "      DEBUG: header[0] text='Qualitative\n", "0\n", "/2'\n", "      DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "        DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "        DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "        DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "      DEBUG: header[1] text='Quantitative\n", "0\n", "/1'\n", "      DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "        DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "        DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "    DEBUG: header[3] text='Qualitative\n", "0\n", "/2'\n", "    DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "      DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "      DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[4] text='Quantitative\n", "0\n", "/1'\n", "    DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "      DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[5] text='Time Course\n", "0\n", "/5'\n", "    DEBUG: found 7 checkboxes under 'Time Course\n", "0\n", "/5'\n", "      DEBUG: checkbox[0] 'Onset' checked=False\n", "      DEBUG: checkbox[1] 'Offset' checked=False\n", "      DEBUG: checkbox[2] 'Duration' checked=False\n", "      DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "      DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "      DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "      DEBUG: checkbox[6] 'Asking Generally' checked=False\n", "      DEBUG: found 2 accordions at depth=3\n", "      DEBUG: header[0] text='Time Course\n", "0\n", "/4'\n", "      DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "        DEBUG: checkbox[0] 'Onset' checked=False\n", "        DEBUG: checkbox[1] 'Offset' checked=False\n", "        DEBUG: checkbox[2] 'Duration' checked=False\n", "        DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "        DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "        DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "      DEBUG: header[1] text='Previous Episodes\n", "0\n", "/1'\n", "      DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "        DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "    DEBUG: header[6] text='Time Course\n", "0\n", "/4'\n", "    DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "      DEBUG: checkbox[0] 'Onset' checked=False\n", "      DEBUG: checkbox[1] 'Offset' checked=False\n", "      DEBUG: checkbox[2] 'Duration' checked=False\n", "      DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "      DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "      DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[7] text='Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[8] text='Contributing Factors\n", "0\n", "/7'\n", "    DEBUG: found 14 checkboxes under 'Contributing Factors\n", "0\n", "/7'\n", "      DEBUG: checkbox[0] 'Resting' checked=False\n", "      DEBUG: checkbox[1] 'Positional' checked=False\n", "      DEBUG: checkbox[2] 'Medications' checked=False\n", "      DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "      DEBUG: checkbox[4] 'Ideas' checked=False\n", "      DEBUG: checkbox[5] 'Allergies' checked=False\n", "      DEBUG: checkbox[6] 'Positional' checked=False\n", "      DEBUG: checkbox[7] 'Context at Onset' checked=False\n", "      DEBUG: checkbox[8] 'Lifestyle Changes' checked=False\n", "      DEBUG: checkbox[9] 'Physical Activity' checked=False\n", "      DEBUG: checkbox[10] 'Infectious Contacts' checked=False\n", "      DEBUG: checkbox[11] 'Waking up Short of Breath' checked=False\n", "      DEBUG: checkbox[12] 'Shortness of Breath when Laying Down' checked=False\n", "      DEBUG: checkbox[13] 'Asking Generally about Aggravating Factors' checked=False\n", "      DEBUG: found 2 accordions at depth=3\n", "      DEBUG: header[0] text='Relieving Factors\n", "0\n", "/2'\n", "      DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "        DEBUG: checkbox[0] 'Resting' checked=False\n", "        DEBUG: checkbox[1] 'Positional' checked=False\n", "        DEBUG: checkbox[2] 'Medications' checked=False\n", "        DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "      DEBUG: header[1] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "      DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "        DEBUG: checkbox[0] 'Ideas' checked=False\n", "        DEBUG: checkbox[1] 'Allergies' checked=False\n", "        DEBUG: checkbox[2] 'Positional' checked=False\n", "        DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "        DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "        DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "        DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "        DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "        DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "        DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "        DEBUG: found 0 accordions at depth=4\n", "    DEBUG: header[9] text='Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Resting' checked=False\n", "      DEBUG: checkbox[1] 'Positional' checked=False\n", "      DEBUG: checkbox[2] 'Medications' checked=False\n", "      DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[10] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "      DEBUG: checkbox[0] 'Ideas' checked=False\n", "      DEBUG: checkbox[1] 'Allergies' checked=False\n", "      DEBUG: checkbox[2] 'Positional' checked=False\n", "      DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "      DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "      DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "      DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "      DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "      DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "      DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[1] text='Quality\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Quality\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 1 accordions at depth=2\n", "    DEBUG: header[0] text='Character\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[2] text='Character\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[3] text='Severity\n", "0\n", "/3'\n", "  DEBUG: found 5 checkboxes under 'Severity\n", "0\n", "/3'\n", "    DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "    DEBUG: checkbox[3] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[4] 'Exercise Capacity' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Qualitative\n", "0\n", "/2'\n", "    DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "      DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "      DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Quantitative\n", "0\n", "/1'\n", "    DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "      DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[4] text='Qualitative\n", "0\n", "/2'\n", "  DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[5] text='Quantitative\n", "0\n", "/1'\n", "  DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[6] text='Time Course\n", "0\n", "/5'\n", "  DEBUG: found 7 checkboxes under 'Time Course\n", "0\n", "/5'\n", "    DEBUG: checkbox[0] 'Onset' checked=False\n", "    DEBUG: checkbox[1] 'Offset' checked=False\n", "    DEBUG: checkbox[2] 'Duration' checked=False\n", "    DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "    DEBUG: checkbox[6] 'Asking Generally' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Time Course\n", "0\n", "/4'\n", "    DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "      DEBUG: checkbox[0] 'Onset' checked=False\n", "      DEBUG: checkbox[1] 'Offset' checked=False\n", "      DEBUG: checkbox[2] 'Duration' checked=False\n", "      DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "      DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "      DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[7] text='Time Course\n", "0\n", "/4'\n", "  DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "    DEBUG: checkbox[0] 'Onset' checked=False\n", "    DEBUG: checkbox[1] 'Offset' checked=False\n", "    DEBUG: checkbox[2] 'Duration' checked=False\n", "    DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[8] text='Previous Episodes\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[9] text='Contributing Factors\n", "0\n", "/7'\n", "  DEBUG: found 14 checkboxes under 'Contributing Factors\n", "0\n", "/7'\n", "    DEBUG: checkbox[0] 'Resting' checked=False\n", "    DEBUG: checkbox[1] 'Positional' checked=False\n", "    DEBUG: checkbox[2] 'Medications' checked=False\n", "    DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[4] 'Ideas' checked=False\n", "    DEBUG: checkbox[5] 'Allergies' checked=False\n", "    DEBUG: checkbox[6] 'Positional' checked=False\n", "    DEBUG: checkbox[7] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[8] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[9] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[10] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[11] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[12] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[13] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Resting' checked=False\n", "      DEBUG: checkbox[1] 'Positional' checked=False\n", "      DEBUG: checkbox[2] 'Medications' checked=False\n", "      DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "      DEBUG: checkbox[0] 'Ideas' checked=False\n", "      DEBUG: checkbox[1] 'Allergies' checked=False\n", "      DEBUG: checkbox[2] 'Positional' checked=False\n", "      DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "      DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "      DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "      DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "      DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "      DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "      DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[10] text='Relieving Factors\n", "0\n", "/2'\n", "  DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Resting' checked=False\n", "    DEBUG: checkbox[1] 'Positional' checked=False\n", "    DEBUG: checkbox[2] 'Medications' checked=False\n", "    DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[11] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "  DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: checkbox[0] 'Ideas' checked=False\n", "    DEBUG: checkbox[1] 'Allergies' checked=False\n", "    DEBUG: checkbox[2] 'Positional' checked=False\n", "    DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[1] text=''\n", "DEBUG: found 27 checkboxes under ''\n", "  DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[1] 'Ability to Sleep' checked=False\n", "  DEBUG: checkbox[2] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[3] 'Affecting Daily Life' checked=False\n", "  DEBUG: checkbox[4] 'Scale out of 10' checked=False\n", "  DEBUG: checkbox[5] 'Exercise Capacity' checked=False\n", "  DEBUG: checkbox[6] 'Onset' checked=False\n", "  DEBUG: checkbox[7] 'Offset' checked=False\n", "  DEBUG: checkbox[8] 'Duration' checked=False\n", "  DEBUG: checkbox[9] 'Change Over Time' checked=False\n", "  DEBUG: checkbox[10] 'Onset of Worsening' checked=False\n", "  DEBUG: checkbox[11] 'Frequency of Episodes' checked=False\n", "  DEBUG: checkbox[12] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[13] 'Resting' checked=False\n", "  DEBUG: checkbox[14] 'Positional' checked=False\n", "  DEBUG: checkbox[15] 'Medications' checked=False\n", "  DEBUG: checkbox[16] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[17] 'Ideas' checked=False\n", "  DEBUG: checkbox[18] 'Allergies' checked=False\n", "  DEBUG: checkbox[19] 'Positional' checked=False\n", "  DEBUG: checkbox[20] 'Context at Onset' checked=False\n", "  DEBUG: checkbox[21] 'Lifestyle Changes' checked=False\n", "  DEBUG: checkbox[22] 'Physical Activity' checked=False\n", "  DEBUG: checkbox[23] 'Infectious Contacts' checked=False\n", "  DEBUG: checkbox[24] 'Waking up Short of Breath' checked=False\n", "  DEBUG: checkbox[25] 'Shortness of Breath when Laying Down' checked=False\n", "  DEBUG: checkbox[26] 'Asking Generally about Aggravating Factors' checked=False\n", "  DEBUG: found 11 accordions at depth=1\n", "  DEBUG: header[0] text='Quality\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Quality\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 1 accordions at depth=2\n", "    DEBUG: header[0] text='Character\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[1] text='Character\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[2] text='Severity\n", "0\n", "/3'\n", "  DEBUG: found 5 checkboxes under 'Severity\n", "0\n", "/3'\n", "    DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "    DEBUG: checkbox[3] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[4] 'Exercise Capacity' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Qualitative\n", "0\n", "/2'\n", "    DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "      DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "      DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Quantitative\n", "0\n", "/1'\n", "    DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "      DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[3] text='Qualitative\n", "0\n", "/2'\n", "  DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[4] text='Quantitative\n", "0\n", "/1'\n", "  DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[5] text='Time Course\n", "0\n", "/5'\n", "  DEBUG: found 7 checkboxes under 'Time Course\n", "0\n", "/5'\n", "    DEBUG: checkbox[0] 'Onset' checked=False\n", "    DEBUG: checkbox[1] 'Offset' checked=False\n", "    DEBUG: checkbox[2] 'Duration' checked=False\n", "    DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "    DEBUG: checkbox[6] 'Asking Generally' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Time Course\n", "0\n", "/4'\n", "    DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "      DEBUG: checkbox[0] 'Onset' checked=False\n", "      DEBUG: checkbox[1] 'Offset' checked=False\n", "      DEBUG: checkbox[2] 'Duration' checked=False\n", "      DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "      DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "      DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "      DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[6] text='Time Course\n", "0\n", "/4'\n", "  DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "    DEBUG: checkbox[0] 'Onset' checked=False\n", "    DEBUG: checkbox[1] 'Offset' checked=False\n", "    DEBUG: checkbox[2] 'Duration' checked=False\n", "    DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[7] text='Previous Episodes\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[8] text='Contributing Factors\n", "0\n", "/7'\n", "  DEBUG: found 14 checkboxes under 'Contributing Factors\n", "0\n", "/7'\n", "    DEBUG: checkbox[0] 'Resting' checked=False\n", "    DEBUG: checkbox[1] 'Positional' checked=False\n", "    DEBUG: checkbox[2] 'Medications' checked=False\n", "    DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[4] 'Ideas' checked=False\n", "    DEBUG: checkbox[5] 'Allergies' checked=False\n", "    DEBUG: checkbox[6] 'Positional' checked=False\n", "    DEBUG: checkbox[7] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[8] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[9] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[10] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[11] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[12] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[13] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 2 accordions at depth=2\n", "    DEBUG: header[0] text='Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "      DEBUG: checkbox[0] 'Resting' checked=False\n", "      DEBUG: checkbox[1] 'Positional' checked=False\n", "      DEBUG: checkbox[2] 'Medications' checked=False\n", "      DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "    DEBUG: header[1] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "      DEBUG: checkbox[0] 'Ideas' checked=False\n", "      DEBUG: checkbox[1] 'Allergies' checked=False\n", "      DEBUG: checkbox[2] 'Positional' checked=False\n", "      DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "      DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "      DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "      DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "      DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "      DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "      DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "      DEBUG: found 0 accordions at depth=3\n", "  DEBUG: header[9] text='Relieving Factors\n", "0\n", "/2'\n", "  DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Resting' checked=False\n", "    DEBUG: checkbox[1] 'Positional' checked=False\n", "    DEBUG: checkbox[2] 'Medications' checked=False\n", "    DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[10] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "  DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: checkbox[0] 'Ideas' checked=False\n", "    DEBUG: checkbox[1] 'Allergies' checked=False\n", "    DEBUG: checkbox[2] 'Positional' checked=False\n", "    DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[2] text='Quality\n", "0\n", "/1'\n", "DEBUG: found 1 checkboxes under 'Quality\n", "0\n", "/1'\n", "  DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "  DEBUG: found 1 accordions at depth=1\n", "  DEBUG: header[0] text='Character\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[3] text='Character\n", "0\n", "/1'\n", "DEBUG: found 1 checkboxes under 'Character\n", "0\n", "/1'\n", "  DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[4] text='Severity\n", "0\n", "/3'\n", "DEBUG: found 5 checkboxes under 'Severity\n", "0\n", "/3'\n", "  DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "  DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "  DEBUG: checkbox[3] 'Scale out of 10' checked=False\n", "  DEBUG: checkbox[4] 'Exercise Capacity' checked=False\n", "  DEBUG: found 2 accordions at depth=1\n", "  DEBUG: header[0] text='Qualitative\n", "0\n", "/2'\n", "  DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "    DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "    DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[1] text='Quantitative\n", "0\n", "/1'\n", "  DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "    DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[5] text='Qualitative\n", "0\n", "/2'\n", "DEBUG: found 3 checkboxes under 'Qualitative\n", "0\n", "/2'\n", "  DEBUG: checkbox[0] 'Ability to Sleep' checked=False\n", "  DEBUG: checkbox[1] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[2] 'Affecting Daily Life' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[6] text='Quantitative\n", "0\n", "/1'\n", "DEBUG: found 2 checkboxes under 'Quantitative\n", "0\n", "/1'\n", "  DEBUG: checkbox[0] 'Scale out of 10' checked=False\n", "  DEBUG: checkbox[1] 'Exercise Capacity' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[7] text='Time Course\n", "0\n", "/5'\n", "DEBUG: found 7 checkboxes under 'Time Course\n", "0\n", "/5'\n", "  DEBUG: checkbox[0] 'Onset' checked=False\n", "  DEBUG: checkbox[1] 'Offset' checked=False\n", "  DEBUG: checkbox[2] 'Duration' checked=False\n", "  DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "  DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "  DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "  DEBUG: checkbox[6] 'Asking Generally' checked=False\n", "  DEBUG: found 2 accordions at depth=1\n", "  DEBUG: header[0] text='Time Course\n", "0\n", "/4'\n", "  DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "    DEBUG: checkbox[0] 'Onset' checked=False\n", "    DEBUG: checkbox[1] 'Offset' checked=False\n", "    DEBUG: checkbox[2] 'Duration' checked=False\n", "    DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "    DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "    DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[1] text='Previous Episodes\n", "0\n", "/1'\n", "  DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "    DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[8] text='Time Course\n", "0\n", "/4'\n", "DEBUG: found 6 checkboxes under 'Time Course\n", "0\n", "/4'\n", "  DEBUG: checkbox[0] 'Onset' checked=False\n", "  DEBUG: checkbox[1] 'Offset' checked=False\n", "  DEBUG: checkbox[2] 'Duration' checked=False\n", "  DEBUG: checkbox[3] 'Change Over Time' checked=False\n", "  DEBUG: checkbox[4] 'Onset of Worsening' checked=False\n", "  DEBUG: checkbox[5] 'Frequency of Episodes' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[9] text='Previous Episodes\n", "0\n", "/1'\n", "DEBUG: found 1 checkboxes under 'Previous Episodes\n", "0\n", "/1'\n", "  DEBUG: checkbox[0] 'Asking Generally' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[10] text='Contributing Factors\n", "0\n", "/7'\n", "DEBUG: found 14 checkboxes under 'Contributing Factors\n", "0\n", "/7'\n", "  DEBUG: checkbox[0] 'Resting' checked=False\n", "  DEBUG: checkbox[1] 'Positional' checked=False\n", "  DEBUG: checkbox[2] 'Medications' checked=False\n", "  DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "  DEBUG: checkbox[4] 'Ideas' checked=False\n", "  DEBUG: checkbox[5] 'Allergies' checked=False\n", "  DEBUG: checkbox[6] 'Positional' checked=False\n", "  DEBUG: checkbox[7] 'Context at Onset' checked=False\n", "  DEBUG: checkbox[8] 'Lifestyle Changes' checked=False\n", "  DEBUG: checkbox[9] 'Physical Activity' checked=False\n", "  DEBUG: checkbox[10] 'Infectious Contacts' checked=False\n", "  DEBUG: checkbox[11] 'Waking up Short of Breath' checked=False\n", "  DEBUG: checkbox[12] 'Shortness of Breath when Laying Down' checked=False\n", "  DEBUG: checkbox[13] 'Asking Generally about Aggravating Factors' checked=False\n", "  DEBUG: found 2 accordions at depth=1\n", "  DEBUG: header[0] text='Relieving Factors\n", "0\n", "/2'\n", "  DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "    DEBUG: checkbox[0] 'Resting' checked=False\n", "    DEBUG: checkbox[1] 'Positional' checked=False\n", "    DEBUG: checkbox[2] 'Medications' checked=False\n", "    DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "  DEBUG: header[1] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "  DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "    DEBUG: checkbox[0] 'Ideas' checked=False\n", "    DEBUG: checkbox[1] 'Allergies' checked=False\n", "    DEBUG: checkbox[2] 'Positional' checked=False\n", "    DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "    DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "    DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "    DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "    DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "    DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "    DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "    DEBUG: found 0 accordions at depth=2\n", "DEBUG: header[11] text='Relieving Factors\n", "0\n", "/2'\n", "DEBUG: found 4 checkboxes under 'Relieving Factors\n", "0\n", "/2'\n", "  DEBUG: checkbox[0] 'Resting' checked=False\n", "  DEBUG: checkbox[1] 'Positional' checked=False\n", "  DEBUG: checkbox[2] 'Medications' checked=False\n", "  DEBUG: checkbox[3] 'Asking Generally' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "DEBUG: header[12] text='Context and Aggravating Factors\n", "0\n", "/5'\n", "DEBUG: found 10 checkboxes under 'Context and Aggravating Factors\n", "0\n", "/5'\n", "  DEBUG: checkbox[0] 'Ideas' checked=False\n", "  DEBUG: checkbox[1] 'Allergies' checked=False\n", "  DEBUG: checkbox[2] 'Positional' checked=False\n", "  DEBUG: checkbox[3] 'Context at Onset' checked=False\n", "  DEBUG: checkbox[4] 'Lifestyle Changes' checked=False\n", "  DEBUG: checkbox[5] 'Physical Activity' checked=False\n", "  DEBUG: checkbox[6] 'Infectious Contacts' checked=False\n", "  DEBUG: checkbox[7] 'Waking up Short of Breath' checked=False\n", "  DEBUG: checkbox[8] 'Shortness of Breath when Laying Down' checked=False\n", "  DEBUG: checkbox[9] 'Asking Generally about Aggravating Factors' checked=False\n", "  DEBUG: found 0 accordions at depth=1\n", "Shortness Of Breath\n", "0\n", "/16 (0\n", "/16)\n", "  ✗ Asking Generally\n", "  ✗ Ability to Sleep\n", "  ✗ Asking Generally\n", "  ✗ Affecting Daily Life\n", "  ✗ Scale out of 10\n", "  ✗ Exercise Capacity\n", "  ✗ Onset\n", "  ✗ Offset\n", "  ✗ Duration\n", "  ✗ Change Over Time\n", "  ✗ Onset of Worsening\n", "  ✗ Frequency of Episodes\n", "  ✗ Asking Generally\n", "  ✗ Resting\n", "  ✗ Positional\n", "  ✗ Medications\n", "  ✗ Asking Generally\n", "  ✗ Ideas\n", "  ✗ Allergies\n", "  ✗ Positional\n", "  ✗ Context at Onset\n", "  ✗ Lifestyle Changes\n", "  ✗ Physical Activity\n", "  ✗ Infectious Contacts\n", "  ✗ Waking up Short of Breath\n", "  ✗ Shortness of Breath when Laying Down\n", "  ✗ Asking Generally about Aggravating Factors\n", "   ()\n", "    ✗ Asking Generally\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "    ✗ Asking Generally\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", "    Quality\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "      Character\n", "0\n", "/1 (0\n", "/1)\n", "        ✗ Asking Generally\n", "    Character\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "    Severity\n", "0\n", "/3 (0\n", "/3)\n", "      ✗ Ability to Sleep\n", "      ✗ Asking Generally\n", "      ✗ Affecting Daily Life\n", "      ✗ Scale out of 10\n", "      ✗ Exercise Capacity\n", "      Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "        ✗ Ability to Sleep\n", "        ✗ Asking Generally\n", "        ✗ Affecting Daily Life\n", "      Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "        ✗ Scale out of 10\n", "        ✗ Exercise Capacity\n", "    Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Ability to Sleep\n", "      ✗ Asking Generally\n", "      ✗ Affecting Daily Life\n", "    Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Scale out of 10\n", "      ✗ Exercise Capacity\n", "    Time Course\n", "0\n", "/5 (0\n", "/5)\n", "      ✗ Onset\n", "      ✗ Offset\n", "      ✗ Duration\n", "      ✗ Change Over Time\n", "      ✗ Onset of Worsening\n", "      ✗ Frequency of Episodes\n", "      ✗ Asking Generally\n", "      Time Course\n", "0\n", "/4 (0\n", "/4)\n", "        ✗ Onset\n", "        ✗ Offset\n", "        ✗ Duration\n", "        ✗ Change Over Time\n", "        ✗ Onset of Worsening\n", "        ✗ Frequency of Episodes\n", "      Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "        ✗ Asking Generally\n", "    Time Course\n", "0\n", "/4 (0\n", "/4)\n", "      ✗ Onset\n", "      ✗ Offset\n", "      ✗ Duration\n", "      ✗ Change Over Time\n", "      ✗ Onset of Worsening\n", "      ✗ Frequency of Episodes\n", "    Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "    Contributing Factors\n", "0\n", "/7 (0\n", "/7)\n", "      ✗ Resting\n", "      ✗ Positional\n", "      ✗ Medications\n", "      ✗ Asking Generally\n", "      ✗ Ideas\n", "      ✗ Allergies\n", "      ✗ Positional\n", "      ✗ Context at Onset\n", "      ✗ Lifestyle Changes\n", "      ✗ Physical Activity\n", "      ✗ Infectious Contacts\n", "      ✗ Waking up Short of Breath\n", "      ✗ Shortness of Breath when Laying Down\n", "      ✗ Asking Generally about Aggravating Factors\n", "      Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "        ✗ Resting\n", "        ✗ Positional\n", "        ✗ Medications\n", "        ✗ Asking Generally\n", "      Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "        ✗ Ideas\n", "        ✗ Allergies\n", "        ✗ Positional\n", "        ✗ Context at Onset\n", "        ✗ Lifestyle Changes\n", "        ✗ Physical Activity\n", "        ✗ Infectious Contacts\n", "        ✗ Waking up Short of Breath\n", "        ✗ Shortness of Breath when Laying Down\n", "        ✗ Asking Generally about Aggravating Factors\n", "    Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Resting\n", "      ✗ Positional\n", "      ✗ Medications\n", "      ✗ Asking Generally\n", "    Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "      ✗ Ideas\n", "      ✗ Allergies\n", "      ✗ Positional\n", "      ✗ Context at Onset\n", "      ✗ Lifestyle Changes\n", "      ✗ Physical Activity\n", "      ✗ Infectious Contacts\n", "      ✗ Waking up Short of Breath\n", "      ✗ Shortness of Breath when Laying Down\n", "      ✗ Asking Generally about Aggravating Factors\n", "  Quality\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "    Character\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "  Character\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "  Severity\n", "0\n", "/3 (0\n", "/3)\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "    Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Ability to Sleep\n", "      ✗ Asking Generally\n", "      ✗ Affecting Daily Life\n", "    Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Scale out of 10\n", "      ✗ Exercise Capacity\n", "  Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "  Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "  Time Course\n", "0\n", "/5 (0\n", "/5)\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "    ✗ Asking Generally\n", "    Time Course\n", "0\n", "/4 (0\n", "/4)\n", "      ✗ Onset\n", "      ✗ Offset\n", "      ✗ Duration\n", "      ✗ Change Over Time\n", "      ✗ Onset of Worsening\n", "      ✗ Frequency of Episodes\n", "    Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "  Time Course\n", "0\n", "/4 (0\n", "/4)\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "  Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "  Contributing Factors\n", "0\n", "/7 (0\n", "/7)\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", "    Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Resting\n", "      ✗ Positional\n", "      ✗ Medications\n", "      ✗ Asking Generally\n", "    Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "      ✗ Ideas\n", "      ✗ Allergies\n", "      ✗ Positional\n", "      ✗ Context at Onset\n", "      ✗ Lifestyle Changes\n", "      ✗ Physical Activity\n", "      ✗ Infectious Contacts\n", "      ✗ Waking up Short of Breath\n", "      ✗ Shortness of Breath when Laying Down\n", "      ✗ Asking Generally about Aggravating Factors\n", "  Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "  Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", " ()\n", "  ✗ Asking Generally\n", "  ✗ Ability to Sleep\n", "  ✗ Asking Generally\n", "  ✗ Affecting Daily Life\n", "  ✗ Scale out of 10\n", "  ✗ Exercise Capacity\n", "  ✗ Onset\n", "  ✗ Offset\n", "  ✗ Duration\n", "  ✗ Change Over Time\n", "  ✗ Onset of Worsening\n", "  ✗ Frequency of Episodes\n", "  ✗ Asking Generally\n", "  ✗ Resting\n", "  ✗ Positional\n", "  ✗ Medications\n", "  ✗ Asking Generally\n", "  ✗ Ideas\n", "  ✗ Allergies\n", "  ✗ Positional\n", "  ✗ Context at Onset\n", "  ✗ Lifestyle Changes\n", "  ✗ Physical Activity\n", "  ✗ Infectious Contacts\n", "  ✗ Waking up Short of Breath\n", "  ✗ Shortness of Breath when Laying Down\n", "  ✗ Asking Generally about Aggravating Factors\n", "  Quality\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "    Character\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "  Character\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "  Severity\n", "0\n", "/3 (0\n", "/3)\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "    Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Ability to Sleep\n", "      ✗ Asking Generally\n", "      ✗ Affecting Daily Life\n", "    Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Scale out of 10\n", "      ✗ Exercise Capacity\n", "  Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "  Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "  Time Course\n", "0\n", "/5 (0\n", "/5)\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "    ✗ Asking Generally\n", "    Time Course\n", "0\n", "/4 (0\n", "/4)\n", "      ✗ Onset\n", "      ✗ Offset\n", "      ✗ Duration\n", "      ✗ Change Over Time\n", "      ✗ Onset of Worsening\n", "      ✗ Frequency of Episodes\n", "    Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "      ✗ Asking Generally\n", "  Time Course\n", "0\n", "/4 (0\n", "/4)\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "  Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "  Contributing Factors\n", "0\n", "/7 (0\n", "/7)\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", "    Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "      ✗ Resting\n", "      ✗ Positional\n", "      ✗ Medications\n", "      ✗ Asking Generally\n", "    Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "      ✗ Ideas\n", "      ✗ Allergies\n", "      ✗ Positional\n", "      ✗ Context at Onset\n", "      ✗ Lifestyle Changes\n", "      ✗ Physical Activity\n", "      ✗ Infectious Contacts\n", "      ✗ Waking up Short of Breath\n", "      ✗ Shortness of Breath when Laying Down\n", "      ✗ Asking Generally about Aggravating Factors\n", "  Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "  Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", "Quality\n", "0\n", "/1 (0\n", "/1)\n", "  ✗ Asking Generally\n", "  Character\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "Character\n", "0\n", "/1 (0\n", "/1)\n", "  ✗ Asking Generally\n", "Severity\n", "0\n", "/3 (0\n", "/3)\n", "  ✗ Ability to Sleep\n", "  ✗ Asking Generally\n", "  ✗ Affecting Daily Life\n", "  ✗ Scale out of 10\n", "  ✗ Exercise Capacity\n", "  Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Ability to Sleep\n", "    ✗ Asking Generally\n", "    ✗ Affecting Daily Life\n", "  Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Scale out of 10\n", "    ✗ Exercise Capacity\n", "Qualitative\n", "0\n", "/2 (0\n", "/2)\n", "  ✗ Ability to Sleep\n", "  ✗ Asking Generally\n", "  ✗ Affecting Daily Life\n", "Quantitative\n", "0\n", "/1 (0\n", "/1)\n", "  ✗ Scale out of 10\n", "  ✗ Exercise Capacity\n", "Time Course\n", "0\n", "/5 (0\n", "/5)\n", "  ✗ Onset\n", "  ✗ Offset\n", "  ✗ Duration\n", "  ✗ Change Over Time\n", "  ✗ Onset of Worsening\n", "  ✗ Frequency of Episodes\n", "  ✗ Asking Generally\n", "  Time Course\n", "0\n", "/4 (0\n", "/4)\n", "    ✗ Onset\n", "    ✗ Offset\n", "    ✗ Duration\n", "    ✗ Change Over Time\n", "    ✗ Onset of Worsening\n", "    ✗ Frequency of Episodes\n", "  Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "    ✗ Asking Generally\n", "Time Course\n", "0\n", "/4 (0\n", "/4)\n", "  ✗ Onset\n", "  ✗ Offset\n", "  ✗ Duration\n", "  ✗ Change Over Time\n", "  ✗ Onset of Worsening\n", "  ✗ Frequency of Episodes\n", "Previous Episodes\n", "0\n", "/1 (0\n", "/1)\n", "  ✗ Asking Generally\n", "Contributing Factors\n", "0\n", "/7 (0\n", "/7)\n", "  ✗ Resting\n", "  ✗ Positional\n", "  ✗ Medications\n", "  ✗ Asking Generally\n", "  ✗ Ideas\n", "  ✗ Allergies\n", "  ✗ Positional\n", "  ✗ Context at Onset\n", "  ✗ Lifestyle Changes\n", "  ✗ Physical Activity\n", "  ✗ Infectious Contacts\n", "  ✗ Waking up Short of Breath\n", "  ✗ Shortness of Breath when Laying Down\n", "  ✗ Asking Generally about Aggravating Factors\n", "  Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "    ✗ Resting\n", "    ✗ Positional\n", "    ✗ Medications\n", "    ✗ Asking Generally\n", "  Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "    ✗ Ideas\n", "    ✗ Allergies\n", "    ✗ Positional\n", "    ✗ Context at Onset\n", "    ✗ Lifestyle Changes\n", "    ✗ Physical Activity\n", "    ✗ Infectious Contacts\n", "    ✗ Waking up Short of Breath\n", "    ✗ Shortness of Breath when Laying Down\n", "    ✗ Asking Generally about Aggravating Factors\n", "Relieving Factors\n", "0\n", "/2 (0\n", "/2)\n", "  ✗ Resting\n", "  ✗ Positional\n", "  ✗ Medications\n", "  ✗ Asking Generally\n", "Context and Aggravating Factors\n", "0\n", "/5 (0\n", "/5)\n", "  ✗ Ideas\n", "  ✗ Allergies\n", "  ✗ Positional\n", "  ✗ Context at Onset\n", "  ✗ Lifestyle Changes\n", "  ✗ Physical Activity\n", "  ✗ Infectious Contacts\n", "  ✗ Waking up Short of Breath\n", "  ✗ Shortness of Breath when Laying Down\n", "  ✗ Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable((By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located((By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        tab_name = tab.get_attribute(\"tour-id\")   # e.g. \"tab-Doctor Information\"\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "        \n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "        \n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "        \n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "        \n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "        \n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located((By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "        \n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip() for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "        \n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "                \n", "        elif \"Marking Rubric\" in tab_name:\n", "        \n", "\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging"]}, {"cell_type": "code", "execution_count": 1, "id": "90bbe289-c2bc-43a2-9eaa-b23a96840eff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "{'children': [{'children': [{'children': [{'children': ['Asking Generally'],\n", "                                           'title': 'Character\\n'\n", "                                                    '0\\n'\n", "                                                    '/1 Character 0\\n'\n", "                                                    '/1 0\\n'\n", "                                                    '/1 0 /1'}],\n", "                             'title': 'Quality\\n'\n", "                                      '0\\n'\n", "                                      '/1 Quality\\n'\n", "                                      '0\\n'\n", "                                      '/1 Quality 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1 Character\\n'\n", "                                      '0\\n'\n", "                                      '/1 Character 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': ['Asking Generally'],\n", "                             'title': 'Character\\n'\n", "                                      '0\\n'\n", "                                      '/1 Character 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': [{'children': ['Ability to Sleep',\n", "                                                        'Asking Generally',\n", "                                                        'Affecting Daily Life'],\n", "                                           'title': 'Qualitative\\n'\n", "                                                    '0\\n'\n", "                                                    '/2 Qualitative 0\\n'\n", "                                                    '/2 0\\n'\n", "                                                    '/2 0 /2'},\n", "                                          {'children': ['Scale out of 10',\n", "                                                        'Exercise Capacity'],\n", "                                           'title': 'Quantitative\\n'\n", "                                                    '0\\n'\n", "                                                    '/1 Quantitative 0\\n'\n", "                                                    '/1 0\\n'\n", "                                                    '/1 0 /1'}],\n", "                             'title': 'Severity\\n'\n", "                                      '0\\n'\n", "                                      '/3 Severity\\n'\n", "                                      '0\\n'\n", "                                      '/3 Severity 0\\n'\n", "                                      '/3 0\\n'\n", "                                      '/3 0 /3 Qualitative\\n'\n", "                                      '0\\n'\n", "                                      '/2 Qualitative 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2 Quantitative\\n'\n", "                                      '0\\n'\n", "                                      '/1 Quantitative 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': ['Ability to Sleep',\n", "                                          'Asking Generally',\n", "                                          'Affecting Daily Life'],\n", "                             'title': 'Qualitative\\n'\n", "                                      '0\\n'\n", "                                      '/2 Qualitative 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2'},\n", "                            {'children': ['Scale out of 10',\n", "                                          'Exercise Capacity'],\n", "                             'title': 'Quantitative\\n'\n", "                                      '0\\n'\n", "                                      '/1 Quantitative 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': [{'children': ['Onset',\n", "                                                        'Offset',\n", "                                                        'Duration',\n", "                                                        'Change Over Time',\n", "                                                        'Onset of Worsening',\n", "                                                        'Frequency of '\n", "                                                        'Episodes'],\n", "                                           'title': 'Time Course\\n'\n", "                                                    '0\\n'\n", "                                                    '/4 Time Course 0\\n'\n", "                                                    '/4 0\\n'\n", "                                                    '/4 0 /4'},\n", "                                          {'children': ['Asking Generally'],\n", "                                           'title': 'Previous Episodes\\n'\n", "                                                    '0\\n'\n", "                                                    '/1 Previous Episodes 0\\n'\n", "                                                    '/1 0\\n'\n", "                                                    '/1 0 /1'}],\n", "                             'title': 'Time Course\\n'\n", "                                      '0\\n'\n", "                                      '/5 Time Course\\n'\n", "                                      '0\\n'\n", "                                      '/5 Time Course 0\\n'\n", "                                      '/5 0\\n'\n", "                                      '/5 0 /5 Time Course\\n'\n", "                                      '0\\n'\n", "                                      '/4 Time Course 0\\n'\n", "                                      '/4 0\\n'\n", "                                      '/4 0 /4 Previous Episodes\\n'\n", "                                      '0\\n'\n", "                                      '/1 Previous Episodes 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': ['Onset',\n", "                                          'Offset',\n", "                                          'Duration',\n", "                                          'Change Over Time',\n", "                                          'Onset of Worsening',\n", "                                          'Frequency of Episodes'],\n", "                             'title': 'Time Course\\n'\n", "                                      '0\\n'\n", "                                      '/4 Time Course 0\\n'\n", "                                      '/4 0\\n'\n", "                                      '/4 0 /4'},\n", "                            {'children': ['Asking Generally'],\n", "                             'title': 'Previous Episodes\\n'\n", "                                      '0\\n'\n", "                                      '/1 Previous Episodes 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'},\n", "                            {'children': [{'children': ['Resting',\n", "                                                        'Positional',\n", "                                                        'Medications',\n", "                                                        'Asking Generally'],\n", "                                           'title': 'Relieving Factors\\n'\n", "                                                    '0\\n'\n", "                                                    '/2 Relieving Factors 0\\n'\n", "                                                    '/2 0\\n'\n", "                                                    '/2 0 /2'},\n", "                                          {'children': ['Ideas',\n", "                                                        'Allergies',\n", "                                                        'Positional',\n", "                                                        'Context at Onset',\n", "                                                        'Lifestyle Changes',\n", "                                                        'Physical Activity',\n", "                                                        'Infectious Contacts',\n", "                                                        'Waking up Short of '\n", "                                                        'Breath',\n", "                                                        'Shortness of Breath '\n", "                                                        'when Laying Down',\n", "                                                        'Asking Generally '\n", "                                                        'about Aggravating '\n", "                                                        'Factors'],\n", "                                           'title': 'Context and Aggravating '\n", "                                                    'Factors\\n'\n", "                                                    '0\\n'\n", "                                                    '/5 Context and '\n", "                                                    'Aggravating Factors 0\\n'\n", "                                                    '/5 0\\n'\n", "                                                    '/5 0 /5'}],\n", "                             'title': 'Contributing Factors\\n'\n", "                                      '0\\n'\n", "                                      '/7 Contributing Factors\\n'\n", "                                      '0\\n'\n", "                                      '/7 Contributing Factors 0\\n'\n", "                                      '/7 0\\n'\n", "                                      '/7 0 /7 Relieving Factors\\n'\n", "                                      '0\\n'\n", "                                      '/2 Relieving Factors 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2 Context and Aggravating '\n", "                                      'Factors\\n'\n", "                                      '0\\n'\n", "                                      '/5 Context and Aggravating Factors 0\\n'\n", "                                      '/5 0\\n'\n", "                                      '/5 0 /5'},\n", "                            {'children': ['Resting',\n", "                                          'Positional',\n", "                                          'Medications',\n", "                                          'Asking Generally'],\n", "                             'title': 'Relieving Factors\\n'\n", "                                      '0\\n'\n", "                                      '/2 Relieving Factors 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2'},\n", "                            {'children': ['Ideas',\n", "                                          'Allergies',\n", "                                          'Positional',\n", "                                          'Context at Onset',\n", "                                          'Lifestyle Changes',\n", "                                          'Physical Activity',\n", "                                          'Infectious Contacts',\n", "                                          'Waking up Short of Breath',\n", "                                          'Shortness of Breath when Laying '\n", "                                          'Down',\n", "                                          'Asking Generally about Aggravating '\n", "                                          'Factors'],\n", "                             'title': 'Context and Aggravating Factors\\n'\n", "                                      '0\\n'\n", "                                      '/5 Context and Aggravating Factors 0\\n'\n", "                                      '/5 0\\n'\n", "                                      '/5 0 /5'}],\n", "               'title': 'Quality\\n'\n", "                        '0\\n'\n", "                        '/1 Quality\\n'\n", "                        '0\\n'\n", "                        '/1 Quality 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1 Character\\n'\n", "                        '0\\n'\n", "                        '/1 Character 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1 Severity\\n'\n", "                        '0\\n'\n", "                        '/3 Severity\\n'\n", "                        '0\\n'\n", "                        '/3 Severity 0\\n'\n", "                        '/3 0\\n'\n", "                        '/3 0 /3 Qualitative\\n'\n", "                        '0\\n'\n", "                        '/2 Qualitative 0\\n'\n", "                        '/2 0\\n'\n", "                        '/2 0 /2 Quantitative\\n'\n", "                        '0\\n'\n", "                        '/1 Quantitative 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1 Time Course\\n'\n", "                        '0\\n'\n", "                        '/5 Time Course\\n'\n", "                        '0\\n'\n", "                        '/5 Time Course 0\\n'\n", "                        '/5 0\\n'\n", "                        '/5 0 /5 Time Course\\n'\n", "                        '0\\n'\n", "                        '/4 Time Course 0\\n'\n", "                        '/4 0\\n'\n", "                        '/4 0 /4 Previous Episodes\\n'\n", "                        '0\\n'\n", "                        '/1 Previous Episodes 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1 Contributing Factors\\n'\n", "                        '0\\n'\n", "                        '/7 Contributing Factors\\n'\n", "                        '0\\n'\n", "                        '/7 Contributing Factors 0\\n'\n", "                        '/7 0\\n'\n", "                        '/7 0 /7 Relieving Factors\\n'\n", "                        '0\\n'\n", "                        '/2 Relieving Factors 0\\n'\n", "                        '/2 0\\n'\n", "                        '/2 0 /2 Context and Aggravating Factors\\n'\n", "                        '0\\n'\n", "                        '/5 Context and Aggravating Factors 0\\n'\n", "                        '/5 0\\n'\n", "                        '/5 0 /5'},\n", "              {'children': [{'children': ['Asking Generally'],\n", "                             'title': 'Character\\n'\n", "                                      '0\\n'\n", "                                      '/1 Character 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'}],\n", "               'title': 'Quality\\n'\n", "                        '0\\n'\n", "                        '/1 Quality\\n'\n", "                        '0\\n'\n", "                        '/1 Quality 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1 Character\\n'\n", "                        '0\\n'\n", "                        '/1 Character 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1'},\n", "              {'children': ['Asking Generally'],\n", "               'title': 'Character\\n0\\n/1 Character 0\\n/1 0\\n/1 0 /1'},\n", "              {'children': [{'children': ['Ability to Sleep',\n", "                                          'Asking Generally',\n", "                                          'Affecting Daily Life'],\n", "                             'title': 'Qualitative\\n'\n", "                                      '0\\n'\n", "                                      '/2 Qualitative 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2'},\n", "                            {'children': ['Scale out of 10',\n", "                                          'Exercise Capacity'],\n", "                             'title': 'Quantitative\\n'\n", "                                      '0\\n'\n", "                                      '/1 Quantitative 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'}],\n", "               'title': 'Severity\\n'\n", "                        '0\\n'\n", "                        '/3 Severity\\n'\n", "                        '0\\n'\n", "                        '/3 Severity 0\\n'\n", "                        '/3 0\\n'\n", "                        '/3 0 /3 Qualitative\\n'\n", "                        '0\\n'\n", "                        '/2 Qualitative 0\\n'\n", "                        '/2 0\\n'\n", "                        '/2 0 /2 Quantitative\\n'\n", "                        '0\\n'\n", "                        '/1 Quantitative 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1'},\n", "              {'children': ['Ability to Sleep',\n", "                            'Asking Generally',\n", "                            'Affecting Daily Life'],\n", "               'title': 'Qualitative\\n0\\n/2 Qualitative 0\\n/2 0\\n/2 0 /2'},\n", "              {'children': ['Scale out of 10', 'Exercise Capacity'],\n", "               'title': 'Quantitative\\n0\\n/1 Quantitative 0\\n/1 0\\n/1 0 /1'},\n", "              {'children': [{'children': ['Onset',\n", "                                          'Offset',\n", "                                          'Duration',\n", "                                          'Change Over Time',\n", "                                          'Onset of Worsening',\n", "                                          'Frequency of Episodes'],\n", "                             'title': 'Time Course\\n'\n", "                                      '0\\n'\n", "                                      '/4 Time Course 0\\n'\n", "                                      '/4 0\\n'\n", "                                      '/4 0 /4'},\n", "                            {'children': ['Asking Generally'],\n", "                             'title': 'Previous Episodes\\n'\n", "                                      '0\\n'\n", "                                      '/1 Previous Episodes 0\\n'\n", "                                      '/1 0\\n'\n", "                                      '/1 0 /1'}],\n", "               'title': 'Time Course\\n'\n", "                        '0\\n'\n", "                        '/5 Time Course\\n'\n", "                        '0\\n'\n", "                        '/5 Time Course 0\\n'\n", "                        '/5 0\\n'\n", "                        '/5 0 /5 Time Course\\n'\n", "                        '0\\n'\n", "                        '/4 Time Course 0\\n'\n", "                        '/4 0\\n'\n", "                        '/4 0 /4 Previous Episodes\\n'\n", "                        '0\\n'\n", "                        '/1 Previous Episodes 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1'},\n", "              {'children': ['Onset',\n", "                            'Offset',\n", "                            'Duration',\n", "                            'Change Over Time',\n", "                            'Onset of Worsening',\n", "                            'Frequency of Episodes'],\n", "               'title': 'Time Course\\n0\\n/4 Time Course 0\\n/4 0\\n/4 0 /4'},\n", "              {'children': ['Asking Generally'],\n", "               'title': 'Previous Episodes\\n'\n", "                        '0\\n'\n", "                        '/1 Previous Episodes 0\\n'\n", "                        '/1 0\\n'\n", "                        '/1 0 /1'},\n", "              {'children': [{'children': ['Resting',\n", "                                          'Positional',\n", "                                          'Medications',\n", "                                          'Asking Generally'],\n", "                             'title': 'Relieving Factors\\n'\n", "                                      '0\\n'\n", "                                      '/2 Relieving Factors 0\\n'\n", "                                      '/2 0\\n'\n", "                                      '/2 0 /2'},\n", "                            {'children': ['Ideas',\n", "                                          'Allergies',\n", "                                          'Positional',\n", "                                          'Context at Onset',\n", "                                          'Lifestyle Changes',\n", "                                          'Physical Activity',\n", "                                          'Infectious Contacts',\n", "                                          'Waking up Short of Breath',\n", "                                          'Shortness of Breath when Laying '\n", "                                          'Down',\n", "                                          'Asking Generally about Aggravating '\n", "                                          'Factors'],\n", "                             'title': 'Context and Aggravating Factors\\n'\n", "                                      '0\\n'\n", "                                      '/5 Context and Aggravating Factors 0\\n'\n", "                                      '/5 0\\n'\n", "                                      '/5 0 /5'}],\n", "               'title': 'Contributing Factors\\n'\n", "                        '0\\n'\n", "                        '/7 Contributing Factors\\n'\n", "                        '0\\n'\n", "                        '/7 Contributing Factors 0\\n'\n", "                        '/7 0\\n'\n", "                        '/7 0 /7 Relieving Factors\\n'\n", "                        '0\\n'\n", "                        '/2 Relieving Factors 0\\n'\n", "                        '/2 0\\n'\n", "                        '/2 0 /2 Context and Aggravating Factors\\n'\n", "                        '0\\n'\n", "                        '/5 Context and Aggravating Factors 0\\n'\n", "                        '/5 0\\n'\n", "                        '/5 0 /5'},\n", "              {'children': ['Resting',\n", "                            'Positional',\n", "                            'Medications',\n", "                            'Asking Generally'],\n", "               'title': 'Relieving Factors\\n'\n", "                        '0\\n'\n", "                        '/2 Relieving Factors 0\\n'\n", "                        '/2 0\\n'\n", "                        '/2 0 /2'},\n", "              {'children': ['Ideas',\n", "                            'Allergies',\n", "                            'Positional',\n", "                            'Context at Onset',\n", "                            'Lifestyle Changes',\n", "                            'Physical Activity',\n", "                            'Infectious Contacts',\n", "                            'Waking up Short of Breath',\n", "                            'Shortness of Breath when Laying Down',\n", "                            'Asking Generally about Aggravating Factors'],\n", "               'title': 'Context and Aggravating Factors\\n'\n", "                        '0\\n'\n", "                        '/5 Context and Aggravating Factors 0\\n'\n", "                        '/5 0\\n'\n", "                        '/5 0 /5'}],\n", " 'title': 'Shortness Of Breath\\n'\n", "          '0\\n'\n", "          '/16 Shortness Of Breath\\n'\n", "          '0\\n'\n", "          '/16 Shortness Of Breath 0\\n'\n", "          '/16 0\\n'\n", "          '/16 0 /16 Quality\\n'\n", "          '0\\n'\n", "          '/1 Quality\\n'\n", "          '0\\n'\n", "          '/1 Quality 0\\n'\n", "          '/1 0\\n'\n", "          '/1 0 /1 Character\\n'\n", "          '0\\n'\n", "          '/1 Character 0\\n'\n", "          '/1 0\\n'\n", "          '/1 0 /1 Severity\\n'\n", "          '0\\n'\n", "          '/3 Severity\\n'\n", "          '0\\n'\n", "          '/3 Severity 0\\n'\n", "          '/3 0\\n'\n", "          '/3 0 /3 Qualitative\\n'\n", "          '0\\n'\n", "          '/2 Qualitative 0\\n'\n", "          '/2 0\\n'\n", "          '/2 0 /2 Quantitative\\n'\n", "          '0\\n'\n", "          '/1 Quantitative 0\\n'\n", "          '/1 0\\n'\n", "          '/1 0 /1 Time Course\\n'\n", "          '0\\n'\n", "          '/5 Time Course\\n'\n", "          '0\\n'\n", "          '/5 Time Course 0\\n'\n", "          '/5 0\\n'\n", "          '/5 0 /5 Time Course\\n'\n", "          '0\\n'\n", "          '/4 Time Course 0\\n'\n", "          '/4 0\\n'\n", "          '/4 0 /4 Previous Episodes\\n'\n", "          '0\\n'\n", "          '/1 Previous Episodes 0\\n'\n", "          '/1 0\\n'\n", "          '/1 0 /1 Contributing Factors\\n'\n", "          '0\\n'\n", "          '/7 Contributing Factors\\n'\n", "          '0\\n'\n", "          '/7 Contributing Factors 0\\n'\n", "          '/7 0\\n'\n", "          '/7 0 /7 Relieving Factors\\n'\n", "          '0\\n'\n", "          '/2 Relieving Factors 0\\n'\n", "          '/2 0\\n'\n", "          '/2 0 /2 Context and Aggravating Factors\\n'\n", "          '0\\n'\n", "          '/5 Context and Aggravating Factors 0\\n'\n", "          '/5 0\\n'\n", "          '/5 0 /5'}\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            def parse_section(section_element):\n", "                \"\"\"Recursively parse an accordion section.\"\"\"\n", "                result = {}\n", "            \n", "                # Heading text (title + score if present)\n", "                title_parts = section_element.find_elements(By.XPATH, \".//div[@role='button']//div\")\n", "                heading_text = \" \".join([t.text.strip() for t in title_parts if t.text.strip()])\n", "                result[\"title\"] = heading_text\n", "                result[\"children\"] = []\n", "            \n", "                # Region (expanded body of this accordion)\n", "                try:\n", "                    body_el = section_element.find_element(By.XPATH, \".//div[@role='region']\")\n", "                except:\n", "                    return result  # no body → just return\n", "            \n", "                # Check for nested accordions\n", "                sub_accordions = body_el.find_elements(By.XPATH, \".//div[contains(@class,'MuiAccordion-root')]\")\n", "            \n", "                if sub_accordions:\n", "                    # Recurse into each sub-accordion (but only direct children, not self)\n", "                    for sub in sub_accordions:\n", "                        result[\"children\"].append(parse_section(sub))\n", "                else:\n", "                    # No sub-accordions → this is a leaf with checkboxes\n", "                    checkboxes = body_el.find_elements(By.XPATH, \".//label//span[contains(@class,'MuiTypography-body1')]\")\n", "                    result[\"children\"] = [c.text.strip() for c in checkboxes]\n", "            \n", "                return result\n", "\n", "\n", "\n", "            # === Inside your loop when \"Marking Rubric\" tab is active ===\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "\n", "            # Find the \"Shortness Of Breath\" top-level accordion\n", "            sob_section = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH, \".//div[@role='button']//div[contains(text(),'Shortness Of Breath')]/ancestor::div[contains(@class,'MuiAccordion-root')]\"\n", "                ))\n", "            )\n", "\n", "            # Parse recursively\n", "            hopc_data = parse_section(sob_section)\n", "\n", "            import pprint\n", "            pprint.pprint(hopc_data)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "dcba5aca-69e1-4e4c-a9c3-6f839f9b6c45", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Marking Rubric (HOPC only) =====\n", "null\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            from bs4 import BeautifulSoup\n", "            import json\n", "        \n", "            # wait for the panel\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            html = active_panel.get_attribute(\"innerHTML\")\n", "            soup = BeautifulSoup(html, \"html.parser\")\n", "        \n", "            def parse_accordion(acc_node):\n", "                \"\"\"Recursively parse one accordion into dict.\"\"\"\n", "                result = {}\n", "        \n", "                # title (first text inside summary)\n", "                header = acc_node.select_one(\".MuiAccordionSummary-root .MuiAccordionSummary-content div\")\n", "                if not header:\n", "                    return None\n", "                result[\"title\"] = header.get_text(strip=True)\n", "        \n", "                # score (look for numbers inside the summary)\n", "                summary = acc_node.select_one(\".MuiAccordionSummary-root .MuiAccordionSummary-content\")\n", "                print(summary)\n", "                if summary:\n", "                    nums = [d.get_text(strip=True) for d in summary.find_all(\"div\") if \"/\" in d.get_text()]\n", "                    if nums:\n", "                        result[\"score\"] = nums[0]\n", "        \n", "                result[\"children\"] = []\n", "        \n", "                # children accordions\n", "                for child_acc in acc_node.select(\"> .MuiCollapse-root .MuiAccordion-root\"):\n", "                    child_parsed = parse_accordion(child_acc)\n", "                    if child_parsed:\n", "                        result[\"children\"].append(child_parsed)\n", "        \n", "                # leaf checkboxes\n", "                for cb in acc_node.select(\"> .MuiCollapse-root input[type=checkbox][name]\"):\n", "                    result[\"children\"].append({\n", "                        \"title\": cb[\"name\"],\n", "                        \"type\": \"checkbox\"\n", "                    })\n", "        \n", "                return result\n", "        \n", "            # find top-level \"Shortness Of Breath\" accordion\n", "            sob_acc = soup.find(\"div\", string=\"Shortness Of Breath\")\n", "            if sob_acc:\n", "                sob_root = sob_acc.find_parent(\"div\", class_=\"MuiAccordion-root\")\n", "                parsed = parse_accordion(sob_root)\n", "            else:\n", "                parsed = None\n", "        \n", "            print(\"\\n===== Marking Rubric (HOPC only) =====\")\n", "            print(json.dumps(parsed, indent=2))\n", "\n", "\n", "\n", "            \n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "962d05ef-b977-485e-8cca-e88c7ae88da5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Mark<PERSON> (Raw) =====\n", "[{'title': 'Shortness Of Breath', 'score': {'current': 0, 'total': 16}, 'items': ['Asking Generally', 'Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity', 'Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally', 'Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Quality', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally'], 'subcategories': [{'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}, {'title': 'Severity', 'score': {'current': 0, 'total': 3}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity'], 'subcategories': [{'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}, {'title': 'Time Course', 'score': {'current': 0, 'total': 5}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally'], 'subcategories': [{'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}, {'title': 'Contributing Factors', 'score': {'current': 0, 'total': 7}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}, {'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]\n", "\n", "===== Mark<PERSON> <PERSON><PERSON><PERSON> (Cleaned) =====\n", "[{'title': 'Shortness Of Breath', 'score': {'current': 0, 'total': 16}, 'subcategories': [{'title': 'Quality', 'score': {'current': 0, 'total': 1}, 'subcategories': [{'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Severity', 'score': {'current': 0, 'total': 3}, 'subcategories': [{'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Time Course', 'score': {'current': 0, 'total': 5}, 'subcategories': [{'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Contributing Factors', 'score': {'current': 0, 'total': 7}, 'subcategories': [{'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]}]\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "                        # Get the full text content from the summary\n", "                        summary_text = summary.text.strip()\n", "\n", "                        # Split by lines and clean up\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # Parse the content - look for title and score patterns\n", "                        title = \"\"\n", "                        score = None\n", "\n", "                        # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "                        full_text = ' '.join(lines)\n", "\n", "                        # Try to find score pattern in the full text\n", "                        score_match = re.search(\n", "                            r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "                        if score_match:\n", "                            score = {\n", "                                'current': int(score_match.group(1)),\n", "                                'total': int(score_match.group(2))\n", "                            }\n", "                            # Remove the score part to get the title\n", "                            title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                                           '', full_text).strip()\n", "                        else:\n", "                            # No score found, the whole text is the title\n", "                            title = full_text\n", "\n", "                        # Fallback: if title is empty, try the first line\n", "                        if not title and lines:\n", "                            title = lines[0]\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "\n", "                    except Exception as e:\n", "                        print(f\"Error parsing accordion summary: {e}\")\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                top_accordions = panel_element.find_elements(\n", "                    By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                # Filter to get only the main categories (not nested ones)\n", "                main_categories = []\n", "                for accordion in top_accordions:\n", "                    try:\n", "                        # Check if this accordion has a parent accordion - if so, skip it\n", "                        parent_accordion = accordion.find_element(\n", "                            By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                        # If we found a parent accordion, this is nested, so skip\n", "                        continue\n", "                    except:\n", "                        # No parent accordion found, this is a top-level one\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            main_categories.append(parsed)\n", "\n", "                return main_categories\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Deduplicate the data to remove hierarchy conflicts\n", "            def deduplicate_rubric_data(categories):\n", "                \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "                def collect_all_subcategory_titles(cats):\n", "                    \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "                    titles = set()\n", "                    for cat in cats:\n", "                        if 'subcategories' in cat:\n", "                            for subcat in cat['subcategories']:\n", "                                titles.add(subcat['title'])\n", "                                # Recursively collect from deeper levels\n", "                                titles.update(\n", "                                    collect_all_subcategory_titles([subcat]))\n", "                    return titles\n", "\n", "                def clean_category(category, all_subcat_titles):\n", "                    \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "                    cleaned = {\n", "                        'title': category['title']\n", "                    }\n", "\n", "                    # Add score if present\n", "                    if 'score' in category:\n", "                        cleaned['score'] = category['score']\n", "\n", "                    # Process subcategories first (recursively)\n", "                    if 'subcategories' in category:\n", "                        cleaned_subcats = []\n", "\n", "                        # Collect titles of subcategories that have their own subcategories\n", "                        # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "                        parent_subcats = set()\n", "                        for subcat in category['subcategories']:\n", "                            if 'subcategories' in subcat:\n", "                                for nested in subcat['subcategories']:\n", "                                    # Only add if it's not a self-reference\n", "                                    if nested['title'] != subcat['title']:\n", "                                        parent_subcats.add(nested['title'])\n", "\n", "                        # First, identify the best version of each duplicate title\n", "                        title_to_best_subcat = {}\n", "                        for subcat in category['subcategories']:\n", "                            title = subcat['title']\n", "                            if title not in title_to_best_subcat:\n", "                                title_to_best_subcat[title] = subcat\n", "                            else:\n", "                                # Compare with existing: prefer the one with subcategories\n", "                                existing = title_to_best_subcat[title]\n", "                                current_has_subcats = 'subcategories' in subcat\n", "                                existing_has_subcats = 'subcategories' in existing\n", "\n", "                                if current_has_subcats and not existing_has_subcats:\n", "                                    # Current is better (has subcategories)\n", "                                    title_to_best_subcat[title] = subcat\n", "                                elif current_has_subcats and existing_has_subcats:\n", "                                    # Both have subcategories, prefer the one with more subcategories\n", "                                    if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                                        title_to_best_subcat[title] = subcat\n", "\n", "                        # Now process the best version of each subcategory\n", "                        for title, best_subcat in title_to_best_subcat.items():\n", "                            # Skip if this title is a subcategory of another subcategory at this level\n", "                            if title not in parent_subcats:\n", "                                cleaned_subcat = clean_category(\n", "                                    best_subcat, all_subcat_titles)\n", "                                cleaned_subcats.append(cleaned_subcat)\n", "\n", "                        if cleaned_subcats:\n", "                            cleaned['subcategories'] = cleaned_subcats\n", "\n", "                    # Process items - only include items that are NOT subcategory titles\n", "                    if 'items' in category:\n", "                        # If this category has subcategories, don't include items that belong to subcategories\n", "                        if 'subcategories' in cleaned:\n", "                            # Collect all items that belong to subcategories\n", "                            subcat_items = set()\n", "                            for subcat in cleaned['subcategories']:\n", "                                if 'items' in subcat:\n", "                                    subcat_items.update(subcat['items'])\n", "                                # Also collect items from deeper subcategories\n", "\n", "                                def collect_deep_items(cat):\n", "                                    items = set()\n", "                                    if 'items' in cat:\n", "                                        items.update(cat['items'])\n", "                                    if 'subcategories' in cat:\n", "                                        for sc in cat['subcategories']:\n", "                                            items.update(\n", "                                                collect_deep_items(sc))\n", "                                    return items\n", "                                subcat_items.update(collect_deep_items(subcat))\n", "\n", "                            # Only keep items that don't belong to any subcategory\n", "                            filtered_items = []\n", "                            for item in category['items']:\n", "                                if item not in subcat_items and item not in all_subcat_titles:\n", "                                    filtered_items.append(item)\n", "\n", "                            if filtered_items:\n", "                                cleaned['items'] = filtered_items\n", "                        else:\n", "                            # No subcategories, keep all items\n", "                            cleaned['items'] = category['items']\n", "\n", "                    return cleaned\n", "\n", "                # First pass: collect all subcategory titles\n", "                all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "                # Second pass: clean each category and remove duplicates\n", "                cleaned_categories = []\n", "                processed_titles = set()\n", "\n", "                for category in categories:\n", "                    title = category['title']\n", "                    # Only process if this title hasn't been seen as a subcategory\n", "                    if title not in all_subcat_titles and title not in processed_titles:\n", "                        cleaned = clean_category(category, all_subcat_titles)\n", "                        cleaned_categories.append(cleaned)\n", "                        processed_titles.add(title)\n", "\n", "                return cleaned_categories\n", "\n", "            # Clean the data\n", "            clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "            print(\"\\n===== Marking Rubric (Raw) =====\")\n", "            print(rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (Cleaned) =====\")\n", "            print(clean_rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            formatted_output = format_rubric_display(clean_rubric_data)\n", "            for line in formatted_output:\n", "                print(line)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": null, "id": "eedc6a98-f175-4abe-a4f1-3a44a7bbe8a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}