{"metadata": {"name": "<PERSON>, 51F", "scraped_at": "2025-09-03T00:18:07.231011", "script_url": "https://www.oscer.ai/pwf/script/QMCIjJzCuQcP"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 51 year old female presenting to the emergency department with abdominal pain. You are the first doctor to see <PERSON>. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m 51 years old. My pronouns are she/her.", "I’ve come in as I’ve turned yellow and have excruciating pain in my upper right quadrant."], "Persona": ["I’m terrified something has gone terribly wrong inside of me and I’m going to die."], "HOPC": ["I have been feeling a ridiculous amount of pain in my upper right abdomen.", "It started about 2 days ago.", "The pain is an 8/10.", "The pain came on gradually and is difficult to point to exactly.", "The pain does not radiate anywhere.", "I have felt feverish for the last couple of days.", "I have a temperature of 39.1 degrees celsius.", "I can barely eat due to the pain and fever but when I do it hurts even more.", "I have turned yellow again recently.", "Both my skin and eyes are yellow.", "I have less frequent stools that are loose.", "I have been feeling increasingly nauseous.", "Sometimes I’ve vomited after eating.", "My urine is a normal colour."], "PMHx": ["I’ve had gallstones before but I’ve never had this much pain. Usually, I just turn yellow and have pale stools.", "8 weeks ago I had an ERCP where a stent was put in for treatment of my gallstones.", "I haven’t had periods for over a year now.", "I’m not taking any medications currently."], "FMHx": ["My parents both died of pneumonia in their 80s. They were smokers.", "I do not have any siblings.", "My children are both well – no health concerns there."], "SHx": ["I live with my husband and have two children who are currently at uni.", "I work full-time as a teacher. I love my job.", "I usually eat a Mediterranean diet. I would say it is pretty healthy, especially with the diet I have recently started.", "I am an ex-smoker. I used to smoke only occasionally at parties. I might have had only 1 or 2 per night. This was about 30 years ago. I smoked for 3 years.", "I drink a glass or two of wine when marking some student’s papers.", "I’ve never done any drugs.", "I play tennis a few times per week with a friend.", "I have lost some weight recently rather rapidly as I’ve started trying to lose weight via exercise and diet.", "I have a dog called <PERSON><PERSON><PERSON><PERSON>.", "The last time I traveled was when I went to Portugal to visit my sister a few years ago. I grew up there and moved over to Australia in my 20’s."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Cramping", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Location at Onset"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Eat", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Vomiting", "Eating or Diet", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Palpating", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Positional or Movements", "Defecating or Flatulating"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Constipation", "Asking Generally", "Black and Tarry Stools"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Hepatitis", "Gallstones", "Asking Generally", "Pancreatic Cancer"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Asking Generally", "Past Medications", "Analgesic Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Cancer", "Gallstones", "Hepatitis B", "Pancreatitis", "Asking Generally", "Peptic Ulcer Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Fatty Foods", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}]}}}