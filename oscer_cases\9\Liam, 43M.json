{"metadata": {"name": "<PERSON>, 43M", "scraped_at": "2025-09-05T12:16:06.109393", "script_url": "https://www.oscer.ai/pwf/script/GpWb03mTrmgQ"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based in an Emergency Department. <PERSON> is a 43-year-old man presenting to ED with jaundice. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am a 43 years old man.", "My pronouns are he/him.", "I have come to the ED because I have noticed that my skin and eyes have turned yellow."], "Persona": ["I am a normally fit and well 43 year old. I haven't seen a Doctor in about a month. Doctors always seem to be telling me off for my drinking.", "I am a little bit concerned about the change in colour of my skin and eyes. I have never had anything like this happen to me before."], "HOPC": ["I have noticed over the past week that my skin has become progressively more yellow.", "I have noticed in the mirror that my eyes have become a bit yellow too.", "Both my eyes and skin have definitely got worse over the last 7 days.", "I haven't felt any itchiness over my body at all.", "I have felt a bit nauseous on and off for the last week. I haven't vomited yet though.", "My appetite has been reduced over the past 3 months. I just don't feel like eating anymore and don't eat as much as I used to.", "I have unintentionally lost about 5 kilograms over the past 3 months.", "I feel a little bit hot and feverish, but nothing too major.", "I have some abdominal pain on my right hand side, right here under my ribs. This started about 5 days ago and feels dull in nature.", "I have not recently travelled overseas or domestically.", "I have not recently eaten any contaminated food", "I have had no changes to my bowel habits.", "I have had no changes to my urinary habits."], "PMHx": ["I have always been a heavy drinker.", "I get Vitamin B12 injections from my family Doctor and I started taking thiamine supplements relatively regularly since my GP prescribed them a few years ago.", "I don't otherwise have any medical conditions of note.", "I have never been hospitalised for anything.", "I have never had any surgeries.", "I don't have any allergies.", "I have had all of my immunisations including for hepatitis A and B.", "I eat fairly well but I really don't feel that hungry.", "I don't do much exercise in my spare-time. I do lots of physical labour for my job throughout the day."], "FMHx": ["My parents are both alive and well but live interstate. There are no medical problems that I am aware of that run in my family.", "I don't have any children."], "SHx": ["I am a construction worker.", "Work has been very stressful over the past 3 months. It looks like my company is going to lose its main contract and I could be unemployed.", "I have been drinking around 5 beers per day for the last 10 years.", "Over the past 3 months I have increased to around 10 beers per day to help cope with my work stress.", "I have never smoked cigarettes.", "I have never used recreational drugs.", "I live in a small flat by myself.", "I don't have a current partner.", "I don't have any pets."]}, "marking_rubric": {"HOPC": [{"title": "Jaundice", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Tattoos", "Paracetamol", "Diet or Eating", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Altered Bowel Habits", "score": 2, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Constipation", "Asking Generally", "Black and Tarry Stools"]}, {"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Enlarged Vessels", "score": 1, "items": ["Enlarged Vessels"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 2, "items": ["Cirr<PERSON>is", "Hepatitis", "Gallstones", "Liver Cancer", "Liver Failure", "Asking Generally", "Chronic Liver Disease"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Pancreatitis", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Endoscopic Retrograde Cholangiopancreatography"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Cancer", "Hepatitis", "Liver Disease", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Fatty Foods", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexual Partners", "Sexually Active"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}