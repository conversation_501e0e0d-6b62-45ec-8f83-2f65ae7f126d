{"metadata": {"name": "<PERSON><PERSON>, 45F", "scraped_at": "2025-09-03T00:19:24.156469", "script_url": "https://www.oscer.ai/pwf/script/XQ5JLiu5Kjbw"}, "tabs": {"doctor_information": {"Intro": "<PERSON><PERSON> is a 45 year old female presenting to the emergency department with abdominal pain. You have been asked to take a history from <PERSON><PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I am <PERSON><PERSON>, a 45 year old migrant and a single mother of two.", "My pronouns are she/her."], "Persona": ["I’m often stressed.", "I am worried about my health and whether I’ve got gallstones like my mother and brother did. They went through such excruciating pain."], "HOPC": ["I have had a sharp pain on my stomach more to the right side after eating dinner. Sometimes I feel pain on my right shoulder. The pain is an 8/10.", "The pain gradually worsened but now it is constant.", "I’ve had episodes of pain like this, but they always went away within 30 minutes. This one has been happening for 2 hours. It has happened each day for the past 3 days but I also experienced it for 2 days a month ago.", "When I have steaks or burgers, the pain is often worse and lasts longer. I take ibuprofen to ease the pain.", "I’m not nauseous. I haven’t vomited.", "When I have pain I feel sweaty.", "I do not have a fever.", "I have not had any indigestion.", "I have not had any jaundice.", "My bowels have been normal and there have been no changes to my stools.", "My urine is a normal colour."], "PMHx": ["I have been generally well, although I do think I can lose a few kilos.", "I do not take any medication other than ibuprofen for my pain.", "I do not have any allergies."], "FMHx": ["My father passed away when I was 14 in a car crash. My mum and brother have had their gallbladder removed. I remember the way they described their pain is very similar to what I’m feeling.", "Everyone in the family is otherwise healthy.", "I have no siblings."], "SHx": ["I drink 1 glass of wine every night and around 3 shots of spirits on the weekend.", "My diet isn’t the best. I don’t really like salad or vegetables. I eat meat or poultry every day.", "I do not smoke.", "I am often stressed because I have lost my job due to COVID-19.", "I was working full-time as a travel agent until just recently.", "My doctor has advised me to go on a diet and exercise. I’ve been trying but it’s been really difficult with two kids and being a single mother."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Pain", "score": 15, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Burning", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally", "Location at Onset"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Eat", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Vomiting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Eating or Diet", "Context at Onset", "Positional or Movements", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Shoulder Pain", "score": 1, "items": ["Shoulder Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Asking Generally", "Defecating Blood", "Black and Tarry Stools"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Food Poisoning"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Gallstones", "Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Gastritis", "Asking Generally", "Peptic Ulcer Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["NSAID", "Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Gallstones", "Pancreatitis", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Fatty Foods", "Expired Foods", "Asking Generally", "Contaminated Food or Water"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}