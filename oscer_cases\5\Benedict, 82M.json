{"metadata": {"name": "Benedict, 82M", "scraped_at": "2025-09-03T00:34:19.484209", "script_url": "https://www.oscer.ai/pwf/script/VDRoWRKPNL26"}, "tabs": {"doctor_information": {"Intro": "You are working a night shift as an intern at a busy metropolitan Emergency Department. You are asked to take the history of an 82 year old male who presents with chest pain. His name is <PERSON>.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>. My pronouns are he/him.", "I am 82 years old. I am a retired town planner."], "Persona": ["I am really worried about this pain and shortness of breath as I have never had anything like this before. I feel an impending sense of doom.", "I am not usually preoccupied by my health. I probably would not go to get check-ups from the GP if my wife didn’t nag me."], "HOPC": ["I currently have a really bad chest pain and shortness of breath.", "The chest pain woke me from sleep at about 3am this morning. This was just over half an hour ago. My wife drove me straight to the hospital.", "The pain is in the center of my chest. I would give it an 8/10 for severity.", "The pain gets worse when I take a big breath in. It also hurts a lot when I cough.", "I have had some intermittent palpitations since the pain and shortness of breath started.", "I have had a cough but haven’t brought anything up yet. This cough started when I woke with the pain.", "Just in the last few minutes I have also developed some dizziness.", "I don’t have any radiating pain to the arm, neck or jaw. I have no diaphoresis. I have not had any nausea or vomiting.", "I do not have any history of ischaemic heart disease.", "I do not have any calf pain.", "I don't have a fever."], "PMHx": ["I have prosate cancer. It is managed by an oncologist. It doesn’t worry me too much at this age. I was diagnosed 6 months ago after having some urinary symptoms. I am just being observed at the moment - they say it is low risk at my age. I am supposed to get my PSA taken every 6 months and they say I might need another biopsy at some point in the future.", "I have high blood pressure and take Perind<PERSON><PERSON>. I am good at remembering to take this medication.", "I am allergic to contrast dye. It makes my lips and face swell up.", "I don’t take any complementary or OTC medications."], "FMHx": ["I don’t know of anything that runs in my family. Both of my parents died of strokes but they were older than I am. My father passed away at 92, and my mother passed away at 89.", "I do not have any siblings.", "I have two children in their 50s. They are both well."], "SHx": ["I live with my wife in a retirement village. I am happy in this environment.", "My wife and I are early risers and go for walks almost every morning before it gets too hot. We usually just walk a few blocks to our local cafe and back.", "Hobby-wise we are both pretty into bridge at the moment. I also dabble in wood-carving.", "We have 2 children who both live nearby. They drop over quite often.", "We are quite independent and do our own shopping, cooking and cleaning. We don’t have licenses anymore but we live in an area with plenty of public transport. My wife is healthy.", "We don’t drink. I used to drink at parties as a teenager. It used to be only a couple of beers. It was only a few times. I haven't had a drink in a very long time now.", "I am a smoker. I smoke 2 or 3 cigarettes per day and have done so for about 17 years. I have tried to quit a few times but it has never lasted for very long. I have no real intention at quitting at this stage."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Ripping", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 2, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Resting", "Injuries", "Palpating", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Legs"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Pneumonia", "Pneumothorax", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally", "Coronary Artery Disease"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Deep Vein Thrombosis", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}, {"title": "Occupational Exposure", "score": 1, "items": ["Animals", "Asbestos", "General <PERSON><PERSON>s", "Chemicals or Toxins"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}]}}}