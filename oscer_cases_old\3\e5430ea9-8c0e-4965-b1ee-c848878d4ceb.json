{"metadata": {"name": "<PERSON>, 70M", "scraped_at": "2025-09-03T00:14:10.060582", "script_url": "https://www.oscer.ai/pwf/script/qMWTVoV2AWSg"}, "tabs": {"doctor_information": {"Intro": "You are a medical student at a general practice. Your next patient is <PERSON>, a 70 year old male presenting with palpitations. Take a focused history from <PERSON>, with an aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I’m <PERSON>, a 70 year old farmer. My pronouns are he/him.", "I’ve come to visit the doctor because I’ve been getting episodes where my heart is racing."], "Persona": ["I’m quite relaxed and laid back.", "I don’t think that my heart racing is a major problem."], "HOPC": ["I’ve been experiencing episodes of strong and rapid heart beatings for the past two weeks. The palpitations seem irregular.", "The episodes seem to come out of nowhere and start suddenly.", "The episodes fluctuate in length but they generally vary between 15 and 20 minutes.", "Rest doesn’t alleviate the palpitations.", "I feel fatigued after getting the palpitations. Only time can relieve these symptoms.", "I do not get any chest, jaw or arm pain.", "I haven’t lost consciousness.", "I don't get any shortness of breath with these episodes.", "I have not had any nausea or vomiting with these episodes.", "I have no pain or fever.", "I have not had a cough recently.", "My weight hasn’t changed and my tolerance to heat has remained the same.", "I don’t feel overly stressed.", "I do not have any lumps or swelling around my neck.", "I have not had any problems with my sleep recently."], "PMHx": ["I’ve was diagnosed with type 2 diabetes about 30 years ago.", "I also have high blood pressure and cholesterol.", "I've been on Lisinopril, Sitagliptin and Metformin for the past 30 years. Sometimes I take Panadol for mild headaches.", "The doctor wants to put me on something for my cholesterol but I have just been trying to eat healthier. I feel like I am on enough drugs.", "I have no allergies.", "I have never been diagnosed with any mental health conditions. I have never had anxiety.", "I do not think anyone in my family has had any arrhythmias."], "FMHx": ["My parents died of heart disease, but they were in their 70's and they'd had heart problems for a long time.", "I do not have any other siblings.", "I do not know of any diseases that run in the family.", "I do not have any family history of thyroid problems."], "SHx": ["I am an apple farmer. I manage and run the farm which can be stressful at times but I have always loved it.", "I always make sure I wear full PPE when we are spraying chemicals on the farm.", "I was previously married but my wife passed away in a car accident about 10 years ago.", "I live alone at the moment.", "I have about 3 cans of beer a night and have done so since I was a teenager.", "I've been smoking a pack a day for almost 50 years now.", "I do not do any specific exercise. The work on the farm is very physical though and I have always thought that would be enough.", "I do not use any illicit drugs and have never used IV drugs.", "I am not currently sexually active."]}, "marking_rubric": {"HOPC": [{"title": "Palpitations", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Rate", "Pounding", "Fluttering", "Regularity", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Work", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Rate of Heartbeats"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally", "Most Recent Episode"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Smoking", "Caffeine", "Exertion", "Anxiousness", "Medications", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1}, {"title": "Thyroid Disease History", "score": 1, "items": ["Hyperthyroidism", "Thyroid Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Anxiety", "Arrhythmia", "Panic Attacks", "Hyperthyroidism", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Diet", "score": 1, "items": ["Caffeine", "Salt-Rich Diet", "Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}