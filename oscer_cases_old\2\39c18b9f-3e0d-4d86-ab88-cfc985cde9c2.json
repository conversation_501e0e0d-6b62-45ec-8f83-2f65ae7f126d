{"metadata": {"name": "Leigh, 75M", "scraped_at": "2025-09-03T00:00:48.551308", "script_url": "https://www.oscer.ai/pwf/script/olBorfCRyOQg"}, "tabs": {"doctor_information": {"Intro": "You are a junior doctor on your emergency medicine rotation. <PERSON> is a 75-year-old man (he/him) presenting to the ED with dark stools. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am 75 years old.", "My pronouns are he/him.", "I have come to the ED because I have had some dark stools."], "Persona": ["I am a relatively well man for my age, the only medical condition I am actively managing is osteoarthritis.", "I am worried about the change in the colour of my stools, I’ve never seen anything like it."], "HOPC": ["I have noticed that my bowel movements have been particularly dark for the last 3 days.", "They smell terrible too.", "I am not too sure what could be causing this.", "I have never had anything like this in the past.", "Nothing like this has ever happened to me before.", "It is not really affecting my day-to-day life too much, I am more just worried that something is wrong.", "Nothing has helped the dark stools.", "Nothing has made the dark stools worse.", "I have not been going to the bathroom more often.", "I have also noticed some abdominal pain.", "I would say the pain is a burning type of pain.", "The pain is in the middle of my abdomen up high.", "The pain doesn’t radiate anywhere else.", "The pain began about a week ago, it was quite mild.", "It is there most of the time now.", "The pain is quite a bit worse now I would say an 8/10 when it’s bad.", "The pain seems to get a bit worse after I eat.", "I have never had pain like this before.", "Antacids seem to relieve the pain a little bit.", "I have been having a little bit of trouble with my osteoarthritis recently.", "I have had to start taking some medication to try and get that pain under control.", "I have not had a fever.", "I have not lost weight recently.", "I do not feel fatigued.", "I have never been diagnosed with cancer.", "I am usually the picture of good health, aside from my bad knees of course.", "I have not been unwell recently.", "I have not travelled recently.", "I have had no changes to my urinary habits."], "PMHx": ["I am a healthy 75-year-old man, the only health concern I have is the osteoarthritis in my knees.", "I have started taking diclofenac 50mg morning and night to try and control the pain.", "I never miss a dose.", "I have had my gallbladder removed.", "I don’t take any over the counter medications.", "I am up to date with my bowel cancer screening.", "As far as I know, I don’t have any allergies.", "I am up to date with all my vaccinations, I receive my flu shot every year."], "FMHx": ["There are no medical conditions that run in my family.", "No one in my family has been diagnosed with bowel cancer."], "SHx": ["I am retired. I worked as a music teacher until I was about 70.", "My days now are quite relaxing, I do a bit of gardening and I like to walk.", "I love to play golf too.", "I have been walking and golfing a bit less because of my knees.", "I eat quite a healthy diet.", "I do not smoke cigarettes, I have never smoked cigarettes.", "I have a nice glass of scotch most nights at around 9 o’clock before bed.", "I have never used recreational drugs.", "I don’t really have much stress in my life, things are going really well.", "My sleep habits are good.", "I have a lovely wife named <PERSON>.", "We have two kids who are all grown up themselves now.", "I have a beautiful little dog named <PERSON>."]}, "marking_rubric": {"HOPC": [{"title": "Dark Stools", "score": 11, "subcategories": [{"title": "Quality", "score": 4, "subcategories": [{"title": "Colour", "score": 1, "items": ["Asking Generally"]}, {"title": "Character", "score": 3, "items": ["<PERSON><PERSON><PERSON>", "Consistency", "Black and Tarry", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 1, "subcategories": [{"title": "Context and Aggravating Factors", "score": 1, "items": ["Patient Ideas", "Context at Onset", "Iron Supplements"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 5, "subcategories": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Anal Pain", "score": 1, "items": ["Anal Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Painful Swallowing", "score": 1, "items": ["Painful Swallowing"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}]}, {"title": "Complications", "score": 1, "subcategories": [{"title": "Anaemia", "score": 1, "items": ["Anaemia"]}]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["St<PERSON>ch Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Gastrointestinal Disease History", "score": 2, "items": ["Polyp", "Gastritis", "Lynch Syndrome", "Asking Generally", "Ulcerative Colitis", "Oesophageal Varices", "Peptic Ulcer Disease", "Inflammatory Bowel Disease", "Familial Adenomatous Polyposis", "Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 3, "subcategories": [{"title": "Prescription Medications", "score": 2, "items": ["NSAID", "<PERSON><PERSON><PERSON>", "Duration", "Warfarin", "Cessation", "Ibuprofen", "Iron Supplement", "Amount of NSAIDs", "Asking Generally", "Duration of NSAIDs"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["Colonoscopy", "Cancer Screening", "Most Recent FOBT", "Faecal Occult Blood Test"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Polyp", "Carcinoma", "Bowel Cancer", "Lynch Syndrome", "Asking Generally", "Peptic Ulcer Disease", "Gastrointestinal Disease", "Familial Adenomatous Polyposis"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Processed Meat", "Amount of Fibre", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Patient Questions"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}