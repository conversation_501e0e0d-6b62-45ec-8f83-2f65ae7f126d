{"metadata": {"name": "<PERSON>, 73M", "scraped_at": "2025-09-03T11:56:22.465142", "script_url": "https://www.oscer.ai/pwf/script/G0326IzQp7Tw"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You have been asked to see <PERSON>, a 73 year old male, presenting with shortness of breath. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m a 73 year old male. My pronouns are he/him.", "I’m retired now, but I used to work as an marketing manager.", "I’ve come in to see the doctor today because I'm concerned about my breathing.", "I feel short of breath even when I do my short trip to the shops now."], "Persona": ["I thought all the tiredness and weight loss is just because I’m getting old.", "I’d just be happy if you could fix up the troubles I’ve been having with my breathing."], "HOPC": ["The shortness of breath came on gradually a couple of years back.", "I feel short of breath at rest, but it gets a lot worse when I exert myself and even just walking.", "Normally I’d rate the shortness of breath as a 5 out of 10.", "I get short of breath at night, but it never really wakes me.", "Over the last 6 months I've felt the shortness of breath get much worse.", "I have a cough as well.", "I've been coughing a bit of gunk up for years now.", "The sputum is usually clear, but lately there have been some red streaks in it.", "I don't know how much gunk I cough up, but it's a decent amount.", "I've also felt quite tired recently, but I thought that’s because I’m getting old.", "I’ve been getting sweaty at night for the last 6 months or so.", "I've had to go down a couple of beltholes so I guess I’ve lost some weight. I think I've lost 6 or 7kg in the last 6 months.", "I've noticed a loss of my appetite.", "I've also noticed I get swelling in both ankles towards the end of the day.", "I haven’t had a fever.", "I don’t have a wheeze", "I haven’t had any chest pain.", "I haven’t had palpitations.", "I don’t have to get up at night to use the toilet more often."], "PMHx": ["I don’t have any medical conditions, apart from this cough that’s been going on for the past 15 years.", "I had asthma as a child, but I haven't had any attacks or problems since I was 12. I don't currently take or require any asthma medications.", "I’m not currently taking any prescription medications.", "I don’t take any over the counter, alternative or supplement medications either.", "I’ve never had any allergic reactions."], "FMHx": ["I can't think of anyone in the family who has had any lung problems.", "Both of my parents died relatively young when they were about 50 years old in a car accident.", "I have a brother who is alive and healthy and one daughter who is also healthy, as far as I am aware."], "SHx": ["I smoked a pack a day for 40 years. I quit about 2 years ago.", "I drink when I’m hanging out with friends every weekend. I drink 1-2 pints of beer but I’m careful though.", "I’m retired now, but I was an accountant.", "I was an accountant, so I never had exposure to chemicals or building sites or things like that.", "I don't exercise at the best of times, but this shortness of breath has made exercise impossible.", "My diet is fine, it’s normally lots of meats and salads.", "I’ve divorced from my wife a couple of years ago. So currently alone. My daughter visits often though."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Talk", "Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 2, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening", "Duration of Episodes"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Ideas", "Resting", "Night-time", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Waking up Short of Breath", "Shortness of Breath when Laying Down"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Bone Pain", "score": 1, "items": ["Bone Pain"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Leg <PERSON>welling", "score": 1, "items": ["Leg <PERSON>welling"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal Congestion"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Asking Generally", "Respiratory Infections", "Interstitial Lung Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Diuretics", "Methotrexate", "ACE Inhibitor", "Asking Generally", "Beta Antagonists", "Antihypertensives"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Spirometry", "Chest X-ray", "Peak Flow Rate"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Cancer", "Lung Cancer", "Heart Disease", "Asking Generally", "Pulmonary Embolism", "Respiratory Disease", "Interstitial Lung Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}]}, {"title": "Occupational History", "score": 2, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Retired", "Occupation", "Previous Occupation"]}, {"title": "Occupational Exposure", "score": 1, "items": ["Dust", "<PERSON><PERSON>", "Animals", "Asbestos", "Radiation", "Carcinogen Exposure"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}