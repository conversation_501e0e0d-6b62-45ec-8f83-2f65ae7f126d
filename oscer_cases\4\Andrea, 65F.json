{"metadata": {"name": "<PERSON>, 65F", "scraped_at": "2025-09-03T00:17:31.344316", "script_url": "https://www.oscer.ai/pwf/script/A75kHDamBWHm"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 65 year old female (she/her) presenting to outpatients complaining of chest pain. Take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I am 65 years old. My pronouns are she/her.", "I have recently retired from my job as an English teacher.", "I’ve come in to see the doctor today because I have been experiencing some recent chest pain over the last couple of weeks."], "Persona": ["I have seen those advertisements on TV and I am very worried that I have had a heart attack.", "I hope it’s just a muscular strain."], "HOPC": ["I feel the pain right in the middle of my chest behind my sternum.", "Last time I felt the pain it also moved into my left shoulder.", "It’s a very strange feeling, it feels like a heaviness, like someone is sitting on my chest.", "The pain varies in intensity, the first two times I felt it, it wasn’t too bad but last time it was terrible, maybe an 8 out of 10.", "I probably noticed the first pain a couple of weeks ago.", "The pain starts quite quickly and lasts about 15 minutes before it goes away.", "The first two times I noticed the pain, I was out on a walk with my husband. The last time I was walking up a flight of stairs and the pain was much worse than the previous two times.", "The pain went away after I stopped and rested but nothing else seems to help.", "I can’t think of anything that made the pain worse, it went away after I rested.", "I was feeling quite nauseous when the pain came on and started to sweat as well.", "I have not had a fever recently, I have no cough and the pain is not worse on deep inspiration.", "I have not had any palpitations.", "I do not have a cough.", "I have not had any recent illnesses or fevers.", "I have had no recent long periods of immobility and have not noticed any pain in my calves.", "I have not had any recent surgeries, fractures or traumatic injuries."], "PMHx": ["I have high blood pressure and high cholesterol for which I take an ACE inhibitor and a statin.", "I have never had any problems with my heart in the past", "I don’t have any allergies."], "FMHx": ["There are no significant medical conditions in my family.", "My parents are both still alive and they don't have any heart problems.", "My adult children are healthy.", "I have one sister who suffers from a bit of depression but she is on medication for that now."], "SHx": ["I live with my husband at home and have 2 adult children.", "Our children have both just left home, my husband has also retired from banking, and we are planning a trip around Australia soon.", "I smoke 5 cigarettes per day and have been smoking at this level for 40 years.", "I don’t drink alcohol or take recreational drugs.", "I walk with my husband every now and then. I maybe walk a couple of kilometres two to three times per week.", "I try to have a healthy diet composed of mix of meat, fish vegetables and fruit. I also make sure to get enough fibre in my meals."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Burning", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Resting", "Injuries", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes", "Metabolic Syndrome"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}]}}}