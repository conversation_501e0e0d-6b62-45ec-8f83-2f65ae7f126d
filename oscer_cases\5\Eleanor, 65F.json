{"metadata": {"name": "<PERSON>, 65F", "scraped_at": "2025-09-03T00:28:50.229099", "script_url": "https://www.oscer.ai/pwf/script/9v0QR3cvs1nr"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in the emergency department. You have been asked to see <PERSON>, a 65 year old female (she/her) presenting with chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I am a 65 year old female (she/her)", "I am a recently retired graphic designer.", "I am presenting today to the GP because of excruciating chest pain."], "Persona": ["I take life easy.", "I am not educated well about my health. So often it is hard for me answer specific medical questions."], "HOPC": ["I have been experiencing chest pain in the centre of my chest, more towards the left side.", "I’m not getting pain anywhere else.", "The pain is constant but it has happened once before. The last time the pain happened it resolved in 15 minutes.", "I first noticed the pain about 30 minutes ago.", "The pain is now more severe than the first time I felt it. It doesn't get any better.", "With the current chest pain I can barely do anything.", "The pain came on within a few minutes.", "The chest pain is getting worse.", "I can’t think of anything specifically that would have caused this. I have had no trauma to my chest.", "The pain severity does not change on movement or changing position.", "The pain stays the same when I breathe in deeply.", "I have no chest pain at night. I got this chest pain after eating breakfast.", "I have noticed I have become a bit out of puff today. This is the first time that I feel breathless.", "The breathless has not affected me too much. It is the chest pain that is bothering me the most.", "I don't have any tearing back pain. Eating food does not make the pain better or worse. Resting helped to improve the pain last time but not this time.", "I have no cough.", "I don't have any ankle swelling.", "I haven't been immobile.", "I don't have a fever."], "PMHx": ["I have been diagnosed with high blood pressure.", "I take a medication for my blood pressure but I can’t remember the name of it.", "I also have a high cholesterol level which I'm managing through medications.", "I also have type 2 diabetes. I take a drug for that one too. The drug has helped with weight loss.", "I don’t take any herbal medications or supplements.", "I don’t have any allergies and my vaccinations are up to date.", "I’ve never had any surgeries or been admitted to hospital.", "I am overweight.", "I haven't had any cardiovascular issues."], "FMHx": ["My father died of a heart attack in his 60s.", "My mother is alive and well.", "I do not have any siblings."], "SHx": ["I do not have a healthy diet, I mostly eat fast food, including burgers and chips.", "I don’t do any exercise because I find it hard leaving the house sometimes.", "I am happily married.", "I don't have any children.", "I haven’t travelled recently.", "I occasionally drink a glass of wine. Mostly when I hang out with my friends once a month.", "I smoke 20 cigarettes a day. I've been smoking for 40 years.", "I have been smoking the same amount for approximately 40 years.", "I've tried quitting but I've found it hard.", "I have never used recreational drugs.", "I am independent and am able to complete my activities of daily living."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Injuries", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Mental Health History", "score": 1, "items": ["Asking Generally", "Depression or Low Mood"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Asking Generally", "Gastro-Oesophageal Reflux Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}