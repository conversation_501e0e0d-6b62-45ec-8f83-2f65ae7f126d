{"metadata": {"name": "<PERSON><PERSON><PERSON>, 19M", "scraped_at": "2025-09-03T00:16:23.292078", "script_url": "https://www.oscer.ai/pwf/script/Zpd8KCaWn0ld"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based at a GP clinic. <PERSON><PERSON><PERSON> is a 19 year old male presenting to general practice complaining of diarrhoea. Please take a history from <PERSON><PERSON><PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 19 year old male, studying science at university.", "I have come to the GP today due to some diarrhoea."], "Persona": ["I am a young, healthy male.\nI am not too worried about my illness and simply want a medical certificate for missing my practical class at university."], "HOPC": ["The diarrhoea is quite frequent up to 6 times per day. There is no blood or mucus in the diarrhoea. The stool is watery and brown. It started yesterday quite suddenly.", "I have also had a couple of episodes of vomiting and was feeling nauseus yesterday. I vomited up food only. The vomit was a pale colour and there was no blood in it. I have not been vomiting or feeling nauseus today.", "It hasn’t been getting better or worse.", "One of my friends at university was complaining about diarrhoea a couple of days ago.", "I haven’t really tried anything to make the diarrhoea better.", "I have noticed that I’ve been feeling quite hot since yesterday.", "I also feel quite thirsty.\nI haven’t noticed any tremors, restlessness or weight loss recently.", "My stools are easy to flush.", "I am not intolerant to lactose.", "I have not eaten any expired or suss food lately."], "PMHx": ["I am otherwise healthy.", "I take no medications and no over the counter drugs.", "I have no allergies.", "My vaccinations are up to date."], "FMHx": ["My mother has high blood pressure. I think she takes some medication for it.", "I have no family history of any cancers.", "I do not know of any medical conditions that run in the family.", "I do not have any siblings."], "SHx": ["I have never smoked.", "I drink approximately twice a month on weekends with friends from university. I probably have around 3 to 4 beers each time.", "I have never taken any recreational drugs.", "I am not sexually active.", "I still live at home with my parents.", "My diet is pretty good. My parents are health freaks and I just eat what they put on the table.", "I exercise a lot. I am playing either football or futsal depending on the time of the year and do at least 2 fitness sessions and 1 game per week."]}, "marking_rubric": {"HOPC": [{"title": "Diarr<PERSON>a", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally"]}, {"title": "Character", "score": 1, "items": ["Blood", "Consistency", "Ability to Flush", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Fasting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Swimming", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Constipation", "Faecal Urgency", "Incomplete Faecal Emptying"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Gastrointestinal Disease History", "score": 2, "items": ["Asking Generally", "Inflammatory Bowel Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "Gastrointestinal Disease", "Inflammatory Bowel Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}