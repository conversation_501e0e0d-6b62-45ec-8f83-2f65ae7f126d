{"metadata": {"name": "<PERSON>, 48F", "scraped_at": "2025-09-02T23:53:02.766604", "script_url": "https://www.oscer.ai/pwf/script/GF00IwGWYucy"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You have been asked to see <PERSON>, a 48 year old female, who has presented with a fever. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 48 year old female and a manager at a clothing department store. My pronouns are she/her."], "Persona": ["I’m an anxious person and worried about my father who is immunocompromised. He just had a kidney transplant (2 weeks ago).", "I’m worried that the fever is a sign of COVID-19.", "I am not very good at wearing a mask all the time at work because I find it hard to breathe in them."], "HOPC": ["I manage a clothing department store and we recently had a customer come in who has recently tested positive for coronavirus.", "I have come in today because I have had a fever and I'm worried I'm infected.", "The fever started a couple of days ago. I decided to come in today because I was notified I had been in contact with a COVID case.", "I cannot think of anyone else I have been around who has been sick.", "I have noticed a tingling sensation at the back of my throat. It is not sore but just feels weird.", "I feel like I have lost my sense of smell slightly.", "I have had a non-productive cough at times since my fever started.", "I have been feeling more tired than usual.", "I have not been sneezing and I do not have a blocked or runny nose.", "I don’t feel short of breath.", "I have not had any recent unintentional weight loss.", "I have not had any night sweats.", "I do not have swollen lymph nodes or tonsils."], "PMHx": ["I regularly take vitamins and minerals as supplements but otherwise take no medication.", "I have slightly high blood pressure. My GP has just put me onto a diet to see if that will control it.", "I have no allergies.", "I am up to date with all my vaccinations except COVID.", "I have never had any surgeries."], "FMHx": ["My father is a diabetic which is why he needed his kidney transplant.", "I think mum has some blood pressure issues as well but she is on medication these days. I think that has got it under control.", "I have 2 siblings who are both around the same age as me and they are both healthy as far as I am aware.", "I have 2 children, aged 6 and 8 who are both happy and healthy.", "I have no family history of lung cancer."], "SHx": ["I work as a manager at a clothing department. I have been working here for 8 years now.", "We try to enforce the store policy that all customers have to wear masks but some people ignore the signs and it is hard to police when the store is busy.", "The job does not pay that well but it is stable. They have me on a part-time contract working 30 hours per week. I quite enjoy it and do not find it too stressful.", "I drink a glass of red wine a night.", "I do not smoke.", "I have not travelled recently.", "I live with my husband, my 2 kids and my sick father. It is a little crowded but I am happy at home."]}, "marking_rubric": {"HOPC": [{"title": "Fever", "score": 10, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Temperature"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Travel", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Coryzal Symptoms", "score": 2, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Swelling", "score": 1}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Muscle Pain", "score": 1, "items": ["Muscle Pain"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "General History", "score": 3, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Covid-19 Test"]}]}, {"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 2, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Infection Prevention", "score": 1, "items": ["Hand Hygiene", "Social Distancing", "Personal Protective Equipment Use"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}