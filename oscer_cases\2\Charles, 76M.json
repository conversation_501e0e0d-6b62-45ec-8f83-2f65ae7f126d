{"metadata": {"name": "<PERSON>, 76M", "scraped_at": "2025-09-03T00:05:29.936617", "script_url": "https://www.oscer.ai/pwf/script/ypdr1HPVqK1P"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in surgical outpatients. You are seeing <PERSON>, a 76 year old male, who is complaining of shortness of breath. Please take a history with a view of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 76 year old man and I am a retiree.", "My pronouns are he/him.", "I’ve come to the doctor today because I’ve been having some trouble breathing."], "Persona": ["I can be pretty stubborn and disgruntled.", "I’m starting to get very worried about the shortness of breath.", "I have been putting off coming to see the doctor because I don’t want to admit that something is wrong."], "HOPC": ["I have been getting short of breath for the past 2 months.", "Initially it was only when I was doing moderate exercise, but now I’m struggling to take the dog for a walk.", "I used to be able to go for a 30 minute walk every day, but now I can only go for 15 minutes and I feel winded.", "I also get these bouts of dizziness/light-headedness sometimes when I walk too quickly up stairs, or when I go up steep hills.", "I also get more short of breath than I used to when I am exerting myself.", "I almost fainted the other day when I stood up from the couch, luckily I fell back onto the couch. I'm lucky I didn’t fall and hit my head!", "I’ve only started having these “dizzy” episodes in the past month.", "I’ve also been getting some 3/10 chest pain when I walk up steep hills.", "I’ve been getting this chest pain for the past couple months.", "The chest pain feels like a pressure behind my sternum and goes away when I rest.", "It doesn’t radiate anywhere.", "I have not had any palpitations.", "I have not had any limb swelling.", "I have not had any nausea or vomiting."], "PMHx": ["I have had hypertension for the past 10 years, but it’s well managed.", "I take <PERSON><PERSON><PERSON> daily for my hypertension and I've been taking it for more than 5 years now.", "I don’t take any other medications.", "I don’t have any allergies and all my vaccinations are up to date.", "I think I've only been to hospital once for a tonsillectomy when I was about 10."], "FMHx": ["My dad died of a heart attack when he was 78 and my mum died of old age at 82.", "I have no siblings, and my children are all healthy as far as I’m aware."], "SHx": ["I am divorced and live by myself with my dog, <PERSON>.", "I have 2 adult son’s who have families of their own now.", "I have been drinking since I was a teenager. I usually have a pint or 2 with dinner.", "I haven't smoked in 15 years. I smoked for about 6 years before I quit.", "I used to smoke about half a pack a day.", "I’ve never taken illicit drugs, alcohol was always enough of a vice for me.", "I have been retired for the past 5 years . I used to own my own newsagency.", "I try to walk the dog every day but I am struggling to go for a walk at the moment because of the breathlessness and chest pain.", "I also like to play lawn bowls every now and then, but I haven’t for a while because I don’t want the guys at the club to see me like this.", "I just have a normal diet. Meat and 3 veg usually.", "I am completely independent with all my activities of daily living.", "I have never had exposure to silica dust or asbestos."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Diurnal Variation", "Onset of Worsening", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Anxiousness", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1}, {"title": "Swelling", "score": 1}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Pneumonia", "Asking Generally", "Interstitial Lung Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease", "Hypertrophic Cardiomyopathy"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Adherence", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asthma", "Heart Disease", "Heart Failure", "Asking Generally", "Respiratory Disease", "Coronary Artery Disease", "Interstitial Lung Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Retired", "Occupation", "Duration At Job", "Workload Current", "Workload Baseline", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}