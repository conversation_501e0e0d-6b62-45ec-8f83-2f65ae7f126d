{"metadata": {"name": "<PERSON>, 66F", "scraped_at": "2025-09-03T00:09:31.621650", "script_url": "https://www.oscer.ai/pwf/script/NQyBXy04kgFa"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 66 year old female retired nurse. She has presented to the emergency department complaining of chest pain. As the medical student seeing <PERSON>, please take a history from her with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I’m <PERSON>. I’m a 66 year old retired nurse. My pronouns are she/her. I came to the clinic today because I’ve been having some chest pain."], "Persona": ["I like to connect to people through stories. I am an energetic person and kind whenever I can be.", "I’ve never had chest pain like this. I’m really worried that it’s something serious."], "HOPC": ["I’ve had a sharp chest pain on my left side for the past 3 days.", "The chest pain doesn’t move anywhere.", "The pain isn’t too severe but it is uncomfortable.", "The chest pain is worsened when I take deep breaths", "The chest pain has been quite constant since it started.", "I also have had a painful cough since the chest pain came on.", "With the cough I bring up a teaspoon of thick yellow-greenish phlegm. I haven’t seen any blood in my phlegm.", "I also feel short of breath all the time since I’ve gotten chest pain. It gets worse when I go up the stairs or try to do too much. I don’t think an allergen triggers my breathlessness.", "I think I might be having a fever as well, that started around the same time as the cough.", "I also have less appetite than I usually do.", "I don’t feel overly fatigued.", "I haven’t had any weight loss or night sweats.", "I haven’t been immobile for extended periods"], "PMHx": ["I take calcium and vitamin D supplements daily.", "My gallbladder was removed a few years ago with no complications.", "My lung function was checked a month ago and everything seemed fine.", "I do not take any prescription medications."], "FMHx": ["My father had a heart-attack in his 60s which he survived through. But unfortunately a few years ago he got prostate cancer and passed away. He was 80 at the time.", "My mum is quite fine. She has diabetes."], "SHx": ["I am no longer working. I have just recently retired after a long career as an oncology nurse.", "I’ve been smoking half a pack for 40 years.", "I usually have a glass or two of red wine with my friends on the weekend.", "I live with my husband <PERSON>. I am happy at home.", "I tend to eat a healthy nutrient-rich diet.", "I haven’t travelled recently."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 13, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Burning", "Ripping", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Most Recent Episode", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Injuries", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Malaise", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["ICU admission", "Asking Generally"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Pulmonary Embolism", "Deep Vein Thrombosis"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Steroids", "Antibiotics", "Asking Generally", "Beta Antagonists", "Combination Inhaler", "Immunosuppressant Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asthma", "Asking Generally", "Clotting Disorders", "Pulmonary Embolism", "Respiratory Disease", "Deep Vein Thrombosis"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Retired", "Occupation", "Previous Occupation", "Personal Protective Equipment Use"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}