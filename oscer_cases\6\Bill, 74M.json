{"metadata": {"name": "<PERSON>, 74M", "scraped_at": "2025-09-03T00:38:32.505938", "script_url": "https://www.oscer.ai/pwf/script/HVZqsjZjmxtn"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on rotation in a general practice. You are seeing <PERSON>, a 74 year old male who has come in complaining of shortness of breath. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m a male (he/him) and this year I’m 74 years young.", "I’ve come into the doctor’s because <PERSON> made me. She’s sick and tired of me always complaining that I can’t do anything around the house because I get a bit breathless."], "Persona": ["I guess I’m most concerned that it’s something related to my heart. I had some troubles with my heart 5 years ago, and they said to watch out for breathlessness or pain.", "I am usually pretty jolly and love to have a chat with anyone."], "HOPC": ["I’ve been feeling a little short of breath, especially when I’m walking around and what not.", "The shortness of breath has been getting worse over the last 2 or 3 days.", "I think I could only make it 10 metres or so before I need to sit down to get my breath back.", "Sitting and resting seems to make things better, I’m fine then.", "Sleeping has been terrible, I’ve actually woken up twice feeling totally short of breath in the last few days. I had to sit up and gasp for breath until I could get it back.", "I normally sleep with 3 pillows, I’ve probably done that for 3 years or so, I find I can breathe a bit easier that way.", "The last few years especially I’ve had trouble with both my legs getting all swollen.", "By the end of the day I always notice that my socks have left a mark, that’s something that never happened until a few years ago.", "I seem to get a dry cough at night too.", "I haven’t been coughing up any muck, and I haven’t had a wheeze.", "I haven’t been feverish and feeling hot or cold or anything like that.", "I haven’t had any cramping or pain in my legs.", "I can get a bit of chest pain when I’m walking around.", "I haven’t felt nauseous or been confused.", "The waterworks have been fine I guess. Well fine for 74, things trickle a bit more than they used to!", "I have been feeling very fatigued – probably because I am not sleeping very much.", "I have not had any recent surgeries, long haul flights or periods of immobility."], "PMHx": ["I’ve had high blood pressure for over 10 years now, since I was in my sixties.", "The doctor told me I had high cholesterol around that time too I guess.", "I had a heart attack five years ago. It was a pretty bad one the doc said, it was all blocked up. Luckily we were visiting the kids in the city so they got me into hospital early and unplugged it quickly.", "The doctors have me on all these tablets since the heart troubles, I’ve got a list of them. I take 40mg of Furosemide twice a day, 16mg of Candesartan once a day, 25mg of Metoprolol two times a day, 100mg of Aspirin a day, and 40 mg of Atorvastatin a day.", "I’ve got enough tablets to worry about to keep the heart going, I don’t take any of that herbal, witch-doctor stuff.", "I don’t have any allergies.", "All of my vaccinations are up to date."], "FMHx": ["Like me, Dad had high blood pressure and his heart eventually gave way.", "My Mum’s health was always pretty good, she just passed away from old age.", "I have three kids and a couple of grandkids who are all very healthy.", "I do not have any siblings."], "SHx": ["I live at home with my wife, <PERSON>.", "I guess the belly is a little bit rounder than it should be, <PERSON> and my GP have been saying for years that I needed to lose some weight. It wasn’t until the heart attack that I started doing something about it.", "Since I’ve recovered from the heart attack, I was trying to do as much exercise as I could. But recently I can’t do anything, if I go out to get the mail I’m totally spent.", "My diet is pretty good now, I cleaned it up after the heart nearly stopped a few years ago.", "It’s <PERSON> and <PERSON> at home, we have 3 kids but they’re all grown up and moved into the city, we even have a couple of grandkids!", "I’m retired now, but I used to be a school teacher.", "I guess the smokes and drinks were always a weak point for me, I always knew I did too much of them but it was until the heart nearly gave way that I’ve cut back.", "I now have one beer on a Sunday with dinner.", "I used to drink 4-5 beers every night for as long as I can remember, but the heart problems changed that.", "I’ve fully stopped the smoking now.", "I used to smoke probably 10 a day since I was a teenager, see it was all quite normal back then, no one knew how bad the stuff was for you.", "I’ve never touched drugs, and I’ve warned my kids never to either. It’s nasty stuff."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Waking up Short of Breath", "Shortness of Breath when Laying Down"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Increased Urination"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally", "Pulmonary Embolism", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Workload Current", "Workload Baseline"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}