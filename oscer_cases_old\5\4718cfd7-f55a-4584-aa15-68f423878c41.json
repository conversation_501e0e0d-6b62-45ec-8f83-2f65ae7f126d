{"metadata": {"name": "Lai, 25M", "scraped_at": "2025-09-03T00:34:55.270734", "script_url": "https://www.oscer.ai/pwf/script/ECZ0YsGrQJFX"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You have been asked to see <PERSON>, a 25 year old male presenting with shortness of breath. You are required to take a history from Ho Shan, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am 25 years old.", "I identify as a man (he/him).", "I work as a junior economist for the state government."], "Persona": ["I am a busy professional.", "I have been ignoring my symptoms for quite some time, but I became quite short of breath on a run the other day. I didn't have a phone to call for help if it got worse which was quite scary."], "HOPC": ["I have been having episodes of shortness breath and coughing fits for years now but has been getting worse and more frequent recently.", "I have an annoying cough a lot of the time even when I am not having those bad episodes.", "They seem to get worse when I am exercising and during winter.", "It feels like I just can't get enough air in and my chest feels tight.", "My shortness of breath and coughing seems to be worse and more frequent when my allergies are at their worst.", "I don't cough up any phlegm.", "I do not feel short of breath all of the time. It is mostly when I exercise or am exposed to dust.", "Smoking seems to make it worse also.", "I have also been feeling a little more tired than usual recently.", "I have not tried any medication for these symptoms yet.", "I have not had contact with anyone who is unwell or any confirmed cases of COVID-19 in the last month.", "I do not feel particuarly stressed or anxious."], "PMHx": ["I have really bad seasonal allergies.", "I take a nasal steroid spray for my hayfever.", "I also have allergic conjunctivitis that I use over-the-counter antihistamine drops for.", "I have had these allergy symptoms for as long as I can remember.", "I do not have any other diagnosed medical conditions.", "I do not take any regular medications."], "FMHx": ["My mother had asthma for her whole life and takes steroids for it sometimes. She was diagnosed as a child.", "My father is healthy.", "I do not have any children.", "I do not have any siblings."], "SHx": ["I am a smoker. I have smoked for 5 years. I smoke 10 cigarettes per day. I have no intention to quit at this stage.", "I drink once per fortnight on a night out. I will have around 15 standard drinks in the form mixed spirits on these nights. I do not see my drinking behaviour as problematic or dangerous and have no intention to cut down at this stage.", "I live with my partner who also works as a public servant. I am happy at home.", "We do not have any pets.", "I have not had any exposure to stone dusts or known carginogens."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 2, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Allergies", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Season or Temperature", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Stress", "score": 1, "items": ["Stress"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1, "items": ["Face"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Emotional", "score": 1, "items": ["Emotional"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Taste Changes", "score": 1, "items": ["Loss of Taste"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON>sal <PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Anaphylaxis History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma", "Asking Generally"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 3}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asthma", "<PERSON><PERSON><PERSON>", "Hayfever", "Lung Disease", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Infectious Contacts", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally", "Contact with Covid-19 Cases"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupational Exposure", "score": 1, "items": ["Dust", "Animals", "Asbestos"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}