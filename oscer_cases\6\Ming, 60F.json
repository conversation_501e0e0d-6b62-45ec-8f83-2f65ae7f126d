{"metadata": {"name": "Ming, 60F", "scraped_at": "2025-09-03T00:37:57.260397", "script_url": "https://www.oscer.ai/pwf/script/KmRteaGUKJ1F"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 60 year old retired school administrator who has presented to the emergency department with shortness of breath. Take a detailed history from <PERSON> to establish a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 60 year old woman and I‘m now retired.", "I’ve come to the doctor today because I have been experiencing shortness of breath."], "Persona": ["I am a friendly person but English is my second language so I speak in simple sentences.", "I am concerned because I’ve been getting so tired too, I have to rely on my children and neighbours to help with the chores.", "I’m hoping the doctor will fix me up so I can stop feeling like this."], "HOPC": ["I have been experiencing breathlessness for the past 6 months.", "My breathlessness has been gradually getting more severe.", "I feel short of breath at least 4 to 5 times per day.", "The episodes of breathlessness usually last 1 to 2 minutes.", "I would rate the shortness of breath as an 8 out of 10", "The breathlessness gets worse whenever I am exerting myself like cleaning the house or walking to the supermarket.", "Lying down also makes the breathlessness worse.", "Resting and sitting up seem to improve my shortness of breath.", "I can barely walk 50m anymore, I used to be able to walk 1 kilometre to go and do my grocery shopping.", "At nights when I lie down, sometimes I suddenly gasp for air and need to sit up.", "I tend to cough at night as well, it doesn’t happen a lot but it is getting worse.", "My shoes don’t seem to fit in any more.", "I noticed the swelling in my ankles a couple of days ago.", "Both of my ankles are swollen.", "The swelling has been persistent since I noticed it.", "I have never had swelling like this before.", "My daughters said the other day that my ankles look bigger than they used to.", "I’m feeling tired and I can’t get enough sleep each night.", "I have no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats."], "PMHx": ["I have type 2 diabetes mellitus.", "I was diagnosed with diabetes 15 years ago", "I used to be on 500mg of Metformin to control my diabetes but I stopped a couple of months ago.", "I have high blood pressure.", "I have had high blood pressure for 5 years.", "I take 10mg of lisinopril and 25mg of hydrochlorothiazide for my hypertension.", "I had a mild heart attack when I was 57 years old.", "I am on atorvastatin 40mg daily.", "I don’t have any allergies that I’m aware of.", "All my vaccinations are up to date."], "FMHx": ["I have had a family history of heart problems. My father and uncles all suffered from heart diseases. Unfortunately, they all passed away in their 70s."], "SHx": ["I am originally from mainland China.", "I used to work as a school administrator but I am retired now.", "I live alone, my husband died a few years ago from a heart attack.", "I have 5 children.", "None of my children have any medical conditions.", "I am well supported, all my children come to visit me.", "I have never had alcohol before.", "I have never smoked before.", "I usually try to have a healthy diet composed of a mix of meat, vegetables and fruit, but getting to the supermarkets is such a challenge at the moment.", "My exercise is that I usually walk to the shops which are about a kilometre away from me.", "I'm a bit overweight, I have tried reducing my weight."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Allergies", "Positional", "Anxiousness", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Chest Pain", "score": 1, "items": ["Tightness", "Chest Pain"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Urinating at Night", "Increased Urination"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 2, "items": ["COPD", "Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Adherence", "Antibiotics", "Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Heart Failure", "Asking Generally", "Pulmonary Embolism", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}]}, {"title": "Occupational Exposure", "score": 1, "subcategories": [{"title": "Environmental Exposure", "score": 1, "items": ["Dust", "Air Pollution", "Exhaust <PERSON>", "Sulphur Dioxide", "Indoor Fireplace"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}