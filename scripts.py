import json
import uuid
import os
import time
import random
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager

from utils import extract_rubric_data, deduplicate_rubric_data


def human_like_delay(min_seconds=1, max_seconds=3):
    """Add random delay to mimic human behavior and avoid rate limiting"""
    delay = random.uniform(min_seconds, max_seconds)
    print(f"Waiting {delay:.1f} seconds...")
    time.sleep(delay)


def save_patient_data(file_name, script_data, page_number):
    """Save individual patient data to organized folder structure"""
    # Create the main oscer_cases directory if it doesn't exist
    base_dir = "oscer_cases"
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)

    # Create the page directory if it doesn't exist
    page_dir = os.path.join(base_dir, str(page_number))
    if not os.path.exists(page_dir):
        os.makedirs(page_dir)

    # Save the patient data as uuid.json
    filename = os.path.join(page_dir, f"{file_name}.json")
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(script_data, f, indent=4, ensure_ascii=False)

    print(f"Saved patient data to {filename}")


# Setup Chrome options with anti-detection measures
options = Options()
options.add_argument("--start-minimized")
options.add_argument("--disable-blink-features=AutomationControlled")
options.add_experimental_option("excludeSwitches", ["enable-automation"])
options.add_experimental_option('useAutomationExtension', False)
options.add_argument(
    "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

driver = webdriver.Chrome(service=Service(
    ChromeDriverManager().install()), options=options)

# Execute script to remove webdriver property
driver.execute_script(
    "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

try:
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. After login, go directly to Scripts page
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )
    page = 9
    last_page = False
    while True:
        i = 0
        if last_page:
            break  # all cards of last page scraped
        while True:
            driver.get("https://www.oscer.ai/dashboard/scripts")
            # Wait for the scripts page to load by waiting for script cards
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located(
                    (By.XPATH, "//div[contains(@class, 'jss308')]"))
            )
            for count in range(page):
                next_button = driver.find_element(
                    By.XPATH, "//button[@aria-label='Go to next page']")
                if "disabled" in next_button.get_attribute("class"):
                    last_page = True

                next_button.click()

                # Wait for new page to load by waiting for script cards to refresh
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located(
                        (By.XPATH, "//div[contains(@class, 'jss308')]"))
                )

            cards = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located(
                    (By.XPATH, "(//div[contains(@class, 'jss308')])"))
            )
            # cards = cards[:1]

            if i > len(cards) - 1:
                page += 1
                break

            card = cards[i]
            # patient_uuid = uuid.uuid4()
            name = card.find_element(
                By.XPATH, ".//div[contains(@class, 'jss322')]").text
            actions = ActionChains(driver)
            actions.move_to_element(card).perform()

            # 7. Click "Open Script" button that appears
            try:
                open_button = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable(
                        (By.XPATH, f"(//span[text()='Open Script'])[{i+1}]"))
                )
                open_button.click()
            except Exception as e:
                print("No open button found")
                i += 1
                continue

            # 8. Wait for tab list to load (using tour-id which is stable)
            try:
                tabs = WebDriverWait(driver, 10).until(
                    EC.presence_of_all_elements_located(
                        (By.XPATH, "//ul[@role='tablist']//div[@tour-id]"))
                )
            except:
                print("No tabs found")
                i += 1
                continue

            # Initialize the main data structure
            script_data = {
                "metadata": {
                    "name": name,
                    "scraped_at": datetime.now().isoformat(),
                    "script_url": driver.current_url
                },
                "tabs": {}
            }

            # 9. Iterate through each tab
            for tab in tabs:
                # e.g. "tab-Doctor Information"
                tab_name = tab.get_attribute("tour-id")
                tab.click()

                # Add small delay after clicking tab
                human_like_delay(0.5, 1.5)

                # Wait for the tab content to load by waiting for the active panel
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((
                        By.XPATH,
                        "//div[@role='tabpanel' and not(contains(@style,'display: none'))]"
                    ))
                )

                if "Doctor Information" in tab_name:
                    active_panel = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((
                            By.XPATH,
                            "//div[@role='tabpanel' and not(contains(@style,'display: none'))]"
                        ))
                    )
                    raw_text = active_panel.text

                    # Structure by headings
                    sections = {}
                    lines = raw_text.split("\n")
                    current = "Intro"
                    sections[current] = ""
                    for line in lines:
                        if line in ["Requirements", "Finished?", "Ready to see your results?"]:
                            current = line
                            sections[current] = ""
                        else:
                            sections[current] += line + " "

                    # Clean up the sections
                    cleaned_sections = {}
                    for k, v in sections.items():
                        cleaned_sections[k] = v.strip()

                    script_data["tabs"]["doctor_information"] = cleaned_sections

                elif "Script" in tab_name:
                    # Find the associated panel id from the selected tab's parent <li role="tab">
                    tab_li = tab.find_element(
                        By.XPATH, "ancestor::li[@role='tab']")
                    panel_id = tab_li.get_attribute("aria-controls")

                    # Wait for the correct tabpanel by id (e.g., react-tabs-3)
                    panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
                    panel = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, panel_xpath))
                    )

                    # Inside that panel, wait for the content container
                    container = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located(
                            (By.XPATH, f"{panel_xpath}//div[@id='containerElement']"))
                    )

                    # Now extract sections: each <p id="..."> followed by its <ul>
                    sections = {}
                    try:
                        headings = WebDriverWait(driver, 10).until(
                            EC.presence_of_all_elements_located(
                                (By.XPATH, f"{panel_xpath}//div[@id='containerElement']//p[@id]"))
                        )
                    except:
                        print("No headings found")
                        break

                    for heading in headings:
                        title = heading.text.strip()
                        try:
                            ul = heading.find_element(
                                By.XPATH, "following-sibling::ul[1]")
                            items = [li.text.strip()
                                     for li in ul.find_elements(By.TAG_NAME, "li")]
                            sections[title] = items
                        except:
                            sections[title] = []

                    script_data["tabs"]["script"] = sections

                elif "Marking Rubric" in tab_name:
                    tab_li = tab.find_element(
                        By.XPATH, "ancestor::li[@role='tab']")
                    panel_id = tab_li.get_attribute("aria-controls")

                    panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
                    panel = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, panel_xpath))
                    )

                    medical_history_tabs = panel.find_elements(
                        By.XPATH, ".//li[@role='tab']")

                    if medical_history_tabs == []:
                        print("No medical history tabs found")
                        break

                    target_categories = ['HOPC', 'Assoc.',
                                         'PMHx', 'FMHx', 'Social', 'Basics']
                    marking_rubric_data = {}

                    for med_tab in medical_history_tabs:
                        med_tab_name = med_tab.text.strip()

                        if med_tab_name not in target_categories:
                            continue

                        try:
                            med_tab.click()
                            # Add small delay after clicking medical tab
                            human_like_delay(0.5, 1.0)
                            # Wait for the medical tab content to load
                            WebDriverWait(driver, 5).until(
                                EC.presence_of_element_located(
                                    (By.XPATH, f"//div[@role='tabpanel' and @id='{med_tab.get_attribute('aria-controls')}']"))
                            )
                        except Exception as e:
                            print(
                                f"Could not click medical history tab {med_tab_name}: {e}")
                            continue

                        med_panel_id = med_tab.get_attribute("aria-controls")
                        med_panel_xpath = f"//div[@role='tabpanel' and @id='{med_panel_id}']"

                        try:
                            med_panel = WebDriverWait(driver, 5).until(
                                EC.presence_of_element_located(
                                    (By.XPATH, med_panel_xpath))
                            )

                            if not med_panel.text.strip():
                                print(f"No content found for {med_tab_name}")
                                marking_rubric_data[med_tab_name] = []
                                continue

                            rubric_data = extract_rubric_data(med_panel)
                            clean_rubric_data = deduplicate_rubric_data(
                                rubric_data)

                            marking_rubric_data[med_tab_name] = clean_rubric_data

                        except Exception as e:
                            print(
                                f"Could not find panel for {med_tab_name}: {e}")
                            marking_rubric_data[med_tab_name] = []
                            continue

                    script_data["tabs"]["marking_rubric"] = marking_rubric_data

            # Save individual patient data to organized folder structure

            # save_patient_data(patient_uuid, script_data, page + 1)
            save_patient_data(name, script_data, page + 1)
            i += 1

            # Add delay between processing cards to avoid rate limiting
            if i < len(cards):  # Don't delay after the last card
                human_like_delay(2, 5)  # 2-5 second delay between cards

    print("All patient data has been saved to individual files in oscer_cases folder")

finally:
    pass  # keep browser open for debugging
