{"cells": [{"cell_type": "code", "execution_count": 1, "id": "abc3b095-01f9-4a9a-8356-6b6754b5bafa", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "Oscer Case Data Scraper - Jupyter Notebook Version\n", "\"\"\"\n", "\n", "# Imports\n", "import os\n", "import json\n", "import time\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.common.exceptions import TimeoutException, NoSuchElementException\n", "from bs4 import BeautifulSoup\n", "import logging\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fac5623c-93d2-45d2-9825-dcb58fb18f5d", "metadata": {}, "outputs": [], "source": ["class OscerScraper:\n", "    def __init__(self, headless=False, output_dir=\"oscer_cases\"):\n", "        \"\"\"\n", "        Initialize the scraper\n", "        \"\"\"\n", "        self.output_dir = output_dir\n", "        self.setup_driver(headless)\n", "        self.setup_output_directory()\n", "        self.case_data = []\n", "        \n", "    def setup_driver(self, headless):\n", "        \"\"\"Setup Chrome WebDriver with appropriate options\"\"\"\n", "        chrome_options = Options()\n", "        if headless:\n", "            chrome_options.add_argument(\"--headless\")\n", "        chrome_options.add_argument(\"--no-sandbox\")\n", "        chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "        chrome_options.add_argument(\"--window-size=1920,1080\")\n", "        \n", "        try:\n", "            self.driver = webdriver.Chrome(options=chrome_options)\n", "            self.wait = WebDriverWait(self.driver, 10)\n", "            logger.info(\"Chrome driver initialized successfully\")\n", "        except Exception as e:\n", "            logger.error(f\"Failed to initialize Chrome driver: {e}\")\n", "            raise\n", "    \n", "    def setup_output_directory(self):\n", "        \"\"\"Create output directory if it doesn't exist\"\"\"\n", "        if not os.path.exists(self.output_dir):\n", "            os.makedirs(self.output_dir)\n", "            logger.info(f\"Created output directory: {self.output_dir}\")\n", "            \n", "    def login_if_required(self, username=None, password=None):\n", "        \"\"\"\n", "        Handle login on Oscer.ai if required\n", "        \"\"\"\n", "        try:\n", "            # If already logged in (check for dashboard or logout button)\n", "            logged_in_indicators = [\n", "                \"//button[contains(text(), 'Logout')]\",\n", "                \"//div[contains(@class, 'dashboard')]\",\n", "                \"//div[contains(@class, 'profile')]\"\n", "            ]\n", "            for indicator in logged_in_indicators:\n", "                if self.driver.find_elements(By.XPATH, indicator):\n", "                    logger.info(\"Already logged in\")\n", "                    return True\n", "    \n", "            # Go to login page\n", "            self.driver.get(\"https://www.oscer.ai/signin\")\n", "            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "            time.sleep(1)\n", "    \n", "            # Ensure credentials provided\n", "            if not username or not password:\n", "                logger.error(\"Login required but no username/password provided\")\n", "                return False\n", "    \n", "            logger.info(\"Waiting for login form...\")\n", "    \n", "            # Explicit wait for React to render inputs\n", "            email_input = WebDriverWait(self.driver, 20).until(\n", "                EC.visibility_of_element_located((By.XPATH, \"//input[@type='email' or @name='email']\"))\n", "            )\n", "            password_input = WebDriverWait(self.driver, 20).until(\n", "                EC.visibility_of_element_located((By.XPATH, \"//input[@type='password']\"))\n", "            )\n", "            login_button = WebDriverWait(self.driver, 20).until(\n", "                EC.element_to_be_clickable((By.XPATH, \"//button[contains(., 'Sign in') or contains(., 'Log in')]\"))\n", "            )\n", "    \n", "            logger.info(\"Filling login form...\")\n", "    \n", "            # Fill form\n", "            email_input.clear()\n", "            email_input.send_keys(username)\n", "            time.sleep(0.5)\n", "    \n", "            password_input.clear()\n", "            password_input.send_keys(password)\n", "            time.sleep(0.5)\n", "    \n", "            login_button.click()\n", "    \n", "            # Wait for login success\n", "            try:\n", "                WebDriverWait(self.driver, 15).until(\n", "                    EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Logout')] | //div[contains(@class, 'dashboard')] | //div[contains(@class, 'profile')]\"))\n", "                )\n", "                logger.info(\"Login successful!\")\n", "                return True\n", "            except TimeoutException:\n", "                logger.warning(\"Login button clicked but no success indicator found yet\")\n", "    \n", "            # Check for error messages\n", "            error_selectors = [\n", "                \"//div[contains(text(), 'Invalid')]\",\n", "                \"//div[contains(text(), 'incorrect')]\",\n", "                \"//div[contains(text(), 'failed')]\",\n", "                \"//div[contains(@class, 'error')]\",\n", "                \"//div[contains(@class, 'alert')]\"\n", "            ]\n", "            for error_selector in error_selectors:\n", "                errors = self.driver.find_elements(By.XPATH, error_selector)\n", "                if errors:\n", "                    logger.error(f\"<PERSON><PERSON> failed: {errors[0].text}\")\n", "                    return False\n", "    \n", "            logger.warning(\"Could not verify login success, proceeding anyway...\")\n", "            return True\n", "    \n", "        except TimeoutException:\n", "            logger.error(\"Timeout while waiting for login page or elements\")\n", "            return False\n", "        except Exception as e:\n", "            logger.error(f\"Unexpected error during login: {e}\")\n", "            return False\n", "\n", "\n", "\n", "    def get_case_links(self, base_url=\"https://oscer.ai\"):\n", "        \"\"\"Collect all case links\"\"\"\n", "        logger.info(f\"Navigating to {base_url}\")\n", "        self.driver.get(base_url)\n", "        time.sleep(3)\n", "        \n", "        case_links = []\n", "        \n", "        try:\n", "            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "            case_elements = self.driver.find_elements(By.XPATH, \"//div[contains(@class, 'case') or contains(@class, 'card')]\")\n", "            \n", "            logger.info(f\"Found {len(case_elements)} potential case elements\")\n", "            \n", "            for i, element in enumerate(case_elements):\n", "                try:\n", "                    webdriver.ActionChains(self.driver).move_to_element(element).perform()\n", "                    time.sleep(0.5)\n", "                    \n", "                    open_case_button = element.find_element(By.XPATH, \".//button[contains(text(), 'Open Case') or contains(text(), 'View Case')]\")\n", "                    \n", "                    if open_case_button:\n", "                        case_link = open_case_button.get_attribute('href') or open_case_button.get_attribute('onclick')\n", "                        if case_link:\n", "                            case_links.append({'index': i, 'link': case_link, 'element': element})\n", "                            logger.info(f\"Found case {i}: {case_link}\")\n", "                \n", "                except NoSuchElementException:\n", "                    logger.debug(f\"No open case button for element {i}\")\n", "                    continue\n", "                except Exception as e:\n", "                    logger.error(f\"Error processing case {i}: {e}\")\n", "                    continue\n", "        except TimeoutException:\n", "            logger.error(\"Timeout waiting for page to load\")\n", "        \n", "        logger.info(f\"Total cases found: {len(case_links)}\")\n", "        return case_links\n", "\n", "    def extract_case_data(self, case_info):\n", "        \"\"\"Extract data from a single case page\"\"\"\n", "        case_data = {\n", "            'case_index': case_info['index'],\n", "            'doctor_information': '',\n", "            'script': '',\n", "            'marking_rubric': '',\n", "            'raw_html': '',\n", "            'error': None\n", "        }\n", "        \n", "        try:\n", "            element = case_info['element']\n", "            webdriver.ActionChains(self.driver).move_to_element(element).perform()\n", "            time.sleep(0.5)\n", "            \n", "            open_case_button = element.find_element(By.XPATH, \".//button[contains(text(), 'Open Case') or contains(text(), 'View Case')]\")\n", "            open_case_button.click()\n", "            time.sleep(3)\n", "            \n", "            page_html = self.driver.page_source\n", "            soup = BeautifulSoup(page_html, 'html.parser')\n", "            case_data['raw_html'] = str(soup)\n", "            \n", "            # Doctor Information\n", "            doctor_info_section = soup.find('div', string=lambda text: text and 'Doctor Information' in text)\n", "            if doctor_info_section:\n", "                parent = doctor_info_section.find_parent()\n", "                if parent:\n", "                    case_data['doctor_information'] = parent.get_text(strip=True)\n", "            \n", "            # Script\n", "            script_section = soup.find('div', string=lambda text: text and 'Script' in text)\n", "            if script_section:\n", "                parent = script_section.find_parent()\n", "                if parent:\n", "                    case_data['script'] = parent.get_text(strip=True)\n", "            \n", "            # Marking Rubric\n", "            rubric_section = soup.find('div', string=lambda text: text and 'Marking Rubric' in text)\n", "            if rubric_section:\n", "                parent = rubric_section.find_parent()\n", "                if parent:\n", "                    case_data['marking_rubric'] = parent.get_text(strip=True)\n", "            \n", "            logger.info(f\"Extracted data for case {case_info['index']}\")\n", "            self.driver.back()\n", "            time.sleep(2)\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error extracting data for case {case_info['index']}: {e}\")\n", "            case_data['error'] = str(e)\n", "            try:\n", "                self.driver.back()\n", "                time.sleep(2)\n", "            except:\n", "                pass\n", "        \n", "        return case_data\n", "\n", "    def save_case_data(self, case_data, format='json'):\n", "        \"\"\"Save case data to file\"\"\"\n", "        case_index = case_data['case_index']\n", "        \n", "        if format == 'json':\n", "            filename = os.path.join(self.output_dir, f\"case_{case_index}.json\")\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(case_data, f, indent=2, ensure_ascii=False)\n", "        \n", "        elif format == 'txt':\n", "            filename = os.path.join(self.output_dir, f\"case_{case_index}.txt\")\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                f.write(f\"CASE {case_index}\\n{'='*50}\\n\\n\")\n", "                f.write(\"DOCTOR INFORMATION:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['doctor_information'] + \"\\n\\n\")\n", "                f.write(\"SCRIPT:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['script'] + \"\\n\\n\")\n", "                f.write(\"MARKING RUBRIC:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['marking_rubric'] + \"\\n\\n\")\n", "        \n", "        logger.info(f\"Saved case {case_index} → {filename}\")\n", "    \n", "    def save_all_cases_summary(self):\n", "        \"\"\"Save summary of all cases\"\"\"\n", "        if not self.case_data:\n", "            logger.warning(\"No case data to save\")\n", "            return\n", "        \n", "        summary_data = []\n", "        for case in self.case_data:\n", "            summary_data.append({\n", "                'case_index': case['case_index'],\n", "                'doctor_info_length': len(case['doctor_information']),\n", "                'script_length': len(case['script']),\n", "                'rubric_length': len(case['marking_rubric']),\n", "                'has_error': case['error'] is not None,\n", "                'error': case['error']\n", "            })\n", "        \n", "        df = pd.DataFrame(summary_data)\n", "        \n", "        csv_path = os.path.join(self.output_dir, \"cases_summary.csv\")\n", "        df.to_csv(csv_path, index=False)\n", "        logger.info(f\"Saved summary → {csv_path}\")\n", "        \n", "        json_path = os.path.join(self.output_dir, \"all_cases.json\")\n", "        with open(json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(self.case_data, f, indent=2, ensure_ascii=False)\n", "        logger.info(f\"Saved all cases JSON → {json_path}\")\n", "\n", "    def scrape_all_cases(self, base_url=\"https://oscer.ai\", username=None, password=None):\n", "        \"\"\"Scrape all cases\"\"\"\n", "        try:\n", "            logger.info(\"Starting Oscer scraping...\")\n", "            self.driver.get(base_url)\n", "            time.sleep(3)\n", "            \n", "            if not self.login_if_required(username, password):\n", "                logger.error(\"<PERSON><PERSON> failed or required but missing\")\n", "                return\n", "            \n", "            case_links = self.get_case_links(base_url)\n", "            if not case_links:\n", "                logger.error(\"No cases found\")\n", "                return\n", "            \n", "            for i, case_info in enumerate(case_links):\n", "                logger.info(f\"Processing case {i+1}/{len(case_links)}\")\n", "                case_data = self.extract_case_data(case_info)\n", "                self.case_data.append(case_data)\n", "                self.save_case_data(case_data, 'json')\n", "                self.save_case_data(case_data, 'txt')\n", "                time.sleep(1)\n", "            \n", "            self.save_all_cases_summary()\n", "            logger.info(f\"Scraping done! {len(self.case_data)} cases saved.\")\n", "        \n", "        except Exception as e:\n", "            logger.error(f\"Scraping error: {e}\")\n", "        finally:\n", "            self.cleanup()\n", "    \n", "    def cleanup(self):\n", "        \"\"\"Close browser\"\"\"\n", "        if hasattr(self, 'driver'):\n", "            self.driver.quit()\n", "            logger.info(\"<PERSON><PERSON><PERSON> closed\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a9a49bf7-8445-44c1-9c6f-c7d4c98b5f9f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-27 18:22:42,948 - INFO - Chrome driver initialized successfully\n", "2025-08-27 18:22:42,950 - INFO - Starting Oscer scraping...\n", "2025-08-27 18:22:56,532 - INFO - Waiting for login form...\n", "2025-08-27 18:23:17,069 - ERROR - Timeout while waiting for login page or elements\n", "2025-08-27 18:23:17,071 - ERROR - <PERSON><PERSON> failed or required but missing\n", "2025-08-27 18:23:19,522 - INFO - <PERSON><PERSON><PERSON> closed\n"]}], "source": ["# Config\n", "BASE_URL = \"https://oscer.ai\"\n", "USERNAME = \"<EMAIL>\"  # Set if login required\n", "PASSWORD = \"techrise01badoscer\"\n", "HEADLESS = False\n", "OUTPUT_DIR = \"oscer_cases\"\n", "\n", "# Run scraper\n", "scraper = OscerScraper(headless=HEADLESS, output_dir=OUTPUT_DIR)\n", "scraper.scrape_all_cases(BASE_URL, USERNAME, PASSWORD)\n"]}, {"cell_type": "code", "execution_count": 4, "id": "87139e18-1915-4b6a-a956-280e387460b2", "metadata": {}, "outputs": [], "source": ["def test_login(self, username, password):\n", "    \"\"\"\n", "    Test only the login functionality without scraping.\n", "    Returns True if login succeeds, False otherwise.\n", "    \"\"\"\n", "    logger.info(\"Testing Oscer login only...\")\n", "    success = self.login_if_required(username, password)\n", "\n", "    if success:\n", "        logger.info(\"✅ Login test successful!\")\n", "    else:\n", "        logger.error(\"❌ Login test failed.\")\n", "\n", "    return success\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f469535b-b5f8-4be9-8cc6-3db3485ff283", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-27 18:25:47,060 - INFO - Chrome driver initialized successfully\n"]}, {"ename": "AttributeError", "evalue": "'OscerScraper' object has no attribute 'test_login'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[5]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m scraper = OscerScraper(headless=\u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m)  \u001b[38;5;66;03m# set headless=True if you don’t want browser UI\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[43mscraper\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtest_login\u001b[49m(\u001b[33m\"\u001b[39m\u001b[<EMAIL>\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mtechrise01badoscer\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: 'OscerScraper' object has no attribute 'test_login'"]}], "source": ["scraper = OscerScraper(headless=False)  # set headless=True if you don’t want browser UI\n", "scraper.test_login(\"<EMAIL>\", \"techrise01badoscer\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3f5ee91-8ea4-4370-a037-4386f7d39336", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}