{"cells": [{"cell_type": "code", "execution_count": 1, "id": "abc3b095-01f9-4a9a-8356-6b6754b5bafa", "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "\"\"\"\n", "Oscer Case Data Scraper - Jupyter Notebook Version\n", "\"\"\"\n", "\n", "# Imports\n", "import os\n", "import json\n", "import time\n", "import pandas as pd\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.common.exceptions import TimeoutException, NoSuchElementException\n", "from bs4 import BeautifulSoup\n", "import logging\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "fac5623c-93d2-45d2-9825-dcb58fb18f5d", "metadata": {}, "outputs": [], "source": ["class OscerScraper:\n", "    def __init__(self, headless=False, output_dir=\"oscer_cases\"):\n", "        \"\"\"\n", "        Initialize the scraper\n", "        \"\"\"\n", "        self.output_dir = output_dir\n", "        self.setup_driver(headless)\n", "        self.setup_output_directory()\n", "        self.case_data = []\n", "        \n", "    def setup_driver(self, headless):\n", "        \"\"\"Setup Chrome WebDriver with appropriate options\"\"\"\n", "        chrome_options = Options()\n", "        if headless:\n", "            chrome_options.add_argument(\"--headless\")\n", "        chrome_options.add_argument(\"--no-sandbox\")\n", "        chrome_options.add_argument(\"--disable-dev-shm-usage\")\n", "        chrome_options.add_argument(\"--window-size=1920,1080\")\n", "        \n", "        try:\n", "            self.driver = webdriver.Chrome(options=chrome_options)\n", "            self.wait = WebDriverWait(self.driver, 10)\n", "            logger.info(\"Chrome driver initialized successfully\")\n", "        except Exception as e:\n", "            logger.error(f\"Failed to initialize Chrome driver: {e}\")\n", "            raise\n", "    \n", "    def setup_output_directory(self):\n", "        \"\"\"Create output directory if it doesn't exist\"\"\"\n", "        if not os.path.exists(self.output_dir):\n", "            os.makedirs(self.output_dir)\n", "            logger.info(f\"Created output directory: {self.output_dir}\")\n", "            \n", "def login_if_required(self, username=None, password=None):\n", "    \"\"\"\n", "    Handle login on Oscer.ai if required\n", "    \"\"\"\n", "    try:\n", "        # If already logged in (check for dashboard or logout button)\n", "        logged_in_indicators = [\n", "            \"//button[contains(text(), 'Logout')]\",\n", "            \"//div[contains(@class, 'dashboard')]\",\n", "            \"//div[contains(@class, 'profile')]\"\n", "        ]\n", "        for indicator in logged_in_indicators:\n", "            if self.driver.find_elements(By.XPATH, indicator):\n", "                logger.info(\"Already logged in\")\n", "                return True\n", "\n", "        # Go to login page\n", "        self.driver.get(\"https://www.oscer.ai/signin\")\n", "        self.wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "        time.sleep(1)\n", "\n", "        # Ensure credentials provided\n", "        if not username or not password:\n", "            logger.error(\"Login required but no username/password provided\")\n", "            return False\n", "\n", "        logger.info(\"Waiting for login form...\")\n", "\n", "        # Explicit wait for React to render inputs\n", "        email_input = WebDriverWait(self.driver, 20).until(\n", "            EC.visibility_of_element_located((By.XPATH, \"//input[@type='email' or @name='email']\"))\n", "        )\n", "        password_input = WebDriverWait(self.driver, 20).until(\n", "            EC.visibility_of_element_located((By.XPATH, \"//input[@type='password']\"))\n", "        )\n", "        login_button = WebDriverWait(self.driver, 20).until(\n", "            EC.element_to_be_clickable((By.XPATH, \"//button[contains(., 'Sign in') or contains(., 'Log in')]\"))\n", "        )\n", "\n", "        logger.info(\"Filling login form...\")\n", "\n", "        # Fill form\n", "        email_input.clear()\n", "        email_input.send_keys(username)\n", "        time.sleep(0.5)\n", "\n", "        password_input.clear()\n", "        password_input.send_keys(password)\n", "        time.sleep(0.5)\n", "\n", "        login_button.click()\n", "\n", "        # Wait for login success\n", "        try:\n", "            WebDriverWait(self.driver, 15).until(\n", "                EC.presence_of_element_located((By.XPATH, \"//button[contains(text(), 'Logout')] | //div[contains(@class, 'dashboard')] | //div[contains(@class, 'profile')]\"))\n", "            )\n", "            logger.info(\"Login successful!\")\n", "            return True\n", "        except TimeoutException:\n", "            logger.warning(\"Login button clicked but no success indicator found yet\")\n", "\n", "        # Check for error messages\n", "        error_selectors = [\n", "            \"//div[contains(text(), 'Invalid')]\",\n", "            \"//div[contains(text(), 'incorrect')]\",\n", "            \"//div[contains(text(), 'failed')]\",\n", "            \"//div[contains(@class, 'error')]\",\n", "            \"//div[contains(@class, 'alert')]\"\n", "        ]\n", "        for error_selector in error_selectors:\n", "            errors = self.driver.find_elements(By.XPATH, error_selector)\n", "            if errors:\n", "                logger.error(f\"<PERSON><PERSON> failed: {errors[0].text}\")\n", "                return False\n", "\n", "        logger.warning(\"Could not verify login success, proceeding anyway...\")\n", "        return True\n", "\n", "    except TimeoutException:\n", "        logger.error(\"Timeout while waiting for login page or elements\")\n", "        return False\n", "    except Exception as e:\n", "        logger.error(f\"Unexpected error during login: {e}\")\n", "        return False\n", "\n", "\n", "\n", "    def get_case_links(self, base_url=\"https://oscer.ai\"):\n", "        \"\"\"Collect all case links\"\"\"\n", "        logger.info(f\"Navigating to {base_url}\")\n", "        self.driver.get(base_url)\n", "        time.sleep(3)\n", "        \n", "        case_links = []\n", "        \n", "        try:\n", "            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, \"body\")))\n", "            case_elements = self.driver.find_elements(By.XPATH, \"//div[contains(@class, 'case') or contains(@class, 'card')]\")\n", "            \n", "            logger.info(f\"Found {len(case_elements)} potential case elements\")\n", "            \n", "            for i, element in enumerate(case_elements):\n", "                try:\n", "                    webdriver.ActionChains(self.driver).move_to_element(element).perform()\n", "                    time.sleep(0.5)\n", "                    \n", "                    open_case_button = element.find_element(By.XPATH, \".//button[contains(text(), 'Open Case') or contains(text(), 'View Case')]\")\n", "                    \n", "                    if open_case_button:\n", "                        case_link = open_case_button.get_attribute('href') or open_case_button.get_attribute('onclick')\n", "                        if case_link:\n", "                            case_links.append({'index': i, 'link': case_link, 'element': element})\n", "                            logger.info(f\"Found case {i}: {case_link}\")\n", "                \n", "                except NoSuchElementException:\n", "                    logger.debug(f\"No open case button for element {i}\")\n", "                    continue\n", "                except Exception as e:\n", "                    logger.error(f\"Error processing case {i}: {e}\")\n", "                    continue\n", "        except TimeoutException:\n", "            logger.error(\"Timeout waiting for page to load\")\n", "        \n", "        logger.info(f\"Total cases found: {len(case_links)}\")\n", "        return case_links\n", "\n", "    def extract_case_data(self, case_info):\n", "        \"\"\"Extract data from a single case page\"\"\"\n", "        case_data = {\n", "            'case_index': case_info['index'],\n", "            'doctor_information': '',\n", "            'script': '',\n", "            'marking_rubric': '',\n", "            'raw_html': '',\n", "            'error': None\n", "        }\n", "        \n", "        try:\n", "            element = case_info['element']\n", "            webdriver.ActionChains(self.driver).move_to_element(element).perform()\n", "            time.sleep(0.5)\n", "            \n", "            open_case_button = element.find_element(By.XPATH, \".//button[contains(text(), 'Open Case') or contains(text(), 'View Case')]\")\n", "            open_case_button.click()\n", "            time.sleep(3)\n", "            \n", "            page_html = self.driver.page_source\n", "            soup = BeautifulSoup(page_html, 'html.parser')\n", "            case_data['raw_html'] = str(soup)\n", "            \n", "            # Doctor Information\n", "            doctor_info_section = soup.find('div', string=lambda text: text and 'Doctor Information' in text)\n", "            if doctor_info_section:\n", "                parent = doctor_info_section.find_parent()\n", "                if parent:\n", "                    case_data['doctor_information'] = parent.get_text(strip=True)\n", "            \n", "            # Script\n", "            script_section = soup.find('div', string=lambda text: text and 'Script' in text)\n", "            if script_section:\n", "                parent = script_section.find_parent()\n", "                if parent:\n", "                    case_data['script'] = parent.get_text(strip=True)\n", "            \n", "            # Marking Rubric\n", "            rubric_section = soup.find('div', string=lambda text: text and 'Marking Rubric' in text)\n", "            if rubric_section:\n", "                parent = rubric_section.find_parent()\n", "                if parent:\n", "                    case_data['marking_rubric'] = parent.get_text(strip=True)\n", "            \n", "            logger.info(f\"Extracted data for case {case_info['index']}\")\n", "            self.driver.back()\n", "            time.sleep(2)\n", "            \n", "        except Exception as e:\n", "            logger.error(f\"Error extracting data for case {case_info['index']}: {e}\")\n", "            case_data['error'] = str(e)\n", "            try:\n", "                self.driver.back()\n", "                time.sleep(2)\n", "            except:\n", "                pass\n", "        \n", "        return case_data\n", "\n", "    def save_case_data(self, case_data, format='json'):\n", "        \"\"\"Save case data to file\"\"\"\n", "        case_index = case_data['case_index']\n", "        \n", "        if format == 'json':\n", "            filename = os.path.join(self.output_dir, f\"case_{case_index}.json\")\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(case_data, f, indent=2, ensure_ascii=False)\n", "        \n", "        elif format == 'txt':\n", "            filename = os.path.join(self.output_dir, f\"case_{case_index}.txt\")\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                f.write(f\"CASE {case_index}\\n{'='*50}\\n\\n\")\n", "                f.write(\"DOCTOR INFORMATION:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['doctor_information'] + \"\\n\\n\")\n", "                f.write(\"SCRIPT:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['script'] + \"\\n\\n\")\n", "                f.write(\"MARKING RUBRIC:\\n\" + \"-\"*20 + \"\\n\")\n", "                f.write(case_data['marking_rubric'] + \"\\n\\n\")\n", "        \n", "        logger.info(f\"Saved case {case_index} → {filename}\")\n", "    \n", "    def save_all_cases_summary(self):\n", "        \"\"\"Save summary of all cases\"\"\"\n", "        if not self.case_data:\n", "            logger.warning(\"No case data to save\")\n", "            return\n", "        \n", "        summary_data = []\n", "        for case in self.case_data:\n", "            summary_data.append({\n", "                'case_index': case['case_index'],\n", "                'doctor_info_length': len(case['doctor_information']),\n", "                'script_length': len(case['script']),\n", "                'rubric_length': len(case['marking_rubric']),\n", "                'has_error': case['error'] is not None,\n", "                'error': case['error']\n", "            })\n", "        \n", "        df = pd.DataFrame(summary_data)\n", "        \n", "        csv_path = os.path.join(self.output_dir, \"cases_summary.csv\")\n", "        df.to_csv(csv_path, index=False)\n", "        logger.info(f\"Saved summary → {csv_path}\")\n", "        \n", "        json_path = os.path.join(self.output_dir, \"all_cases.json\")\n", "        with open(json_path, 'w', encoding='utf-8') as f:\n", "            json.dump(self.case_data, f, indent=2, ensure_ascii=False)\n", "        logger.info(f\"Saved all cases JSON → {json_path}\")\n", "\n", "    def scrape_all_cases(self, base_url=\"https://oscer.ai\", username=None, password=None):\n", "        \"\"\"Scrape all cases\"\"\"\n", "        try:\n", "            logger.info(\"Starting Oscer scraping...\")\n", "            self.driver.get(base_url)\n", "            time.sleep(3)\n", "            \n", "            if not self.login_if_required(username, password):\n", "                logger.error(\"<PERSON><PERSON> failed or required but missing\")\n", "                return\n", "            \n", "            case_links = self.get_case_links(base_url)\n", "            if not case_links:\n", "                logger.error(\"No cases found\")\n", "                return\n", "            \n", "            for i, case_info in enumerate(case_links):\n", "                logger.info(f\"Processing case {i+1}/{len(case_links)}\")\n", "                case_data = self.extract_case_data(case_info)\n", "                self.case_data.append(case_data)\n", "                self.save_case_data(case_data, 'json')\n", "                self.save_case_data(case_data, 'txt')\n", "                time.sleep(1)\n", "            \n", "            self.save_all_cases_summary()\n", "            logger.info(f\"Scraping done! {len(self.case_data)} cases saved.\")\n", "        \n", "        except Exception as e:\n", "            logger.error(f\"Scraping error: {e}\")\n", "        finally:\n", "            self.cleanup()\n", "    \n", "    def cleanup(self):\n", "        \"\"\"Close browser\"\"\"\n", "        if hasattr(self, 'driver'):\n", "            self.driver.quit()\n", "            logger.info(\"<PERSON><PERSON><PERSON> closed\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "a9a49bf7-8445-44c1-9c6f-c7d4c98b5f9f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-27 18:33:28,404 - INFO - Chrome driver initialized successfully\n", "2025-08-27 18:33:28,406 - INFO - Starting Oscer scraping...\n", "2025-08-27 18:33:40,330 - INFO - Waiting for login form...\n", "2025-08-27 18:34:00,695 - ERROR - Timeout while waiting for login page or elements\n", "2025-08-27 18:34:00,696 - INFO - Navigating to https://oscer.ai\n", "2025-08-27 18:34:05,895 - INFO - Found 76 potential case elements\n", "2025-08-27 18:34:05,955 - ERROR - Error processing case 0: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,016 - ERROR - Error processing case 1: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,048 - ERROR - Error processing case 2: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,106 - ERROR - Error processing case 3: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,134 - ERROR - Error processing case 4: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,243 - ERROR - Error processing case 5: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,384 - ERROR - Error processing case 6: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,455 - ERROR - Error processing case 7: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,494 - ERROR - Error processing case 8: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,506 - ERROR - Error processing case 9: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,602 - ERROR - Error processing case 10: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:06,619 - ERROR - Error processing case 11: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:09,135 - ERROR - Error processing case 15: Message: move target out of bounds\n", "  (Session info: chrome=139.0.7258.139)\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c9e50c]\n", "\t(No symbol) [0x0x7ff632c6b19a]\n", "\t(No symbol) [0x0x7ff632c93344]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:11,880 - ERROR - Error processing case 18: Message: invalid session id: session deleted as the browser has closed the connection\n", "from disconnected: not connected to DevTools\n", "  (Session info: chrome=139.0.7258.139); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bec0fa]\n", "\t(No symbol) [0x0x7ff632bd8355]\n", "\t(No symbol) [0x0x7ff632bfceee]\n", "\t(No symbol) [0x0x7ff632c727c5]\n", "\t(No symbol) [0x0x7ff632c92c72]\n", "\t(No symbol) [0x0x7ff632c6af73]\n", "\t(No symbol) [0x0x7ff632c341b1]\n", "\t(No symbol) [0x0x7ff632c34f43]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\tGetHandleVerifier [0x0x7ff632e4c974+115220]\n", "\tGetHandleVerifier [0x0x7ff632e4cb29+115657]\n", "\tGetHandleVerifier [0x0x7ff632e33268+11016]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,457 - ERROR - Error processing case 19: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,461 - ERROR - Error processing case 20: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,465 - ERROR - Error processing case 21: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,469 - ERROR - Error processing case 22: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,474 - ERROR - Error processing case 23: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,478 - ERROR - Error processing case 24: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,483 - ERROR - Error processing case 25: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,494 - ERROR - Error processing case 26: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,500 - ERROR - Error processing case 27: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,508 - ERROR - Error processing case 28: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,512 - ERROR - Error processing case 29: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,516 - ERROR - Error processing case 30: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,523 - ERROR - Error processing case 31: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,528 - ERROR - Error processing case 32: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,532 - ERROR - Error processing case 33: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,539 - ERROR - Error processing case 34: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,544 - ERROR - Error processing case 35: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,549 - ERROR - Error processing case 36: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,559 - ERROR - Error processing case 37: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,562 - ERROR - Error processing case 38: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,567 - ERROR - Error processing case 39: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,586 - ERROR - Error processing case 40: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,598 - ERROR - Error processing case 41: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,610 - ERROR - Error processing case 42: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,617 - ERROR - Error processing case 43: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,625 - ERROR - Error processing case 44: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,631 - ERROR - Error processing case 45: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,636 - ERROR - Error processing case 46: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,642 - ERROR - Error processing case 47: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,646 - ERROR - Error processing case 48: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,661 - ERROR - Error processing case 49: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,668 - ERROR - Error processing case 50: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,674 - ERROR - Error processing case 51: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,678 - ERROR - Error processing case 52: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,685 - ERROR - Error processing case 53: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,695 - ERROR - Error processing case 54: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,701 - ERROR - Error processing case 55: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,707 - ERROR - Error processing case 56: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,711 - ERROR - Error processing case 57: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,724 - ERROR - Error processing case 58: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,730 - ERROR - Error processing case 59: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,735 - ERROR - Error processing case 60: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,742 - ERROR - Error processing case 61: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,757 - ERROR - Error processing case 62: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,762 - ERROR - Error processing case 63: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,797 - ERROR - Error processing case 64: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,824 - ERROR - Error processing case 65: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,843 - ERROR - Error processing case 66: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,868 - ERROR - Error processing case 67: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,873 - ERROR - Error processing case 68: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,876 - ERROR - Error processing case 69: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,881 - ERROR - Error processing case 70: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,886 - ERROR - Error processing case 71: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,891 - ERROR - Error processing case 72: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,896 - ERROR - Error processing case 73: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,905 - ERROR - Error processing case 74: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,909 - ERROR - Error processing case 75: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception\n", "Stacktrace:\n", "\tGetHandleVerifier [0x0x7ff632e43d85+79397]\n", "\tGetHandleVerifier [0x0x7ff632e43de0+79488]\n", "\t(No symbol) [0x0x7ff632bebf2c]\n", "\t(No symbol) [0x0x7ff632c332df]\n", "\t(No symbol) [0x0x7ff632c6b062]\n", "\t(No symbol) [0x0x7ff632c65b93]\n", "\t(No symbol) [0x0x7ff632c64c59]\n", "\t(No symbol) [0x0x7ff632bb6fd5]\n", "\tGetHandleVerifier [0x0x7ff63310e1ed+3005069]\n", "\tGetHandleVerifier [0x0x7ff63310831d+2980797]\n", "\tGetHandleVerifier [0x0x7ff633127e0d+3110573]\n", "\tGetHandleVerifier [0x0x7ff632e5d6de+184190]\n", "\tGetHandleVerifier [0x0x7ff632e6516f+215567]\n", "\t(No symbol) [0x0x7ff632bb5fd1]\n", "\tGetHandleVerifier [0x0x7ff633231d48+4199912]\n", "\tBaseThreadInitThunk [0x0x7ff965d77374+20]\n", "\tRtlUserThreadStart [0x0x7ff9675fcc91+33]\n", "\n", "2025-08-27 18:34:12,910 - INFO - Total cases found: 0\n", "2025-08-27 18:34:12,911 - ERROR - No cases found\n", "2025-08-27 18:34:14,938 - INFO - <PERSON><PERSON><PERSON> closed\n"]}], "source": ["# Config\n", "BASE_URL = \"https://oscer.ai\"\n", "USERNAME = \"<EMAIL>\"  # Set if login required\n", "PASSWORD = \"techrise01badoscer\"\n", "HEADLESS = False\n", "OUTPUT_DIR = \"oscer_cases\"\n", "\n", "# Run scraper\n", "scraper = OscerScraper(headless=HEADLESS, output_dir=OUTPUT_DIR)\n", "scraper.scrape_all_cases(BASE_URL, USERNAME, PASSWORD)\n"]}, {"cell_type": "code", "execution_count": null, "id": "f3f5ee91-8ea4-4370-a037-4386f7d39336", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}