{"metadata": {"name": "<PERSON>, 16F", "scraped_at": "2025-09-05T12:42:20.304672", "script_url": "https://www.oscer.ai/pwf/script/pptzoN5y5ewV"}, "tabs": {"doctor_information": {"Intro": "You are a medical student at a busy urban GP office. <PERSON> is a 16 year old girl presenting with transient loss of consciousness. Please take a history with the aim of establishing a diagnosis to present to your supervising GP.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm 16 years old.", "My pronouns are she/her. You can call me <PERSON><PERSON>.", "Mum made me come in today, but I'm really embarrassed about what happened."], "Persona": ["I am an embarrassed teenager, I'd rather forget this ever happened.", "I'm sure I'm fine.", "I don't have any contact with the healthcare system as I am usually well."], "HOPC": ["I briefly lost consciousness yesterday.", "The biology teacher was showing us a video of child birth at school. All I remember was that there was a lot of blood.", "I wanted to walk out of the room and I fainted as I was walking to the door.", "I did not hurt my head. My friend caught me.", "Before I fainted, I had a funny feeling like I was going to faint.", "The audio of the video and the voices in the hallway disappeared and all I could hear was ringing in my ears.", "My vision was blurry right before I fainted.", "My friend said I was only out for about 20 seconds.", "I did not bite my tongue, I did not urinate myself and I did not have any seizure activity while I was unconscious.", "I recovered immediately after waking up. I wasn't a fan of the attention I was getting.", "I didn't have any numbness or weakness.", "This has never happened to me before."], "PMHx": ["I am well and healthy.", "I have never been diagnosed with diabetes and I am not taking any insulin or glycaemic agents.", "I do not take any regular medications.", "I take a multivitamin for children with breakfast every morning."], "FMHx": ["I don't know of any medical conditions that run in my family.", "I still have all of my grandparents."], "SHx": ["I live at home with my Mum and Dad.", "I have never touched alcohol or drugs.", "I tried a cigarette at a party earlier this year, but I found it disgusting. I haven't touched cigarettes since.", "I'm focusing on my studies because I want to study a Bachelor of Law at university.", "I love playing basketball with my friends during lunch break at school.", "I am well supported by my family and my friends.", "My mental health is fine."]}, "marking_rubric": {"HOPC": [{"title": "Transient Loss of Consciousness", "score": 17, "subcategories": [{"title": "After the Transient Loss of Consciousness", "score": 4, "subcategories": [{"title": "After the Episode", "score": 3, "items": ["Resting", "Weakness", "Confusion", "Asking Generally", "Recovery Duration"]}, {"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Before the Transient Loss of Consciousness", "score": 6, "subcategories": [{"title": "Leading up to Episode", "score": 2, "items": ["Diet", "Recent Illness", "Lifestyle Changes"]}, {"title": "Immediately before Episode", "score": 4, "items": ["<PERSON>ra", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cry Out", "Cy<PERSON><PERSON>", "Standing", "Sweating", "Dizziness", "Chest Pain", "Palpitations", "Hearing Changes", "Sensory Changes", "Asking Generally"]}]}, {"title": "During the Transient Loss of Consciousness", "score": 5, "subcategories": [{"title": "Associated Features", "score": 4, "items": ["Biting", "Jerking", "Drooling", "Injuries", "<PERSON><PERSON><PERSON>", "Asking Generally", "Faecal Incontinence", "Urinary Incontinence"]}, {"title": "Time Course of Episode", "score": 1, "items": ["Episode Duration"]}]}, {"title": "History of TLOC", "score": 1, "subcategories": [{"title": "Previous Episodes", "score": 1, "items": ["Frequency of Episodes"]}]}, {"title": "Confirming Environment", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Asking Generally"]}, {"title": "Witnesses", "score": 1, "items": ["Asking Generally"]}]}]}], "Assoc.": [], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Malnutrition Disease History", "score": 1, "items": ["Anorexia Nervosa", "Eating Disorders"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Asking Generally", "Atrial Fibrillation", "Hypertrophic Cardiomyopathy"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Blood Sugar Level"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Asking Generally", "Past Medications", "Oral Hypoglycaemics"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Syncope", "Arrhythmia", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}