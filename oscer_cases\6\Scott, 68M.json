{"metadata": {"name": "<PERSON>, 68M", "scraped_at": "2025-09-03T00:35:31.319806", "script_url": "https://www.oscer.ai/pwf/script/SUqyqzljO905"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 68 year old male. He is presenting to the emergency department complaining of chest pain. Please take a focused history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 68 year old male (he/him) and a truck driver.", "I have come to the doctor today because I have severe chest pain."], "Persona": ["I’m a relaxed person and a family man.", "I’m worried about the chest pain and fear that it is a heart problem like it was for my father."], "HOPC": ["The pain started this morning while I was watching TV.", "The pain started 30 minutes ago.", "I felt pain come on in the centre of my chest.", "I’m getting some pain in my left shoulder and left arm. I’m still experiencing the pain and I would say that it is getting worse.", "When I move and change my position the chest pain isn’t affected.", "The chest pain feels like a pressure pain. It feels kind of like there is an elephant sitting on my chest.", "The chest pain doesn’t feel sharp or tight.", "The pain seems to be getting so much worse that I can’t even walk.", "Breathing or lying down doesn’t make the chest pain worse.", "I feel exhausted because of it. I’ve never had this kind of pain before.", "I have been feeling short of breath since the chest pain started.", "I also have been feeling my heart pounding strongly. This happened when the chest pain started. The rhythm is regular but very fast.", "I’m sweating a lot.", "I feel nauseous but I haven’t vomited.", "My skin is pale on my face.", "I don’t have a fever.", "I do not have a cough.", "I have not been especially stressed recently.", "I haven’t had any long haul flights or prolonged periods of immobility recently. I have also not had any recent surgeries."], "PMHx": ["My cholesterol isn’t great. I’ve been taking atorvastatin for the past couple of years. I am mostly good at taking it.", "My <PERSON> has mentioned a couple of times that I should lose weight.", "She has also said that my blood pressure is high. Last time I checked it was 150/90. I am not on any medications yet though. The GP is thinking about starting me on something.", "I have not had any tests performed on my heart before.", "I have not had any heart problems in the past.", "I don’t have any autoimmune diseases.", "I do not have asthma.", "I don’t have <PERSON><PERSON><PERSON>s syndrome.", "I do not have anxiety.", "I don't have any allergies.", "My vaccinations are up to date."], "FMHx": ["My father died of a heart attack in his 60s.", "My brother also struggles with his blood pressure and cholesterol."], "SHx": ["My diet is not that great. I think I eat too much fat.", "I don’t exercise.", "I’ve been drinking 1-2 beers every night since my late teens. Sometimes I have 4 cans on the weekend. I don’t have any desire to reduce my alcohol consumption. I do not see it as a problem.", "I have been smoking a pack a day since my late teens. I’m not thinking about quitting at this age. I think that ship has sailed for me. I haven’t used any illicit drugs.", "I am happily married and have a bunch of family and friends I can always count on. I live with my wife. I’m pretty relaxed.", "I am independent in all aspects of my life.", "I work as a truck driver."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 4, "items": ["<PERSON>", "Burning", "Ripping", "Mild or Dull", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 2, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Resting", "Palpating", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Sense of Impending Doom", "score": 1, "items": ["Sense of Impending Doom"]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Statins", "Antacids", "Asking Generally", "Antihypertensives"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Heart Surgery", "Asking Generally", "Vascular Surgery", "Coronary Interventions"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Heart Surgery", "Asking Generally", "Vascular Surgery", "Coronary Interventions"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}