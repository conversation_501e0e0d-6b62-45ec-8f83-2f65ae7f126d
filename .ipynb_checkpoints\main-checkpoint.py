import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from webdriver_manager.chrome import ChromeDriverManager

# Setup Chrome options
options = Options()
options.add_argument("--start-maximized")
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)

try:
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. After login, go directly to Scripts page
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )
    driver.get("https://www.oscer.ai/dashboard/scripts")

    # 5. Wait until script cards load
    first_card = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, "(//div[contains(@class, 'jss308')])[1]"))
    )

    # 6. Hover over the first card to reveal "Open Script"
    actions = ActionChains(driver)
    actions.move_to_element(first_card).perform()

    # 7. Click "Open Script" button that appears
    open_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, "(//span[text()='Open Script'])[1]"))
    )
    open_button.click()

    # 8. Wait for tab list to load (using tour-id which is stable)
    tabs = WebDriverWait(driver, 10).until(
        EC.presence_of_all_elements_located((By.XPATH, "//ul[@role='tablist']//div[@tour-id]"))
    )

    # 9. Iterate through each tab
    for tab in tabs:
        tab_name = tab.get_attribute("tour-id")   # e.g. "tab-Doctor Information"
        tab.click()
        time.sleep(2)  # allow content to load

        print(f"\n===== {tab_name} =====")

        if "Doctor Information" in tab_name:
            active_panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((
                    By.XPATH,
                    "//div[@role='tabpanel' and not(contains(@style,'display: none'))]"
                ))
            )
            raw_text = active_panel.text
            print("\n===== Doctor Information (raw) =====\n", raw_text)
        
            # Structure by headings
            sections = {}
            lines = raw_text.split("\n")
            current = "Intro"
            sections[current] = ""
            for line in lines:
                if line in ["Requirements", "Finished?", "Ready to see your results?"]:
                    current = line
                    sections[current] = ""
                else:
                    sections[current] += line + " "
            print("\n===== Doctor Information (structured) =====")
            for k, v in sections.items():
                print(f"\n{k}:\n{v.strip()}")
        
        elif "Script" in tab_name:
            # Find the associated panel id from the selected tab's parent <li role="tab">
            tab_li = tab.find_element(By.XPATH, "ancestor::li[@role='tab']")
            panel_id = tab_li.get_attribute("aria-controls")
        
            # Wait for the correct tabpanel by id (e.g., react-tabs-3)
            panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
            panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, panel_xpath))
            )
        
            # Inside that panel, wait for the content container
            container = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, f"{panel_xpath}//div[@id='containerElement']"))
            )
        
            # Now extract sections: each <p id="..."> followed by its <ul>
            sections = {}
            headings = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.XPATH, f"{panel_xpath}//div[@id='containerElement']//p[@id]"))
            )
        
            for heading in headings:
                title = heading.text.strip()
                try:
                    ul = heading.find_element(By.XPATH, "following-sibling::ul[1]")
                    items = [li.text.strip() for li in ul.find_elements(By.TAG_NAME, "li")]
                    sections[title] = items
                except:
                    sections[title] = []
        
            # Print nicely (keeps your original output style)
            print("\n===== Script (structured) =====")
            for section, items in sections.items():
                print(f"\n{section}:")
                for item in items:
                    print(f" - {item}")

                
        elif "Marking Rubric" in tab_name:
            print("\n===== Marking Rubric (structured) =====")
        
            # Find associated panel
            tab_li = tab.find_element(By.XPATH, "ancestor::li[@role='tab']")
            panel_id = tab_li.get_attribute("aria-controls")
            panel_xpath = f"//div[@role='tabpanel' and @id='{panel_id}']"
            panel = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, panel_xpath))
            )
        
            def parse_accordion(root):
                """ Recursively parse accordion structure into a dict """
                data = {}
        
                # Each accordion header has a title and optional score like "0/3"
                headers = root.find_elements(By.XPATH, ".//div[contains(@class,'MuiAccordionSummary-content')]")
                for header in headers:
                    try:
                        title = header.find_element(By.XPATH, ".//div[1]").text.strip()
                    except:
                        title = ""
        
                    try:
                        score = header.find_element(By.XPATH, ".//div[contains(text(),'/')]").text.strip()
                    except:
                        score = ""
        
                    # Move up to accordion container
                    accordion = header.find_element(By.XPATH, "ancestor::div[contains(@class,'MuiAccordion-root')]")
        
                    section = {
                        "score": score,
                        "subsections": {},
                        "criteria": []
                    }
        
                    # Find direct checkboxes inside this accordion
                    checkboxes = accordion.find_elements(By.XPATH, ".//input[@type='checkbox']")
                    for cb in checkboxes:
                        try:
                            label = cb.find_element(By.XPATH, "ancestor::label").text.strip()
                        except:
                            label = "(no label)"
                        section["criteria"].append({
                            "label": label,
                            "checked": cb.is_selected()
                        })
        
                    # Recursively parse nested accordions
                    subsections = accordion.find_elements(By.XPATH, ".//div[contains(@class,'MuiAccordionDetails-root')]")
                    for sub in subsections:
                        sub_data = parse_accordion(sub)
                        section["subsections"].update(sub_data)
        
                    data[title] = section
        
                return data
        
            rubric_data = parse_accordion(panel)
        
            # Pretty printer for clarity
            def print_rubric(data, indent=0):
                for title, section in data.items():
                    print("  " * indent + f"{title} ({section['score']})")
                    for crit in section["criteria"]:
                        status = "✓" if crit["checked"] else "✗"
                        print("  " * (indent+1) + f"{status} {crit['label']}")
                    if section["subsections"]:
                        print_rubric(section["subsections"], indent+1)
        
            print_rubric(rubric_data)


finally:
    pass  # keep browser open for debugging