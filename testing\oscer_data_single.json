{"metadata": {"scraped_at": "2025-09-02T19:26:54.121540", "script_url": "https://www.oscer.ai/pwf/script/NBSOVQmYkaUA"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.", "I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out."], "Persona": ["I’m a pretty relaxed and happy person.", "I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.", "I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about."], "HOPC": ["I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.", "I can now only walk a few kilometres before getting very short of breath and needing to rest.", "Initially I was only getting short of breath when I was working hard.", "For the past 2 or 3 weeks I have gotten breathless with light exertion.", "I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.", "I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.", "Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.", "I’m otherwise feeling well.", "I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats."], "PMHx": ["I’m usually pretty healthy and I have no underlying medical conditions.", "I don’t have any allergies that I’m aware of. I don’t take any regular medications.", "I take the occasional course of antibiotics for chest infections that I get most winters.", "I can’t remember what the antibiotic I take is called.", "I haven’t had a chest infection in over 6 months."], "FMHx": ["My father has high blood pressure.", "There are no other medical conditions in my family.", "I have no siblings.", "My children are both happy and healthy."], "SHx": ["I work as a project manager and currently I'm managing the new build of the local hospital.", "I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.", "I have never worked with asbestos or any dangerous chemicals or fumes.", "I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.", "I have no pets.", "I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.", "I smoked marijuana in my late teens.", "I usually drink a beer or two most nights.", "I eat a relatively balanced diet.", "I don’t do much exercise, I prefer to watch sport on TV rather than play it.", "I wouldn’t say that I’m overweight."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 16, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 7, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 5, "items": ["Ideas", "Allergies", "Positional", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Waking up Short of Breath", "Shortness of Breath when Laying Down", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Swelling", "score": 1}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Defecating Blood", "Black and Tarry Stools"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Bronchiectasis", "Asking Generally", "Interstitial Lung Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia", "Iron Deficiency"]}]}, {"title": "Medication History", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally", "Antihypertensives"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally", "Interstitial Lung Disease"]}], "Social": [{"title": "Social History", "score": 2, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}