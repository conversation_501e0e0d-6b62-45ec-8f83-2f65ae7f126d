{"metadata": {"name": "Ben, 28M", "scraped_at": "2025-09-03T00:23:26.173362", "script_url": "https://www.oscer.ai/pwf/script/fPh2D9eQJ7yZ"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 28 year old male presenting to general practice with diarrhoea. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 28 year old male and I am an electrician.", "My pronouns are he/him."], "Persona": ["I'm normally very outgoing and carefree, however this diarrhoea is really getting in the way of me enjoying my life.", "I’m otherwise healthy."], "HOPC": ["I’ve been suffering with diarrhoea.", "I have had diarrhoea for about 3 months now, I was really hoping it would go away on its own, but it hasn’t so I have finally come into the doctor to try and get some help.", "I have at least 4 loose stools per day, it’s starting to make work very difficult.", "I can't think of anything that would have caused it.", "It has been fairly constant over the last 3 months.", "Nothing seems to make it any better or worse.", "I haven’t noticed any blood.", "I have had some lower abdominal pain too over the past 2 days. It’s more on the right side.", "The pain is crampy in nature. I would say the pain is about a 2 out of 10.", "I have lost about 6 kilograms over the past 3 months. The weight loss was unintentional."], "PMHx": ["I don't have any medical conditions, I'm usually healthy.", "I've only had to go to the hospital once when I had an incision and drainage for a perianal abscess when I was 18.", "I don’t take any prescription medications.", "I occasionally take over the counter pain relief such as panadol and Nurofen when I have a headache.", "I don’t have any allergies that I am aware of.", "I am up to date on all of my vaccinations."], "FMHx": ["My older sister has <PERSON><PERSON><PERSON>s disease.", "I can’t think of any medical conditions that run in the family.", "No one at home has had diarrhoea like mine."], "SHx": ["I live with my girlfriend in a 1 bedroom apartment, she has been perfectly healthy.", "I like the occasional cigarette.", "I only smoke on weekends, maybe half a pack a week.", "I’ve been smoking since I was 17.", "I like to go out on the weekend with the boys and have 4 or 5 beers.", "I don’t normally drink during the week, it makes it too hard to get up early for work.", "I don’t take any kind of recreational drugs.", "I haven’t travelled overseas since my trip around Europe when I was 18.", "I have a pretty balanced diet.", "I have a girlfriend, we’ve been together for the past 2 years.", "We always use a condom when we have sex."]}, "marking_rubric": {"HOPC": [{"title": "Diarr<PERSON>a", "score": 11, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally"]}, {"title": "Character", "score": 2, "items": ["Oily", "Blood", "Watery", "Ability to Flush", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Stress", "Swimming", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 7, "subcategories": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Anal Pain", "score": 1, "items": ["Anal Pain"]}, {"title": "Dizziness", "score": 3}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "<PERSON>l Discharge", "score": 1}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 3, "items": ["Faecal Urgency", "Asking Generally", "Defecating Blood", "Mucous in Stools", "Malodorous Stools", "Painful Defecation"]}]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Food Poisoning", "Gastroenteritis"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Coeliac Disease", "Asking Generally", "Irritable Bowel Syndrome", "Inflammatory Bowel Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Appendectomy"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Appendectomy"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Coeliac Disease", "Asking Generally", "Gastrointestinal Disease", "Inflammatory Bowel Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Expired Foods", "Asking Generally", "Contaminated Food or Water"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}