{"metadata": {"name": "<PERSON>, 63F", "scraped_at": "2025-09-05T12:43:36.701748", "script_url": "https://www.oscer.ai/pwf/script/vTBiHzZ4O5dP"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based at a General Practice clinic. <PERSON> is a 63 year old female presenting to general practice complaining of numbness. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>. My pronouns are she/her.", "I’m a 63 year old woman.", "I’ve come in today as my feet have been feeling a bit numb, I’m not feeling things in them quite like I used to."], "Persona": ["I'm a reserved and polite lady."], "HOPC": ["I’ve noticed some numbness in my feet recently.", "I have lost sensation to pain and touch.", "It started as tingling in my toes, before moving to numbness in my whole foot.", "I’ve had cuts on my feet that I haven’t noticed before actually looking at my feet.", "I sometimes feel pins and needles in my feet.", "This has all been going on for several years. I think it has been gradually getting worse.", "I haven’t noticed any muscle weakness in my legs, it’s more the sensation changes that I’m worried about.", "I haven’t been sick recently and have not been around anyone that has had an infection.", "I don’t have any pain or cramps in my legs.", "My symptoms don’t get worse on exertion.", "I have not had any falls.", "I have not hit my head.", "I have not lost consciousness."], "PMHx": ["I’ve had type 2 diabetes for over 25 years now.", "Unfortunately I’m still not very good at keeping my blood sugars low darling.", "I take metformin for my diabetes, they’re 500mg tablets and I take one with breakfast and one with dinner.", "The doctors are always trying to get me to improve my lifestyle, they say I need to do better with my diabetes or else I’ll have problems later on.", "I was diagnosed with high blood pressure 15 years ago, it normally hovers around 135/85 now.", "My GP was reluctant to start me on medication for my blood pressure, because he said it wasn't quite high enough for medication.", "I was also diagnosed with high cholesterol 15 years ago, but my GP said I didn't need any medication for that.", "The only time I've ever been to hospital was when I had my appendix taken out as a teenager.", "I have had all my vaccinations.", "I do not have any nutrient absorption issues that I know of."], "FMHx": ["My mother had type 2 diabetes too. Eventually she had to go on dialysis and passed away when she was 76 years old from heart failure.", "My father is 87 years old, he has high blood pressure and cholesterol but still lives independently and is doing well.", "I've got one sister, she's also has high blood pressure."], "SHx": ["I’ve always weighed too much for my size. I am 162 cm tall. I weigh 87 kilograms.", "I live at home with my partner <PERSON>.", "We have one daughter <PERSON>, she doesn’t live at home any more.", "We have a <PERSON> at home, he’s low maintenance and walks himself out in the backyard.", "I don’t work anymore, but I used to answer the telephone for a real estate agency.", "<PERSON> and I are simple eaters, we don’t eat as healthily as we should with my diabetes. We love takeaway food too, we order out a couple of times a week.", "I don’t exercise too much, but <PERSON> and I do go for a walk around the neighbourhood twice a week.", "I don’t drink alcohol.", "I do smoke, I know it’s a filthy habit but I’ve done it since I was younger and could never stop.", "I’ve smoked 10 cigarettes a day since I was in my early twenties.", "I do want to quit, especially if these problems in my feet are related to smoking, but maybe let’s talk about it another time.", "I’ve never tried recreational drugs, they’re not for me."]}, "marking_rubric": {"HOPC": [{"title": "Lower Limb Sensory Disturbance", "score": 11, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Burning", "Band-Like", "Asking Generally", "Painful or Shooting"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Asking Generally", "Location at Onset"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Feel", "Ability to Walk", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 1, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Standing", "Leg Elevation", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Ideas", "Injuries", "Bend Back", "Bend Knee", "Bend Neck", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Falls", "score": 1, "items": ["Falls"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["Feet", "Legs", "<PERSON><PERSON>"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "Foot Pain", "score": 1, "items": ["Foot Pain"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Thigh Pain", "score": 1, "items": ["Thigh Pain"]}, {"title": "Hair Changes", "score": 1, "items": ["Hair Loss"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Skin Changes", "score": 1, "items": ["<PERSON><PERSON>", "Sc<PERSON>", "<PERSON>y", "Asking Generally"]}, {"title": "Skin Lesions", "score": 1, "items": ["Asking Generally"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Increased Urination"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Constipation"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Coordination Difficulty", "score": 1, "items": ["Ataxia"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Stroke", "Transient Ischaemic Attacks"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Atherosclerosis", "Asking Generally", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Statins", "Isoniazid", "Metformin", "Asking Generally", "Past Medications", "Proton Pump Inhibitors", "Immunosuppressant Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Tests and Procedures", "score": 1, "items": ["Blood Sugar Level"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Cardiac Disease", "Asking Generally", "<PERSON><PERSON><PERSON>"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Patient Demographics", "score": 1, "items": ["Weight"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}