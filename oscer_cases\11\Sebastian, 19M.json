{"metadata": {"name": "<PERSON>, 19M", "scraped_at": "2025-09-05T12:50:08.461321", "script_url": "https://www.oscer.ai/pwf/script/IK83DPCfUeX2"}, "tabs": {"doctor_information": {"Intro": "You are a junior doctor working in a general practice clinic. <PERSON> is a 19-year-old man (he/him) presenting to the ED with a rash and you are asked to see him. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am a 19-year-old male (he/him)", "I have come to the doctor because of a rash I have been getting"], "Persona": ["I am a reserved 19-year-old", "I am not into sport, and I don’t exercise much", "I am in a band, I am the drummer", "I am worried about the rash that I have been getting"], "HOPC": ["I have noticed a rash on my arms", "The rash is on the inside of my elbows", "I first noticed the rash about 3 years ago, it comes and goes", "The rash has been a little bit worse recently", "The rash looks red and dry", "It is extremely itchy, I can’t help but scratch it", "The rash is not painful, it’s just the itch that is hard to deal with", "The itch is localised to the area of my skin that is affected by the rash", "I do not have a rash anywhere else", "Nothing seems to make the rash better", "Over the years I have noticed hot weather makes the rash worse", "More recently I have been doing a lot of band practice", "I am the drummer and I get hot and sweaty; the recent flair seems to coincide with that", "I did not injure my arms prior to the onset of the rash", "I have not been sick recently", "I feel generally well now", "My personal hygiene is good", "No one around me has had similar issues recently", "I do not have a fever", "I have not lost any weight recently", "I have not traveled recently", "I have had no changes to my bowel or urinary habits"], "PMHx": ["I am usually pretty well, I had asthma as a child and I get hay fever each year", "I have never had any surgeries", "I don’t take any medications for asthma but I do take over the counter anti-histamines for my hay fever", "As far as I know I don’t have any allergies", "I am up to date with all my vaccinations, I receive my flu shot every year"], "FMHx": ["My mum and dad are both asthmatics, my sister gets bad hay fever too. Aside from that, we are a pretty healthy family"], "SHx": ["I have just started at University, I am studying music", "I really love my degree, I am a bit stressed at the moment because my band has our final live assessment coming up", "I am doing long hours of practice as well as study at the moment", "I don’t really do any exercise aside from my drumming", "I still live with mum and dad", "Mum and dad cook all of my meals which is great, they make sure I have a healthy diet", "I have a girlfriend, her name is <PERSON> and we have been together for 4 years", "I do not smoke cigarettes, I have never smoked cigarettes", "I don’t really drink any alcohol, I might have two beers once a month if my friends go out", "I have never used recreational drugs", "I am a little bit stressed with university at the moment but I generally feel on top of things", "My sleep habits are good", "I do not have any children"]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 15, "subcategories": [{"title": "Quality", "score": 5, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Colour"]}, {"title": "Character", "score": 4, "items": ["Flat", "Pain", "Raised", "Smooth", "Bleeding", "<PERSON><PERSON><PERSON>", "Discharge", "Blistering", "Irritation", "Symmetrical", "Well-circumscribed"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Arm", "Unilateral", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Number of Rashes"]}]}, {"title": "Temporal", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Cold Exposure", "Asking Generally", "Topical Steroids"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Injuries", "Heat Exposure", "Skin Products", "Life Stressors", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["Asking Generally"]}, {"title": "Plaques", "score": 1, "items": ["Plaques"]}, {"title": "Swelling", "score": 1, "items": ["Face", "Mouth", "Asking Generally"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Skin Infections", "score": 1, "items": ["Skin Infections"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 2, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asthma"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Atopic Dermatitis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Medications", "Asking Generally", "Past Medications", "Topical Steroids"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Adherence", "Past Medications", "Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["<PERSON><PERSON>", "Asthma", "<PERSON><PERSON><PERSON>", "Hayfever", "Allergies", "Asking Generally", "Dermatological Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Diet at Baseline"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Passive Exposure", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Environmental Exposure", "score": 1, "items": ["Animals"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}]}}}