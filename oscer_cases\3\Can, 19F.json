{"metadata": {"name": "Can, 19F", "scraped_at": "2025-09-03T00:10:07.839129", "script_url": "https://www.oscer.ai/pwf/script/m4znabmZIiTX"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 19 year old female presenting with a rash. Please take a history with the aim of establishing a diagnosis. You are NOT required to perform an examination.", "Requirements": "Please interview <PERSON> <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m a 19 year old female. My pronouns are she/her.", "I’m a university student and I work part-time at the supermarket.", "I've come in to see the doctor because I’ve recently gotten a rash that’s not going away."], "Persona": ["The rash is driving me absolutely insane!", "I think most of my friends would say I'm quite an anxious person."], "HOPC": ["The rash started on my arms but now it’s on my chest and on my back.", "The rash came on pretty quickly, it started as a slight itch on my arms at first and an hour later it was all over my back and chest as well.", "The rash is on both arms.", "The rash seems to be just on the upper part of my body.", "I've had a rash for about 1 week now. It begins to fade but never fully goes away and then starts up again full blast.", "The rash did start pretty soon after I used the traditional medicine Mum gave me about a week ago", "It’s extremely itchy! I can barely sleep due to this rash!", "The rash feels quite warm.", "Each of the lumps are red and raised, but the surrounding skin looks normal.", "The lumps are asymmetrical, I guess. It all seems a bit random.", "The lumps are about 3-6cm large and they cover everything!", "The lumps aren’t painful, flakey or crusty, and there’s no pus.", "I tried an antihistamine, which did help the rash a bit, but it made me drowsy so I didn't take any more.", "I haven't really done much for it but none of the skin creams I usually use have made it any better.", "I haven't been bitten by anything, I haven’t noticed bite marks anywhere.", "Nothing in my diet has changed.", "I haven’t been wearing any new jewellery.", "I don’t think I’ve been exposed to or touched anything unusual.", "I don't feel short of breath.", "I haven’t had any throat swelling.", "I haven’t felt nauseous."], "PMHx": ["I have a Ventolin puffer and steroid cream.", "I very occasionally use the ventolin puffer and steroid cream when I have an asthma or eczema flare up.", "I have also recently been taking some Chinese medicine.", "I've never had an allergic reaction before.", "I should be up to date with all my vaccines.", "I’ve never been hospitalised or had a significant illness before."], "FMHx": ["My Dad has a really serious allergy to peanuts.", "My Mum had childhood asthma."], "SHx": ["I just have the stock standard diet that every child in a Chinese family does. Nothings changed recently.", "I usually run 5 times a week but haven't done that this week due to the rash.", "I travelled to visit family in China around 2 years ago.", "I only started drinking a year ago, but I rarely drink. Maybe only once a month.", "I would never smoke. I hate the smell.", "I’ve never used drugs.", "I'm at University doing a Bachelor of Commerce.", "I live at home with my parents.", "I work part-time at the supermarket."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Colour"]}, {"title": "Character", "score": 2, "items": ["Pain", "Smooth", "Bleeding", "Irregular", "Blistering", "Symmetrical", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Side", "Unilateral", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Number of Rashes"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Medications", "Topical Creams", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Eating", "Medications", "Sun exposure", "Skin Products", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Hot", "score": 1, "items": ["Hot"]}, {"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Swelling", "score": 1, "items": ["Face", "Throat"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Facial Flushing", "score": 1}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Anxiousness", "Irritability"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Asking Generally", "Past Medications", "Topical Steroids"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Moisturiser", "Non-Prescription Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Anaphylaxis History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Past Medical History", "score": 1, "items": ["Previous Hospitalisations"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Previous Hospitalisations"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Asthma", "Hayfever", "Allergies", "Asking Generally", "Autoimmune Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Diet at Baseline", "Most Recent Meal"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}