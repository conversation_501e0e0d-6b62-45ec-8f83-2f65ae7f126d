{"metadata": {"name": "Mark, 49M", "scraped_at": "2025-09-02T23:58:53.724255", "script_url": "https://www.oscer.ai/pwf/script/7S3bAGHZEzPM"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 49 year old man who has presented to the emergency department with haemoptysis. Please take a focused history from him with the aims of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I'm a 49 year old man.", "My pronouns are he/him.", "I've been coughing up some blood recently. Not gonna lie, I'm actually scared it might be something serious."], "Persona": ["I'm not a very cautious person.", "Recently I've gone through a bit of bend. I have trouble controlling my lifestyle."], "HOPC": ["I've been seeing some blood in my phlegm.", "The blood is coming from my lungs, not my throat.", "The blood is dark red.", "There are only streaks of blood in my phlegm. So it's not a huge amount.", "I bring up phlegm frequently. Almost with every single cough.", "The phlegm itself looks grayish red.", "I've been experiencing the bloody phlegm for the past 3 days. Previously, there was phlegm but not blood in it.", "My symptoms are getting worse each day.", "I've had the cough symptom for the past 2 weeks.", "Nothing helps with the phlegm.", "I cough a lot of red blood in the morning.", "3 weeks ago I had a really crappy day. I've only recently begun to do this but I drank way too much and passed out. I woke up in the morning with vomit around me and felt I was choking. I'm glad I survived. My symptoms started a few days after that incident.", "I feel feverish. I haven't experienced any uncontrollable shaking.", "I have some breathlessness.", "I've never had breathlessness before.", "I have experienced some weightloss recently but that's mainly because I don't have much money to buy food. You see, I lost all of my money. I gambled it all away 3 weeks ago.", "I haven't experienced any nightsweats.", "I don't have any chest pain.", "I haven't been immobile.", "I haven't had any recent fractures.", "I haven't been in any TB endemic areas.", "I haven't had contact with anyone who has respiratory symptoms like mine.", "I don't have any wheezes."], "PMHx": ["I have type 2 diabetes. It got diagnosed 1 year ago. I have to manage with metformin and diet, which due to my state I do not.", "I haven't had a pneumonia before.", "I don't have any clotting disorders.", "I don't have a history of cancer.", "I'm fully vaccinated against COVID19 and other illnesses too.", "I don't have any allergies."], "FMHx": ["My mama died when I was young. She got hit by a car. My dad left me and so I was raised by my gran.", "I have an older sister whom I live with at the moment."], "SHx": ["I don't smoke.", "I had a binge drinking session 3 weeks ago. I think I drank so much I passed out and collapsed. I'm usually not this kind of person.", "Normally, I used to have 2 or 3 beers in the company of friends.", "I've never used recreational drugs.", "My mood is fine.", "I'm suffering majorly financially. I used to be the talk of town. The \"Wolf of Wallstreet\" but now I am a man with -$120,000 in my account. I don't know how I'm gonna recover from this."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "score": 11, "subcategories": [{"title": "Quality", "score": 3, "subcategories": [{"title": "Sputum Amount", "score": 1, "items": ["Asking Generally"]}, {"title": "Sputum Colour", "score": 1, "items": ["Red", "Dark Red"]}, {"title": "Blood Character", "score": 1, "items": ["Quality", "Consistency"]}, {"title": "Sputum Character", "score": 1, "items": ["Blood", "Odour", "Frothy", "Consistency"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Blood Amount", "score": 1, "items": ["Asking Generally", "Cups or Teaspoons", "Presence of Clots"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Onset", "score": 1, "items": ["<PERSON><PERSON>", "Gradual"]}, {"title": "Frequency", "score": 1, "items": ["Frequency"]}, {"title": "Time Course", "score": 2, "items": ["Offset", "Improving", "Worsening", "First Noticed", "Most Recent Haemoptysis"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Patient Ideas", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Face", "Legs"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Bone Pain", "score": 1, "items": ["Bone Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Malaise", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Drooping Eyelids", "score": 1, "items": ["Drooping Eyelids"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Poor Sleep", "Memory Loss", "Depression or Low Mood"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Steroids", "Antibiotics", "Asking Generally", "Past Medications", "Antituberculosis Medications", "Immunosuppressant Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Factor V Leiden", "Pulmonary Embolism", "Deep Vein Thrombosis"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Tuberculosis Vaccination", "Current Vaccination Status"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["COPD", "Cancer", "Lung Cancer", "Asking Generally", "Pulmonary Embolism", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Financial Stability", "Mental Health at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Been to Asia", "Been to China", "Recent Travel", "Been to Africa"]}, {"title": "Alcohol History", "score": 2, "items": ["<PERSON>e Drinking", "Drink of Choice", "Drinking Status", "Amount Currently", "Amount at Baseline", "Reason for Drinking", "AUDIT-C Binge Screening"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally", "Contact with Covid-19 Cases"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": []}}}