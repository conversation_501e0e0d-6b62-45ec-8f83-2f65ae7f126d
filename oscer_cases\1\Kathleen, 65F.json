{"metadata": {"name": "<PERSON>, 65F", "scraped_at": "2025-09-02T23:56:30.722899", "script_url": "https://www.oscer.ai/pwf/script/oWvJtRunKi2S"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement in a GP clinic. <PERSON> is a 65 year-old female who has presented with leg swelling. Please take a detailed history and determine the most likely diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I'm a 65 year old female.", "My preferred pronouns are she/her.", "I've come to the GP clinic because I've noticed my legs are quite swollen."], "Persona": ["I'm a happy-go lucky, newly retired lady!", "I'm not great at taking care of my health, and generally just go with the flow.", "My knowledge about health isn't great..."], "HOPC": ["I've noticed my legs are more swollen than usual...", "It's not just one leg, it's both! They look horrible!", "I think this began about two weeks ago - i'm not exactly sure when... I often don't notice these things.", "It started with just my feet, then the swelling just got higher up my legs. I think there's swelling up to my knees now.", "My socks leave an imprint on my legs at the end of the day now, that never used to happen.", "I noticed the swelling is worse at the end of the day.", "Sleeping, or keeping my legs up helps a the swelling a bit... but it hasn't gone away fully on it's own.", "Being on my feet all day makes the swelling worse.", "I've also been feeling short of breath, this started around the same time.", "I sometimes get short of breath when i'm laying down, and find I have had to add another pillow! I now sleep on 3 pillows.", "I've even woken up a few times in the night this week, gasping for air, and had to sit up in bed.", "I've also been feeling quite tired, I guess this has been happening for the last month or so. I haven't changed my lifestyle at all.", "I've never been short of breath. had swelling in my legs, or been this tired for no reason before.", "I have not had any trauma", "I haven't had any chest pain, palpitations, sweating or vomiting.", "I haven't had any facial swelling or changes in my urinary habits."], "PMHx": ["I've had one heart attack just under a year ago. That was scary.", "I have been diagnosed with high blood pressure and high cholesterol.", "I'm a bit overweight... I'm about 69kg and 160cm tall.", "I take perindopril and atorvastatin, that's it.", "I take panadol occasionally for headaches but other than that don't take anything over the counter or supplements.", "I don't have any allergies.", "My vaccinations are up to date."], "FMHx": ["My parents health was fine.", "<PERSON> lived to 80, she didn't have any conditions, neither did <PERSON>, although he only lived until he was 75.", "They both died of natural causes - or at least that's what the doctors said!"], "SHx": ["I live at home with my wife <PERSON>.", "We have been together for 40 years now! We only got married a couple of years ago.", "We don't have any children together - unless you count our minature daschund, <PERSON>!", "I've just retired, but was previously working as a receptionist for a dentist. I worked there for 20 odd years.", "My job is pretty sedentary, and I don't exercise much except for short walks with <PERSON>.", "My eating could be improved. I try to eat balanced meals but definitely eat too many carbs and sweets. <PERSON><PERSON> is my absolute favourite.", "I've never smoked before.", "I drink a glass of red wine every few days.", "I don't take any recreational drugs. I steer clear of that sort of stuff!"]}, "marking_rubric": {"HOPC": [{"title": "Leg <PERSON>welling", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Unilateral", "Asking Generally", "Level up the Leg"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Amount of Swelling Currently", "Amount of Swelling at Baseline"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Exercise", "Asking Generally", "Compression Support"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Travel", "Morning", "Surgery", "Injuries", "Night-time", "Laying Down", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 7, "subcategories": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Skin"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Gain", "score": 1, "items": ["Weight Gain"]}, {"title": "Hand Redness", "score": 1, "items": ["Hand Redness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Skin Changes", "score": 1, "items": ["Dry", "<PERSON>", "<PERSON>y", "<PERSON><PERSON><PERSON>", "Asking Generally"]}, {"title": "Skin Lesions", "score": 1, "items": ["Asking Generally"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Frothy Urine", "Asking Generally", "Urinating at Night", "Increased Urination"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Black and Tarry Stools"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}]}], "PMHx": [{"title": "Medications", "score": 3, "subcategories": [{"title": "Prescription Medications", "score": 2, "items": ["Duration", "Adherence", "Cessation", "Regularity", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Adherence", "Past Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Cancer Screening", "Liver Function Tests", "Full Blood Examination"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Bowel Cancer", "Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Parasitic Infection", "Previous Hospitalisations"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Pulmonary Embolism"]}, {"title": "Thyroid Disease History", "score": 1, "items": ["Thyroid Disease"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asking Generally", "Pulmonary Hypertension"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Asking Generally", "Chronic Kidney Disease"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Liver Cancer", "Asking Generally", "Fatty Liver Disease"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Heart Failure", "Asking Generally", "Valvular Heart Disease", "Coronary Artery Disease", "Congenital Heart Disease"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally", "Lymph Node Biopsy"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally", "Lymph Node Biopsy"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Heart Failure", "Kidney Disease", "Asking Generally", "Pulmonary Hypertension"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Been to Asia", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Functional History", "score": 1, "subcategories": [{"title": "Mobility", "score": 1, "items": ["Walking", "Mobility"]}, {"title": "Domestic Activities Of Daily Living", "score": 1, "items": ["Cooking", "Domestic Activities"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Dressing", "Asking Generally", "Personal Hygiene"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion", "Patient Questions"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}