{"metadata": {"name": "Don, 66M", "scraped_at": "2025-09-05T12:59:13.952645", "script_url": "https://www.oscer.ai/pwf/script/lhidjogF5go1"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 66 year old man (he/him) who has presented with a rash. You are a medical student at a GP clinic. Please take a focused history from <PERSON> with the aims of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I'm 66 years old.", "I identity as a male so you can use he/him pronouns.", "I've come in because recently I've gotten a rash."], "Persona": ["I'm an easy going person.", "Although I care about my wellbeing I have trouble navigating healthcare."], "HOPC": ["I've gotten this rash recently.", "The rash is localised to an area just below my chest.", "The rash is only on one side; that is my left side.", "The rash is painful and itchy.", "I felt pain over the area first and then the rash came.", "The area affected is red. There are some blisters in the area as well.", "There is no bleeding or ulcers in the area.", "I noticed the rash 3 days ago and thought it would settle.", "About 5 days, the area with the rash was painful. There was no rash then.", "I've never had anything like this.", "I feel a bit sick. It's kind of a malaise.", "I haven't had contact with any atopic or erosive substances.", "I haven't been too stressed lately.", "I don't feel feverish.", "I don't have any joint aches.", "I don't have any swelling anywhere.", "I haven't been stung or bitten.", "I haven't experienced any hairloss.", "I haven't had any trauma to the area."], "PMHx": ["I haven't been to the doc in years. So far I've been all good.", "I'm not on any medications.", "I had chicken pox when I was a kid. I wasn't treated for it. It resolved on its own."], "FMHx": ["My parents have had some heart issues. Otherwise they're well. My children are also doing well."], "SHx": ["I don't smoke.", "I drink a few pints of beer with mates on the weekend.", "I'm sexually active with my wife.", "My diet is pretty good.", "I don't know about my vaccinations. I got them when I was a child.", "I'm afraid to get the COVID vaccine.", "I'm a carpenter.", "I'm not too stressed."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 13, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red", "Pink", "Colour", "Purple"]}, {"title": "Character", "score": 2, "items": ["Hot", "Flat", "Itch", "Sc<PERSON>", "<PERSON><PERSON><PERSON>", "Raised", "Bleeding", "Irregular", "Asymmetrical", "Asking Generally", "Well-circumscribed"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 2, "items": ["Face", "Unilateral", "Asking Generally", "Location at Onset"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Size", "Number of Rashes"]}]}, {"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Allergies", "Medications", "Cold Exposure", "Heat Exposure", "Patient Ideas", "Skin Products", "Life Stressors", "Topical Creams", "Context at Onset", "Lifestyle Changes", "Infectious Contacts"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["Asking Generally"]}, {"title": "Fatigue", "score": 1}, {"title": "Malaise", "score": 1, "items": ["Malaise"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Joint Pain", "score": 1, "items": ["Joint Pain"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Skin Lesions", "score": 1, "items": ["Groin Lesions", "Asking Generally"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Skin Infections", "score": 1, "items": ["Skin Infections"]}]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["HIV", "Infections", "Asking Generally"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Chemotherapy", "Asking Generally", "Immunosuppressant Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Allergies"]}, {"title": "Tests and Procedures", "score": 1, "items": ["STI Testing", "Chemotherapy"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Allergies", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally", "Autoimmune Disease", "Dermatological Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Asking Generally", "Herpes Simplex Virus"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Manual Labourer", "Previous Occupation", "Personal Protective Equipment Use"]}, {"title": "Living Situation", "score": 1, "items": ["Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}]}}}