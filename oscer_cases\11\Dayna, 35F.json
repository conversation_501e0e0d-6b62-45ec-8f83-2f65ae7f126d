{"metadata": {"name": "<PERSON>na, 35F", "scraped_at": "2025-09-05T12:57:07.162343", "script_url": "https://www.oscer.ai/pwf/script/2JIPyBNhXr1v"}, "tabs": {"doctor_information": {"Intro": "You are a medical student at a rural general practice. <PERSON><PERSON> is a 35 year old woman (she/her) presenting with palpitations. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON> and I am 35 years old.", "I've come in to see the doctor today because I've really been feeling my heart beating fast lately."], "Persona": ["I've been feeling really on edge recently.", "I'm worried and concerned there could be a problem with my heart."], "HOPC": ["I've been having palpitations for the past month.", "It just feels like my heart is beating really fast from inside my chest, it's almost like I can feel it fluttering.", "The palpitations come on suddenly, and then end suddenly.", "I experience an episode of palpitations once a day.", "Each episode of the palpitations last for a few minutes I suppose, but it can vary.", "I'm not doing anything in particular when the palpitations start.", "The palpitations come and go, they are not there all the time.", "I haven't noticed anything that makes the palpitations better or worse.", "I've also noticed that I've got a smooth neck lump, I noticed it a couple of weeks ago when I was moisturising so I'm not sure how long it has been there for.", "The neck lump seems to be all around the voice box region on both sides.", "The lump on my neck isn't painful.", "I've also had some weight loss.", "I have lost 4 kilograms over the past two months without even trying.", "I've really been struggling with the temperature lately too. I have to sleep with a sheet, I can't share the doona with my partner because it feels like I'm too hot.", "I haven't noticed any double or blurred vision.", "I haven't experienced any changes to my menstrual cycle, they've still been regular.", "I haven't felt feverish at all.", "I haven't had any muscle or joint pains.", "I haven't notoiced that I've had a tremor.", "I haven't had any recent illnesses or been around someone who's sick since I've been experiencing these symptoms.", "I haven't had any changes to my bowel habits, they've been normal.", "I haven't had any chest pain, shortness of breath or dizziness.", "I haven't had any night sweats or loss of appetite.", "I haven't had any voice hoarseness or difficulty swallowing."], "PMHx": ["I've got type 1 diabetes, it was diagnosed when I was 14 years old.", "I take insulin through an insulin pump for my diabetes.", "The only time I've been to hospital is when my diabetes was first diagnosed.", "I've never had any surgeries.", "I don't have any allergies.", "My vaccinations are all up to date."], "FMHx": ["My Mother has rheumatoid arthritis, it was diagnosed when she was 48 years old I think. My <PERSON> and my younger sister are both healthy and well with no medical conditions!", "There is no family history of cancer or heart disease."], "SHx": ["I live with my partner.", "We don't have any children yet, although we'd like to in the next few years.", "I work as a researcher in social sciences.", "I'm not a smoker, and never have been before.", "I do drink alcohol, I think I'd average 5 glasses of red wine per week.", "I've never used any recreational drugs before.", "My diet is normal, that's why I found it strange that I've lost weight. I eat muesli for breakfast, a sandwich for lunch, and then protein and vegetables for dinner.", "I do 2 or 3 classes of pilates per week for exercise.", "I haven't had any recent travel."]}, "marking_rubric": {"HOPC": [{"title": "Palpitations", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Rate", "Fluttering", "Regularity", "Asking Generally"]}]}, {"title": "Location", "score": 1, "subcategories": [{"title": "Location", "score": 1, "items": ["Location", "Radiation", "Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Hospitalisation", "Asking Generally", "Affecting Daily Life"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally", "Most Recent Episode"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Stress", "Smoking", "Caffeine", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Tremor", "score": 1, "items": ["Tremor"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Dry Eyes", "score": 1, "items": ["Dry Eyes"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Legs"]}, {"title": "Neck Pain", "score": 1, "items": ["Neck Pain"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Neck Lumps", "score": 1, "items": ["Neck Lump"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Bulging Eyes", "score": 1, "items": ["Bulging Eyes", "Asking Generally"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Painful Swallowing", "score": 1, "items": ["Painful Swallowing"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Asking Generally"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}], "PMHx": [{"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "Menstrual History", "score": 1, "subcategories": [{"title": "Blood Amount", "score": 1, "items": ["Asking Generally", "Presence of Clots"]}, {"title": "Menstrual Cycle", "score": 1, "items": ["Asking Generally", "Length between Bleeding at Baseline"]}]}, {"title": "Past Medical History", "score": 1, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}]}, {"title": "Past Medical History (2)", "score": 1, "items": ["Recent Illness", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Thyroid Cancer", "Thyroid Disease", "Asking Generally", "Autoimmune Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Diet at Baseline"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}