{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9a8affee-648b-467f-b7cb-658414bb52e5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Mark<PERSON> (Raw) =====\n", "[{'title': 'Shortness Of Breath', 'items': ['Asking Generally', 'Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity', 'Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally', 'Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Quality', 'items': ['Asking Generally'], 'subcategories': [{'title': 'Character', 'items': ['Asking Generally']}]}, {'title': 'Character', 'items': ['Asking Generally']}, {'title': 'Severity', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity'], 'subcategories': [{'title': 'Qualitative', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Qualitative', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'items': ['Scale out of 10', 'Exercise Capacity']}, {'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally'], 'subcategories': [{'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'items': ['Asking Generally']}]}, {'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'items': ['Asking Generally']}, {'title': 'Contributing Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Relieving Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}, {'title': 'Relieving Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath\n", "    - Quality\n", "        - Character\n", "                Asking Generally\n", "            Asking Generally\n", "    - Character\n", "            Asking Generally\n", "    - Severity\n", "        - Qualitative\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative\n", "                Scale out of 10\n", "                Exercise Capacity\n", "            Ability to Sleep\n", "            Asking Generally\n", "            Affecting Daily Life\n", "            Scale out of 10\n", "            Exercise Capacity\n", "    - Qualitative\n", "            Ability to Sleep\n", "            Asking Generally\n", "            Affecting Daily Life\n", "    - Quantitative\n", "            Scale out of 10\n", "            Exercise Capacity\n", "    - Time Course\n", "        - Time Course\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes\n", "                Asking Generally\n", "            Onset\n", "            Offset\n", "            Duration\n", "            Change Over Time\n", "            Onset of Worsening\n", "            Frequency of Episodes\n", "            Asking Generally\n", "    - Time Course\n", "            Onset\n", "            Offset\n", "            Duration\n", "            Change Over Time\n", "            Onset of Worsening\n", "            Frequency of Episodes\n", "    - Previous Episodes\n", "            Asking Generally\n", "    - Contributing Factors\n", "        - Relieving Factors\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "            Resting\n", "            Positional\n", "            Medications\n", "            Asking Generally\n", "            Ideas\n", "            Allergies\n", "            Positional\n", "            Context at Onset\n", "            Lifestyle Changes\n", "            Physical Activity\n", "            Infectious Contacts\n", "            Waking up Short of Breath\n", "            Shortness of Breath when Laying Down\n", "            Asking Generally about Aggravating Factors\n", "    - Relieving Factors\n", "            Resting\n", "            Positional\n", "            Medications\n", "            Asking Generally\n", "    - Context and Aggravating Factors\n", "            Ideas\n", "            Allergies\n", "            Positional\n", "            Context at Onset\n", "            Lifestyle Changes\n", "            Physical Activity\n", "            Infectious Contacts\n", "            Waking up Short of Breath\n", "            Shortness of Breath when Laying Down\n", "            Asking Generally about Aggravating Factors\n", "        Asking Generally\n", "        Ability to Sleep\n", "        Asking Generally\n", "        Affecting Daily Life\n", "        Scale out of 10\n", "        Exercise Capacity\n", "        Onset\n", "        Offset\n", "        Duration\n", "        Change Over Time\n", "        Onset of Worsening\n", "        Frequency of Episodes\n", "        Asking Generally\n", "        Resting\n", "        Positional\n", "        Medications\n", "        Asking Generally\n", "        Ideas\n", "        Allergies\n", "        Positional\n", "        Context at Onset\n", "        Lifestyle Changes\n", "        Physical Activity\n", "        Infectious Contacts\n", "        Waking up Short of Breath\n", "        Shortness of Breath when Laying Down\n", "        Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "                        # Extract title and score from summary text\n", "                        summary_text = summary.text.strip()\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # First non-score line is usually the title\n", "                        title = \"\"\n", "                        score = None\n", "                        for line in lines:\n", "                            if not re.match(r'^\\d+/\\d+$', line):\n", "                                if not title:  # Take first non-score line as title\n", "                                    title = line\n", "                            else:\n", "                                score = extract_score_from_text(line)\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "                    except:\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                top_accordions = panel_element.find_elements(\n", "                    By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                # Filter to get only the main categories (not nested ones)\n", "                main_categories = []\n", "                for accordion in top_accordions:\n", "                    try:\n", "                        # Check if this accordion has a parent accordion - if so, skip it\n", "                        parent_accordion = accordion.find_element(\n", "                            By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                        # If we found a parent accordion, this is nested, so skip\n", "                        continue\n", "                    except:\n", "                        # No parent accordion found, this is a top-level one\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            main_categories.append(parsed)\n", "\n", "                return main_categories\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "            print(\"\\n===== Marking Rubric (Raw) =====\")\n", "            print(rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            formatted_output = format_rubric_display(rubric_data)\n", "            for line in formatted_output:\n", "                print(line)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "d5b89826-2892-4218-ac23-06f4a1043344", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Mark<PERSON> (Raw) =====\n", "[{'title': 'Shortness Of Breath', 'items': ['Asking Generally', 'Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity', 'Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally', 'Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Quality', 'items': ['Asking Generally'], 'subcategories': [{'title': 'Character', 'items': ['Asking Generally']}]}, {'title': 'Character', 'items': ['Asking Generally']}, {'title': 'Severity', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity'], 'subcategories': [{'title': 'Qualitative', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Qualitative', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'items': ['Scale out of 10', 'Exercise Capacity']}, {'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally'], 'subcategories': [{'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'items': ['Asking Generally']}]}, {'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'items': ['Asking Generally']}, {'title': 'Contributing Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Relieving Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}, {'title': 'Relieving Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]\n", "\n", "===== Mark<PERSON> <PERSON><PERSON><PERSON> (Cleaned) =====\n", "[{'title': 'Shortness Of Breath', 'subcategories': [{'title': 'Quality', 'subcategories': [{'title': 'Character', 'items': ['Asking Generally']}]}, {'title': 'Severity', 'subcategories': [{'title': 'Qualitative', 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Time Course', 'subcategories': [{'title': 'Time Course', 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'items': ['Asking Generally']}]}, {'title': 'Contributing Factors', 'subcategories': [{'title': 'Relieving Factors', 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]}]\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath\n", "    - Quality\n", "        - Character\n", "                Asking Generally\n", "    - Severity\n", "        - Qualitative\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course\n", "        - Time Course\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes\n", "                Asking Generally\n", "    - Contributing Factors\n", "        - Relieving Factors\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "                        # Extract title and score from summary text\n", "                        summary_text = summary.text.strip()\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # First non-score line is usually the title\n", "                        title = \"\"\n", "                        score = None\n", "                        for line in lines:\n", "                            if not re.match(r'^\\d+/\\d+$', line):\n", "                                if not title:  # Take first non-score line as title\n", "                                    title = line\n", "                            else:\n", "                                score = extract_score_from_text(line)\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "                    except:\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                top_accordions = panel_element.find_elements(\n", "                    By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                # Filter to get only the main categories (not nested ones)\n", "                main_categories = []\n", "                for accordion in top_accordions:\n", "                    try:\n", "                        # Check if this accordion has a parent accordion - if so, skip it\n", "                        parent_accordion = accordion.find_element(\n", "                            By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                        # If we found a parent accordion, this is nested, so skip\n", "                        continue\n", "                    except:\n", "                        # No parent accordion found, this is a top-level one\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            main_categories.append(parsed)\n", "\n", "                return main_categories\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Deduplicate the data to remove hierarchy conflicts\n", "            def deduplicate_rubric_data(categories):\n", "                \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "                def collect_all_subcategory_titles(cats):\n", "                    \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "                    titles = set()\n", "                    for cat in cats:\n", "                        if 'subcategories' in cat:\n", "                            for subcat in cat['subcategories']:\n", "                                titles.add(subcat['title'])\n", "                                # Recursively collect from deeper levels\n", "                                titles.update(\n", "                                    collect_all_subcategory_titles([subcat]))\n", "                    return titles\n", "\n", "                def clean_category(category, all_subcat_titles):\n", "                    \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "                    cleaned = {\n", "                        'title': category['title']\n", "                    }\n", "\n", "                    # Add score if present\n", "                    if 'score' in category:\n", "                        cleaned['score'] = category['score']\n", "\n", "                    # Process subcategories first (recursively)\n", "                    if 'subcategories' in category:\n", "                        cleaned_subcats = []\n", "\n", "                        # Collect titles of subcategories that have their own subcategories\n", "                        # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "                        parent_subcats = set()\n", "                        for subcat in category['subcategories']:\n", "                            if 'subcategories' in subcat:\n", "                                for nested in subcat['subcategories']:\n", "                                    # Only add if it's not a self-reference\n", "                                    if nested['title'] != subcat['title']:\n", "                                        parent_subcats.add(nested['title'])\n", "\n", "                        # First, identify the best version of each duplicate title\n", "                        title_to_best_subcat = {}\n", "                        for subcat in category['subcategories']:\n", "                            title = subcat['title']\n", "                            if title not in title_to_best_subcat:\n", "                                title_to_best_subcat[title] = subcat\n", "                            else:\n", "                                # Compare with existing: prefer the one with subcategories\n", "                                existing = title_to_best_subcat[title]\n", "                                current_has_subcats = 'subcategories' in subcat\n", "                                existing_has_subcats = 'subcategories' in existing\n", "\n", "                                if current_has_subcats and not existing_has_subcats:\n", "                                    # Current is better (has subcategories)\n", "                                    title_to_best_subcat[title] = subcat\n", "                                elif current_has_subcats and existing_has_subcats:\n", "                                    # Both have subcategories, prefer the one with more subcategories\n", "                                    if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                                        title_to_best_subcat[title] = subcat\n", "\n", "                        # Now process the best version of each subcategory\n", "                        for title, best_subcat in title_to_best_subcat.items():\n", "                            # Skip if this title is a subcategory of another subcategory at this level\n", "                            if title not in parent_subcats:\n", "                                cleaned_subcat = clean_category(\n", "                                    best_subcat, all_subcat_titles)\n", "                                cleaned_subcats.append(cleaned_subcat)\n", "\n", "                        if cleaned_subcats:\n", "                            cleaned['subcategories'] = cleaned_subcats\n", "\n", "                    # Process items - only include items that are NOT subcategory titles\n", "                    if 'items' in category:\n", "                        # If this category has subcategories, don't include items that belong to subcategories\n", "                        if 'subcategories' in cleaned:\n", "                            # Collect all items that belong to subcategories\n", "                            subcat_items = set()\n", "                            for subcat in cleaned['subcategories']:\n", "                                if 'items' in subcat:\n", "                                    subcat_items.update(subcat['items'])\n", "                                # Also collect items from deeper subcategories\n", "\n", "                                def collect_deep_items(cat):\n", "                                    items = set()\n", "                                    if 'items' in cat:\n", "                                        items.update(cat['items'])\n", "                                    if 'subcategories' in cat:\n", "                                        for sc in cat['subcategories']:\n", "                                            items.update(\n", "                                                collect_deep_items(sc))\n", "                                    return items\n", "                                subcat_items.update(collect_deep_items(subcat))\n", "\n", "                            # Only keep items that don't belong to any subcategory\n", "                            filtered_items = []\n", "                            for item in category['items']:\n", "                                if item not in subcat_items and item not in all_subcat_titles:\n", "                                    filtered_items.append(item)\n", "\n", "                            if filtered_items:\n", "                                cleaned['items'] = filtered_items\n", "                        else:\n", "                            # No subcategories, keep all items\n", "                            cleaned['items'] = category['items']\n", "\n", "                    return cleaned\n", "\n", "                # First pass: collect all subcategory titles\n", "                all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "                # Second pass: clean each category and remove duplicates\n", "                cleaned_categories = []\n", "                processed_titles = set()\n", "\n", "                for category in categories:\n", "                    title = category['title']\n", "                    # Only process if this title hasn't been seen as a subcategory\n", "                    if title not in all_subcat_titles and title not in processed_titles:\n", "                        cleaned = clean_category(category, all_subcat_titles)\n", "                        cleaned_categories.append(cleaned)\n", "                        processed_titles.add(title)\n", "\n", "                return cleaned_categories\n", "\n", "            # Clean the data\n", "            clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "            print(\"\\n===== Marking Rubric (Raw) =====\")\n", "            print(rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (Cleaned) =====\")\n", "            print(clean_rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            formatted_output = format_rubric_display(clean_rubric_data)\n", "            for line in formatted_output:\n", "                print(line)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "3f13ac05-a0ba-42ed-9c70-e20f5eb9cfbe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Mark<PERSON> (Raw) =====\n", "[{'title': 'Shortness Of Breath', 'score': {'current': 0, 'total': 16}, 'items': ['Asking Generally', 'Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity', 'Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally', 'Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Quality', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally'], 'subcategories': [{'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}, {'title': 'Severity', 'score': {'current': 0, 'total': 3}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life', 'Scale out of 10', 'Exercise Capacity'], 'subcategories': [{'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}, {'title': 'Time Course', 'score': {'current': 0, 'total': 5}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes', 'Asking Generally'], 'subcategories': [{'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}, {'title': 'Contributing Factors', 'score': {'current': 0, 'total': 7}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally', 'Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors'], 'subcategories': [{'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}, {'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]\n", "\n", "===== Mark<PERSON> <PERSON><PERSON><PERSON> (Cleaned) =====\n", "[{'title': 'Shortness Of Breath', 'score': {'current': 0, 'total': 16}, 'subcategories': [{'title': 'Quality', 'score': {'current': 0, 'total': 1}, 'subcategories': [{'title': 'Character', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Severity', 'score': {'current': 0, 'total': 3}, 'subcategories': [{'title': 'Qualitative', 'score': {'current': 0, 'total': 2}, 'items': ['Ability to Sleep', 'Asking Generally', 'Affecting Daily Life']}, {'title': 'Quantitative', 'score': {'current': 0, 'total': 1}, 'items': ['Scale out of 10', 'Exercise Capacity']}]}, {'title': 'Time Course', 'score': {'current': 0, 'total': 5}, 'subcategories': [{'title': 'Time Course', 'score': {'current': 0, 'total': 4}, 'items': ['Onset', 'Offset', 'Duration', 'Change Over Time', 'Onset of Worsening', 'Frequency of Episodes']}, {'title': 'Previous Episodes', 'score': {'current': 0, 'total': 1}, 'items': ['Asking Generally']}]}, {'title': 'Contributing Factors', 'score': {'current': 0, 'total': 7}, 'subcategories': [{'title': 'Relieving Factors', 'score': {'current': 0, 'total': 2}, 'items': ['Resting', 'Positional', 'Medications', 'Asking Generally']}, {'title': 'Context and Aggravating Factors', 'score': {'current': 0, 'total': 5}, 'items': ['Ideas', 'Allergies', 'Positional', 'Context at Onset', 'Lifestyle Changes', 'Physical Activity', 'Infectious Contacts', 'Waking up Short of Breath', 'Shortness of Breath when Laying Down', 'Asking Generally about Aggravating Factors']}]}]}]\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Extract rubric data using stable Material-UI classes\n", "            def extract_rubric_data(panel_element):\n", "                \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "                import re\n", "\n", "                def extract_score_from_text(text):\n", "                    \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "                    score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "                    if score_match:\n", "                        return {\n", "                            'current': int(score_match.group(1)),\n", "                            'total': int(score_match.group(2))\n", "                        }\n", "                    return None\n", "\n", "                def parse_accordion(accordion_elem, level=0):\n", "                    \"\"\"Parse a single accordion element\"\"\"\n", "                    result = {}\n", "\n", "                    # Find summary element using stable MUI class\n", "                    try:\n", "                        summary = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "                        # Get the full text content from the summary\n", "                        summary_text = summary.text.strip()\n", "\n", "                        # Split by lines and clean up\n", "                        lines = [line.strip() for line in summary_text.split(\n", "                            '\\n') if line.strip()]\n", "\n", "                        # Parse the content - look for title and score patterns\n", "                        title = \"\"\n", "                        score = None\n", "\n", "                        # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "                        full_text = ' '.join(lines)\n", "\n", "                        # Try to find score pattern in the full text\n", "                        score_match = re.search(\n", "                            r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "                        if score_match:\n", "                            score = {\n", "                                'current': int(score_match.group(1)),\n", "                                'total': int(score_match.group(2))\n", "                            }\n", "                            # Remove the score part to get the title\n", "                            title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                                           '', full_text).strip()\n", "                        else:\n", "                            # No score found, the whole text is the title\n", "                            title = full_text\n", "\n", "                        # Fallback: if title is empty, try the first line\n", "                        if not title and lines:\n", "                            title = lines[0]\n", "\n", "                        result['title'] = title\n", "                        if score:\n", "                            result['score'] = score\n", "\n", "                    except Exception as e:\n", "                        print(f\"Error parsing accordion summary: {e}\")\n", "                        pass\n", "\n", "                    # Find details element and look for nested content\n", "                    try:\n", "                        details = accordion_elem.find_element(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "                        # Look for checkboxes (leaf items)\n", "                        checkboxes = details.find_elements(\n", "                            By.XPATH, \".//input[@type='checkbox']\")\n", "                        if checkboxes:\n", "                            items = []\n", "                            for checkbox in checkboxes:\n", "                                name = checkbox.get_attribute('name')\n", "                                if name and name.strip():\n", "                                    items.append(name.strip())\n", "                            if items:\n", "                                result['items'] = items\n", "\n", "                        # Look for nested accordions (subcategories)\n", "                        nested_accordions = details.find_elements(\n", "                            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "                        if nested_accordions:\n", "                            subcategories = []\n", "                            for nested in nested_accordions:\n", "                                try:\n", "                                    nested_result = parse_accordion(\n", "                                        nested, level + 1)\n", "                                    if nested_result and nested_result.get('title'):\n", "                                        subcategories.append(nested_result)\n", "                                except:\n", "                                    continue\n", "                            if subcategories:\n", "                                result['subcategories'] = subcategories\n", "                    except:\n", "                        pass\n", "\n", "                    return result\n", "\n", "                # Find all top-level accordions in the panel\n", "                top_accordions = panel_element.find_elements(\n", "                    By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                # Filter to get only the main categories (not nested ones)\n", "                main_categories = []\n", "                for accordion in top_accordions:\n", "                    try:\n", "                        # Check if this accordion has a parent accordion - if so, skip it\n", "                        parent_accordion = accordion.find_element(\n", "                            By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "                        # If we found a parent accordion, this is nested, so skip\n", "                        continue\n", "                    except:\n", "                        # No parent accordion found, this is a top-level one\n", "                        parsed = parse_accordion(accordion)\n", "                        if parsed and parsed.get('title'):\n", "                            main_categories.append(parsed)\n", "\n", "                return main_categories\n", "\n", "            # Extract the data\n", "            rubric_data = extract_rubric_data(panel)\n", "\n", "            # Deduplicate the data to remove hierarchy conflicts\n", "            def deduplicate_rubric_data(categories):\n", "                \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "                def collect_all_subcategory_titles(cats):\n", "                    \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "                    titles = set()\n", "                    for cat in cats:\n", "                        if 'subcategories' in cat:\n", "                            for subcat in cat['subcategories']:\n", "                                titles.add(subcat['title'])\n", "                                # Recursively collect from deeper levels\n", "                                titles.update(\n", "                                    collect_all_subcategory_titles([subcat]))\n", "                    return titles\n", "\n", "                def clean_category(category, all_subcat_titles):\n", "                    \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "                    cleaned = {\n", "                        'title': category['title']\n", "                    }\n", "\n", "                    # Add score if present\n", "                    if 'score' in category:\n", "                        cleaned['score'] = category['score']\n", "\n", "                    # Process subcategories first (recursively)\n", "                    if 'subcategories' in category:\n", "                        cleaned_subcats = []\n", "\n", "                        # Collect titles of subcategories that have their own subcategories\n", "                        # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "                        parent_subcats = set()\n", "                        for subcat in category['subcategories']:\n", "                            if 'subcategories' in subcat:\n", "                                for nested in subcat['subcategories']:\n", "                                    # Only add if it's not a self-reference\n", "                                    if nested['title'] != subcat['title']:\n", "                                        parent_subcats.add(nested['title'])\n", "\n", "                        # First, identify the best version of each duplicate title\n", "                        title_to_best_subcat = {}\n", "                        for subcat in category['subcategories']:\n", "                            title = subcat['title']\n", "                            if title not in title_to_best_subcat:\n", "                                title_to_best_subcat[title] = subcat\n", "                            else:\n", "                                # Compare with existing: prefer the one with subcategories\n", "                                existing = title_to_best_subcat[title]\n", "                                current_has_subcats = 'subcategories' in subcat\n", "                                existing_has_subcats = 'subcategories' in existing\n", "\n", "                                if current_has_subcats and not existing_has_subcats:\n", "                                    # Current is better (has subcategories)\n", "                                    title_to_best_subcat[title] = subcat\n", "                                elif current_has_subcats and existing_has_subcats:\n", "                                    # Both have subcategories, prefer the one with more subcategories\n", "                                    if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                                        title_to_best_subcat[title] = subcat\n", "\n", "                        # Now process the best version of each subcategory\n", "                        for title, best_subcat in title_to_best_subcat.items():\n", "                            # Skip if this title is a subcategory of another subcategory at this level\n", "                            if title not in parent_subcats:\n", "                                cleaned_subcat = clean_category(\n", "                                    best_subcat, all_subcat_titles)\n", "                                cleaned_subcats.append(cleaned_subcat)\n", "\n", "                        if cleaned_subcats:\n", "                            cleaned['subcategories'] = cleaned_subcats\n", "\n", "                    # Process items - only include items that are NOT subcategory titles\n", "                    if 'items' in category:\n", "                        # If this category has subcategories, don't include items that belong to subcategories\n", "                        if 'subcategories' in cleaned:\n", "                            # Collect all items that belong to subcategories\n", "                            subcat_items = set()\n", "                            for subcat in cleaned['subcategories']:\n", "                                if 'items' in subcat:\n", "                                    subcat_items.update(subcat['items'])\n", "                                # Also collect items from deeper subcategories\n", "\n", "                                def collect_deep_items(cat):\n", "                                    items = set()\n", "                                    if 'items' in cat:\n", "                                        items.update(cat['items'])\n", "                                    if 'subcategories' in cat:\n", "                                        for sc in cat['subcategories']:\n", "                                            items.update(\n", "                                                collect_deep_items(sc))\n", "                                    return items\n", "                                subcat_items.update(collect_deep_items(subcat))\n", "\n", "                            # Only keep items that don't belong to any subcategory\n", "                            filtered_items = []\n", "                            for item in category['items']:\n", "                                if item not in subcat_items and item not in all_subcat_titles:\n", "                                    filtered_items.append(item)\n", "\n", "                            if filtered_items:\n", "                                cleaned['items'] = filtered_items\n", "                        else:\n", "                            # No subcategories, keep all items\n", "                            cleaned['items'] = category['items']\n", "\n", "                    return cleaned\n", "\n", "                # First pass: collect all subcategory titles\n", "                all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "                # Second pass: clean each category and remove duplicates\n", "                cleaned_categories = []\n", "                processed_titles = set()\n", "\n", "                for category in categories:\n", "                    title = category['title']\n", "                    # Only process if this title hasn't been seen as a subcategory\n", "                    if title not in all_subcat_titles and title not in processed_titles:\n", "                        cleaned = clean_category(category, all_subcat_titles)\n", "                        cleaned_categories.append(cleaned)\n", "                        processed_titles.add(title)\n", "\n", "                return cleaned_categories\n", "\n", "            # Clean the data\n", "            clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "            # Format and display the results\n", "            def format_rubric_display(categories, indent=0):\n", "                \"\"\"Format rubric data for display\"\"\"\n", "                output = []\n", "                indent_str = \"    \" * indent\n", "\n", "                for category in categories:\n", "                    title = category.get('title', 'Unknown')\n", "                    score = category.get('score')\n", "\n", "                    if score:\n", "                        score_str = f\" {score['current']}/{score['total']}\"\n", "                    else:\n", "                        score_str = \"\"\n", "\n", "                    output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "                    # Add subcategories\n", "                    if 'subcategories' in category:\n", "                        sub_output = format_rubric_display(\n", "                            category['subcategories'], indent + 1)\n", "                        output.extend(sub_output)\n", "\n", "                    # Add items (checkboxes)\n", "                    if 'items' in category:\n", "                        for item in category['items']:\n", "                            output.append(f\"{indent_str}        {item}\")\n", "\n", "                return output\n", "\n", "            print(\"\\n===== Marking Rubric (Raw) =====\")\n", "            print(rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (Cleaned) =====\")\n", "            print(clean_rubric_data)\n", "\n", "            print(\"\\n===== Marking Rubric (structured) =====\")\n", "            formatted_output = format_rubric_display(clean_rubric_data)\n", "            for line in formatted_output:\n", "                print(line)\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "a99dda33-81cd-46ac-b774-6c7fc494f9ee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "Found top-level main accordion: Shortness Of Breath\n", "Found nested main accordion: Quality (level 2, score 1)\n", "Skipping nested accordion: Character (level 3, score 1)\n", "Found nested main accordion: Se<PERSON><PERSON> (level 2, score 3)\n", "Skipping nested accordion: Qualitative (level 3, score 2)\n", "Skipping nested accordion: Quantitative (level 3, score 1)\n", "Found nested main accordion: Time Course (level 2, score 5)\n", "Skipping nested accordion: Time Course (level 3, score 4)\n", "Skipping nested accordion: Previous Episodes (level 3, score 1)\n", "Found nested main accordion: Contributing Factors (level 2, score 7)\n", "Skipping nested accordion: Relieving Factors (level 3, score 2)\n", "Skipping nested accordion: Context and Aggravating Factors (level 3, score 5)\n", "\n", "Raw data for HOPC: 5 categories\n", "  1. Shortness Of Breath - {'current': 0, 'total': 16}\n", "  2. Quality - {'current': 0, 'total': 1}\n", "  3. Severity - {'current': 0, 'total': 3}\n", "  4. Time Course - {'current': 0, 'total': 5}\n", "  5. Contributing Factors - {'current': 0, 'total': 7}\n", "Processed main category: Shortness Of Breath\n", "Processed main category: Quality\n", "Processed main category: Severity\n", "Processed main category: Time Course\n", "Processed main category: Contributing Factors\n", "\n", "Clean data for HOPC: 5 categories\n", "  1. Shortness Of Breath - {'current': 0, 'total': 16}\n", "  2. Quality - {'current': 0, 'total': 1}\n", "  3. Severity - {'current': 0, 'total': 3}\n", "  4. Time Course - {'current': 0, 'total': 5}\n", "  5. Contributing Factors - {'current': 0, 'total': 7}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "- Quality 0/1\n", "    - Character 0/1\n", "            Asking Generally\n", "- Severity 0/3\n", "    - Qualitative 0/2\n", "            Ability to Sleep\n", "            Asking Generally\n", "            Affecting Daily Life\n", "    - Quantitative 0/1\n", "            Scale out of 10\n", "            Exercise Capacity\n", "- Time Course 0/5\n", "    - Time Course 0/4\n", "            Onset\n", "            Offset\n", "            Duration\n", "            Change Over Time\n", "            Onset of Worsening\n", "            Frequency of Episodes\n", "    - Previous Episodes 0/1\n", "            Asking Generally\n", "- Contributing Factors 0/7\n", "    - Relieving Factors 0/2\n", "            Resting\n", "            Positional\n", "            Medications\n", "            Asking Generally\n", "    - Context and Aggravating Factors 0/5\n", "            Ideas\n", "            Allergies\n", "            Positional\n", "            Context at Onset\n", "            Lifestyle Changes\n", "            Physical Activity\n", "            Infectious Contacts\n", "            Waking up Short of Breath\n", "            Shortness of Breath when Laying Down\n", "            Asking Generally about Aggravating Factors\n", "\n", "===== Processing Assoc. =====\n", "Found nested main accordion: <PERSON><PERSON> (level 2, score 1)\n", "Found nested main accordion: Fever (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON><PERSON> (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON><PERSON> (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON><PERSON> (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON><PERSON> (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON> <PERSON> (level 2, score 1)\n", "Found nested main accordion: Weight Loss (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON> Symptoms (level 2, score 1)\n", "Found nested main accordion: Altered <PERSON> (level 2, score 1)\n", "Found nested main accordion: Waking up <PERSON> of Breath (level 2, score 1)\n", "Found nested main accordion: Shortness of Breath when Laying Down (level 2, score 1)\n", "\n", "Raw data for Assoc.: 12 categories\n", "  1. <PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  2. Fever - {'current': 0, 'total': 1}\n", "  3. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  4. Wheeze - {'current': 0, 'total': 1}\n", "  5. <PERSON><PERSON>ue - {'current': 0, 'total': 1}\n", "  6. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  7. <PERSON><PERSON> <PERSON> - {'current': 0, 'total': 1}\n", "  8. Weight Loss - {'current': 0, 'total': 1}\n", "  9. <PERSON><PERSON> Symptoms - {'current': 0, 'total': 1}\n", "  10. <PERSON><PERSON> Bowel Habits - {'current': 0, 'total': 1}\n", "  11. Waking up Short of Breath - {'current': 0, 'total': 1}\n", "  12. Shortness of Breath when Laying Down - {'current': 0, 'total': 1}\n", "Processed main category: Cough\n", "Processed main category: Fever\n", "Processed main category: Pallor\n", "Processed main category: Wheeze\n", "Processed main category: Fatigue\n", "Processed main category: Swelling\n", "Processed main category: Chest Pain\n", "Processed main category: Weight Loss\n", "Processed main category: Coryzal Symptoms\n", "Processed main category: Altered Bowel Habits\n", "Processed main category: Waking up Short of Breath\n", "Processed main category: Shortness of Breath when Laying Down\n", "\n", "Clean data for Assoc.: 12 categories\n", "  1. <PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  2. Fever - {'current': 0, 'total': 1}\n", "  3. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  4. Wheeze - {'current': 0, 'total': 1}\n", "  5. <PERSON><PERSON>ue - {'current': 0, 'total': 1}\n", "  6. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  7. <PERSON><PERSON> <PERSON> - {'current': 0, 'total': 1}\n", "  8. Weight Loss - {'current': 0, 'total': 1}\n", "  9. <PERSON><PERSON> Symptoms - {'current': 0, 'total': 1}\n", "  10. <PERSON><PERSON> Bowel Habits - {'current': 0, 'total': 1}\n", "  11. Waking up Short of Breath - {'current': 0, 'total': 1}\n", "  12. Shortness of Breath when Laying Down - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Cough 0/1\n", "        <PERSON><PERSON>\n", "- Fever 0/1\n", "        Fever\n", "- Pallor 0/1\n", "        <PERSON><PERSON><PERSON>\n", "- Wheeze 0/1\n", "        Wheeze\n", "- Fatigue 0/1\n", "        Fatigue\n", "- Swelling 0/1\n", "- Chest Pain 0/1\n", "        Chest Pain\n", "- Weight Loss 0/1\n", "        Weight Loss\n", "- Coryzal Symptoms 0/1\n", "        Sneezing\n", "        <PERSON><PERSON>\n", "        Nasal <PERSON>harge\n", "        Nasal Congestion\n", "- Altered Bowel Habits 0/1\n", "        Defecating Blood\n", "        Black and Tarry Stools\n", "- Waking up <PERSON> of Breath 0/1\n", "        Waking up Short of Breath\n", "- Shortness of Breath when Laying Down 0/1\n", "        Shortness of Breath when Laying Down\n", "\n", "===== Processing PMHx =====\n", "Found top-level main accordion: Medication History\n", "Found nested main accordion: Prescription Medications (level 2, score 1)\n", "Found top-level main accordion: Past Medical History\n", "Skipping nested accordion: Past Medical History (level 2, score 1)\n", "Found nested main accordion: Respiratory Disease History (level 2, score 1)\n", "Found nested main accordion: Cardiovascular Disease History (level 2, score 1)\n", "Found nested main accordion: Haematological Disease History (level 2, score 1)\n", "\n", "Raw data for PMHx: 6 categories\n", "  1. Medication History - {'current': 0, 'total': 1}\n", "  2. Prescription Medications - {'current': 0, 'total': 1}\n", "  3. Past Medical History - {'current': 0, 'total': 3}\n", "  4. Respiratory Disease History - {'current': 0, 'total': 1}\n", "  5. Cardiovascular Disease History - {'current': 0, 'total': 1}\n", "  6. Haematological Disease History - {'current': 0, 'total': 1}\n", "Processed main category: Medication History\n", "Processed main category: Prescription Medications\n", "Processed main category: Past Medical History\n", "Processed main category: Respiratory Disease History\n", "Processed main category: Cardiovascular Disease History\n", "Processed main category: Haematological Disease History\n", "\n", "Clean data for PMHx: 6 categories\n", "  1. Medication History - {'current': 0, 'total': 1}\n", "  2. Prescription Medications - {'current': 0, 'total': 1}\n", "  3. Past Medical History - {'current': 0, 'total': 3}\n", "  4. Respiratory Disease History - {'current': 0, 'total': 1}\n", "  5. Cardiovascular Disease History - {'current': 0, 'total': 1}\n", "  6. Haematological Disease History - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Medication History 0/1\n", "    - Prescription Medications 0/1\n", "            Inhalers\n", "            Asking Generally\n", "            Antihypertensives\n", "- Prescription Medications 0/1\n", "        Inhalers\n", "        Asking Generally\n", "        Antihypertensives\n", "- Past Medical History 0/3\n", "    - Past Medical History 0/1\n", "            Recent Illness\n", "            Asking Generally\n", "            Previous Hospitalisations\n", "    - Respiratory Disease History 0/1\n", "            COPD\n", "            Asthma\n", "            Pneumonia\n", "            Bronchiectasis\n", "            Asking Generally\n", "            Interstitial Lung Disease\n", "    - Cardiovascular Disease History 0/1\n", "            Asking Generally\n", "            Valvular Heart Disease\n", "            Coronary Artery Disease\n", "    - Haematological Disease History 0/1\n", "            Anaemia\n", "            Iron Deficiency\n", "- Respiratory Disease History 0/1\n", "        COPD\n", "        Asthma\n", "        Pneumonia\n", "        Bronchiectasis\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "- Cardiovascular Disease History 0/1\n", "        Asking Generally\n", "        Valvular Heart Disease\n", "        Coronary Artery Disease\n", "- Haematological Disease History 0/1\n", "        Anaemia\n", "        Iron Deficiency\n", "\n", "===== Processing FMHx =====\n", "Found nested main accordion: Family History (level 2, score 1)\n", "\n", "Raw data for FMHx: 1 categories\n", "  1. Family History - {'current': 0, 'total': 1}\n", "Processed main category: Family History\n", "\n", "Clean data for FMHx: 1 categories\n", "  1. Family History - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Family History 0/1\n", "        COPD\n", "        Asthma\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "\n", "===== Processing Social =====\n", "Found top-level main accordion: Social History\n", "Found nested main accordion: Alcohol History (level 2, score 1)\n", "Found nested main accordion: Smoking History (level 2, score 1)\n", "Found nested main accordion: Recreational Drug History (level 2, score 1)\n", "\n", "Raw data for Social: 4 categories\n", "  1. Social History - {'current': 0, 'total': 2}\n", "  2. Alcohol History - {'current': 0, 'total': 1}\n", "  3. Smoking History - {'current': 0, 'total': 1}\n", "  4. Recreational Drug History - {'current': 0, 'total': 1}\n", "Processed main category: Social History\n", "Processed main category: Alcohol History\n", "Processed main category: Smoking History\n", "Processed main category: Recreational Drug History\n", "\n", "Clean data for Social: 4 categories\n", "  1. Social History - {'current': 0, 'total': 2}\n", "  2. Alcohol History - {'current': 0, 'total': 1}\n", "  3. Smoking History - {'current': 0, 'total': 1}\n", "  4. Recreational Drug History - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Social History 0/2\n", "    - Alcohol History 0/1\n", "            Drinking Status\n", "            Amount Currently\n", "            Amount at Baseline\n", "    - Smoking History 0/1\n", "            Smoking Status\n", "            Amount Currently\n", "            Amount in the Past\n", "    - Recreational Drug History 0/1\n", "            User Status\n", "- Alcohol History 0/1\n", "        Drinking Status\n", "        Amount Currently\n", "        Amount at Baseline\n", "- Smoking History 0/1\n", "        Smoking Status\n", "        Amount Currently\n", "        Amount in the Past\n", "- Recreational Drug History 0/1\n", "        User Status\n", "\n", "===== Processing Basics =====\n", "Found nested main accordion: Introduction (level 2, score 1)\n", "Found nested main accordion: Patient Identity (level 2, score 1)\n", "Found nested main accordion: <PERSON><PERSON> (level 2, score 1)\n", "\n", "Raw data for Basics: 3 categories\n", "  1. Introduction - {'current': 0, 'total': 1}\n", "  2. Patient Identity - {'current': 0, 'total': 1}\n", "  3. Present<PERSON> <PERSON><PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "Processed main category: Introduction\n", "Processed main category: Patient Identity\n", "Processed main category: Present<PERSON> <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Clean data for Basics: 3 categories\n", "  1. Introduction - {'current': 0, 'total': 1}\n", "  2. Patient Identity - {'current': 0, 'total': 1}\n", "  3. Present<PERSON> <PERSON><PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Introduction 0/1\n", "        Introduction\n", "- Patient Identity 0/1\n", "        Preferred Name\n", "        Patient Identity\n", "- Presenting <PERSON><PERSON><PERSON> 0/1\n", "        Presenting <PERSON><PERSON><PERSON><PERSON>\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def extract_score_from_text(text):\n", "        \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "        score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "        if score_match:\n", "            return {\n", "                'current': int(score_match.group(1)),\n", "                'total': int(score_match.group(2))\n", "            }\n", "        return None\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = {\n", "                    'current': int(score_match.group(1)),\n", "                    'total': int(score_match.group(2))\n", "                }\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Strategy 1: Look for main accordions (big headings)\n", "    all_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # Collect all accordions, including nested ones that might be main categories\n", "    main_categories = []\n", "    processed_titles = {}  # Track titles and their count\n", "\n", "    for accordion in all_accordions:\n", "        try:\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                title = parsed['title']\n", "\n", "                # Check if this is a true nested accordion or a separate main category\n", "                try:\n", "                    parent_accordion = accordion.find_element(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                    # Even if it has a parent, it might still be a main category\n", "                    # Check the nesting level and content to decide\n", "                    nesting_level = len(accordion.find_elements(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\"))\n", "\n", "                    # Determine if this should be treated as a main category based on:\n", "                    # 1. Nesting level (≤ 2 for main categories)\n", "                    # 2. Score value (higher scores are more likely to be main categories)\n", "                    # 3. Content structure\n", "\n", "                    score = parsed.get('score', {})\n", "                    score_total = score.get('total', 0) if score else 0\n", "\n", "                    # For PMHx: prefer the outer containers (0/3) over inner subcategories (0/1)\n", "                    # But also capture disease-specific histories that are at the right level\n", "                    should_include = False\n", "\n", "                    if nesting_level == 1:\n", "                        # Top-level or first-level nested - likely main categories\n", "                        should_include = True\n", "                    elif nesting_level == 2:\n", "                        # Second-level nested - include if it has a good score or is disease-specific\n", "                        if score_total >= 2 or \"Disease History\" in title:\n", "                            should_include = True\n", "                        elif score_total == 1 and \"Past Medical History\" not in title:\n", "                            # Include specific subcategories but not generic \"Past Medical History\" with low scores\n", "                            should_include = True\n", "                    elif nesting_level == 3:\n", "                        # Third-level nested - include disease-specific histories that might be deeply nested\n", "                        if \"Disease History\" in title and score_total == 1:\n", "                            should_include = True\n", "\n", "                    if should_include:\n", "                        # Add a suffix to distinguish multiple instances of the same title\n", "                        if title in processed_titles:\n", "                            processed_titles[title] += 1\n", "                            unique_title = f\"{title} ({processed_titles[title]})\"\n", "                            parsed['title'] = unique_title\n", "                            print(\n", "                                f\"Found nested main accordion: {unique_title} (level {nesting_level}, score {score_total})\")\n", "                        else:\n", "                            processed_titles[title] = 1\n", "                            print(\n", "                                f\"Found nested main accordion: {title} (level {nesting_level}, score {score_total})\")\n", "\n", "                        main_categories.append(parsed)\n", "                    else:\n", "                        print(\n", "                            f\"Skipping nested accordion: {title} (level {nesting_level}, score {score_total})\")\n", "\n", "                except:\n", "                    # No parent accordion found, this is definitely a top-level one\n", "                    if title in processed_titles:\n", "                        processed_titles[title] += 1\n", "                        unique_title = f\"{title} ({processed_titles[title]})\"\n", "                        parsed['title'] = unique_title\n", "                        print(\n", "                            f\"Found top-level main accordion: {unique_title}\")\n", "                    else:\n", "                        processed_titles[title] = 1\n", "                        print(f\"Found top-level main accordion: {title}\")\n", "\n", "                    main_categories.append(parsed)\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion: {e}\")\n", "            continue\n", "\n", "    # Strategy 2: If no main accordions found, look for alternative content structures\n", "    if not main_categories:\n", "        print(\"No main accordions found, looking for alternative content structures...\")\n", "\n", "        # Look for direct checkboxes (flat structure without main headings)\n", "        checkboxes = panel_element.find_elements(\n", "            By.XPATH, \".//input[@type='checkbox']\")\n", "        if checkboxes:\n", "            items = []\n", "            for checkbox in checkboxes:\n", "                name = checkbox.get_attribute('name')\n", "                if name and name.strip():\n", "                    items.append(name.strip())\n", "\n", "            if items:\n", "                # Create a category for direct items\n", "                direct_category = {\n", "                    'title': 'Items',\n", "                    'items': items\n", "                }\n", "                main_categories.append(direct_category)\n", "                print(f\"Found {len(items)} direct checkbox items\")\n", "\n", "        # Look for nested accordions that might not have a main container\n", "        # (This is now less needed since Strategy 1 handles nested accordions better)\n", "        nested_accordions = panel_element.find_elements(\n", "            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "        for accordion in nested_accordions:\n", "            try:\n", "                parsed = parse_accordion(accordion)\n", "                if parsed and parsed.get('title'):\n", "                    # Avoid duplicates from the main accordion search above\n", "                    # Check both exact title match and numbered versions\n", "                    existing_titles = [cat.get('title', '')\n", "                                       for cat in main_categories]\n", "                    base_title = parsed['title']\n", "\n", "                    # Check if this title (or a numbered version) already exists\n", "                    title_exists = any(\n", "                        existing_title == base_title or\n", "                        existing_title.startswith(f\"{base_title} (\")\n", "                        for existing_title in existing_titles\n", "                    )\n", "\n", "                    if not title_exists:\n", "                        main_categories.append(parsed)\n", "                        print(\n", "                            f\"Found additional nested accordion: {parsed['title']}\")\n", "            except Exception as e:\n", "                print(f\"Error parsing nested accordion: {e}\")\n", "                continue\n", "\n", "        # Look for any text content that might indicate items or structure\n", "        text_content = panel_element.text.strip()\n", "        # if text_content and not main_categories:\n", "            # print(\n", "                # f\"Found text content (first 200 chars): {text_content[:200]}...\")\n", "\n", "            # Try to parse text content for potential items\n", "            lines = [line.strip()\n", "                     for line in text_content.split('\\n') if line.strip()]\n", "            # if lines:\n", "                # print(f\"Found {len(lines)} text lines\")\n", "                # Could potentially create items from text lines if they follow a pattern\n", "\n", "        # Look for other possible content structures\n", "        content_divs = panel_element.find_elements(\n", "            By.XPATH, \".//div[normalize-space(text())]\")\n", "        if content_divs and not main_categories:\n", "            print(f\"Found {len(content_divs)} content divs\")\n", "            # Show first 5 for debugging\n", "            for i, div in enumerate(content_divs[:5]):\n", "                div_text = div.text.strip()\n", "                if div_text and len(div_text) > 3:  # Skip very short text\n", "                    print(f\"  Content div {i+1}: {div_text[:100]}\")\n", "\n", "        # Strategy 3: Look for any form elements or interactive content\n", "        form_elements = panel_element.find_elements(\n", "            By.XPATH, \".//input | .//select | .//textarea | .//button\")\n", "        if form_elements and not main_categories:\n", "            print(f\"Found {len(form_elements)} form elements\")\n", "            for i, element in enumerate(form_elements[:3]):\n", "                element_type = element.get_attribute(\n", "                    'type') or element.tag_name\n", "                element_name = element.get_attribute(\n", "                    'name') or element.get_attribute('id') or 'unnamed'\n", "                print(f\"  Form element {i+1}: {element_type} - {element_name}\")\n", "\n", "        # Strategy 4: Check if the panel is truly empty or just not loaded\n", "        panel_html = panel_element.get_attribute('innerHTML')\n", "        if not panel_html.strip():\n", "            print(\"Panel appears to be completely empty (no HTML content)\")\n", "        elif len(panel_html.strip()) < 50:\n", "            print(f\"Panel has minimal content: {panel_html.strip()}\")\n", "        else:\n", "            print(\n", "                f\"Panel has content but no recognizable structure (HTML length: {len(panel_html)})\")\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # For the multi-category extraction approach, we want to keep all extracted categories\n", "    # as main categories, even if they appear as subcategories elsewhere\n", "    # This is different from the original single-category approach\n", "\n", "    # First pass: collect all subcategory titles (for internal deduplication within categories)\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category but keep all extracted main categories\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Process all categories that were explicitly extracted as main categories\n", "        # Don't filter based on subcategory appearance since we want them as main categories\n", "        if title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "            print(f\"Processed main category: {title}\")\n", "        else:\n", "            print(f\"Skipped duplicate title: {title}\")\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score['current']}/{score['total']}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    print(\n", "                        f\"\\nRaw data for {med_tab_name}: {len(rubric_data)} categories\")\n", "                    for i, cat in enumerate(rubric_data):\n", "                        print(\n", "                            f\"  {i+1}. {cat.get('title', 'Unknown')} - {cat.get('score', 'No score')}\")\n", "\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "                    print(\n", "                        f\"\\nClean data for {med_tab_name}: {len(clean_rubric_data)} categories\")\n", "                    for i, cat in enumerate(clean_rubric_data):\n", "                        print(\n", "                            f\"  {i+1}. {cat.get('title', 'Unknown')} - {cat.get('score', 'No score')}\")\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "9dfae8de-a573-463c-8f37-bab13f871dee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "Skipping nested accordion: Character (level 3, score 1)\n", "Skipping nested accordion: Qualitative (level 3, score 2)\n", "Skipping nested accordion: Quantitative (level 3, score 1)\n", "Skipping nested accordion: Time Course (level 3, score 4)\n", "Skipping nested accordion: Previous Episodes (level 3, score 1)\n", "Skipping nested accordion: Relieving Factors (level 3, score 2)\n", "Skipping nested accordion: Context and Aggravating Factors (level 3, score 5)\n", "Added main category: Shortness Of Breath (level 0, score 16)\n", "Skipping Contributing Factors - already exists as subcategory of Shortness Of Breath\n", "Skipping Time Course - already exists as subcategory of Shortness Of Breath\n", "Skipping Severity - already exists as subcategory of Shortness Of Breath\n", "Skipping Quality - already exists as subcategory of Shortness Of Breath\n", "\n", "Raw data for HOPC: 1 categories\n", "  1. Shortness Of Breath - {'current': 0, 'total': 16}\n", "Processed main category: Shortness Of Breath\n", "\n", "Clean data for HOPC: 1 categories\n", "  1. Shortness Of Breath - {'current': 0, 'total': 16}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "\n", "===== Processing Assoc. =====\n", "Added main category: <PERSON><PERSON> (level 2, score 1)\n", "Added main category: Fever (level 2, score 1)\n", "Added main category: <PERSON><PERSON><PERSON> (level 2, score 1)\n", "Added main category: Wheeze (level 2, score 1)\n", "Added main category: Fat<PERSON><PERSON> (level 2, score 1)\n", "Added main category: Swelling (level 2, score 1)\n", "Added main category: Chest Pain (level 2, score 1)\n", "Added main category: Weight Loss (level 2, score 1)\n", "Added main category: Coryzal Symptoms (level 2, score 1)\n", "Added main category: Altered Bowel Habits (level 2, score 1)\n", "Added main category: Waking up <PERSON> of Breath (level 2, score 1)\n", "Added main category: Shortness of Breath when Laying Down (level 2, score 1)\n", "\n", "Raw data for Assoc.: 12 categories\n", "  1. <PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  2. Fever - {'current': 0, 'total': 1}\n", "  3. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  4. Wheeze - {'current': 0, 'total': 1}\n", "  5. <PERSON><PERSON>ue - {'current': 0, 'total': 1}\n", "  6. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  7. <PERSON><PERSON> <PERSON> - {'current': 0, 'total': 1}\n", "  8. Weight Loss - {'current': 0, 'total': 1}\n", "  9. <PERSON><PERSON> Symptoms - {'current': 0, 'total': 1}\n", "  10. <PERSON><PERSON> Bowel Habits - {'current': 0, 'total': 1}\n", "  11. Waking up Short of Breath - {'current': 0, 'total': 1}\n", "  12. Shortness of Breath when Laying Down - {'current': 0, 'total': 1}\n", "Processed main category: Cough\n", "Processed main category: Fever\n", "Processed main category: Pallor\n", "Processed main category: Wheeze\n", "Processed main category: Fatigue\n", "Processed main category: Swelling\n", "Processed main category: Chest Pain\n", "Processed main category: Weight Loss\n", "Processed main category: Coryzal Symptoms\n", "Processed main category: Altered Bowel Habits\n", "Processed main category: Waking up Short of Breath\n", "Processed main category: Shortness of Breath when Laying Down\n", "\n", "Clean data for Assoc.: 12 categories\n", "  1. <PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  2. Fever - {'current': 0, 'total': 1}\n", "  3. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  4. Wheeze - {'current': 0, 'total': 1}\n", "  5. <PERSON><PERSON>ue - {'current': 0, 'total': 1}\n", "  6. <PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "  7. <PERSON><PERSON> <PERSON> - {'current': 0, 'total': 1}\n", "  8. Weight Loss - {'current': 0, 'total': 1}\n", "  9. <PERSON><PERSON> Symptoms - {'current': 0, 'total': 1}\n", "  10. <PERSON><PERSON> Bowel Habits - {'current': 0, 'total': 1}\n", "  11. Waking up Short of Breath - {'current': 0, 'total': 1}\n", "  12. Shortness of Breath when Laying Down - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Cough 0/1\n", "        <PERSON><PERSON>\n", "- Fever 0/1\n", "        Fever\n", "- Pallor 0/1\n", "        <PERSON><PERSON><PERSON>\n", "- Wheeze 0/1\n", "        Wheeze\n", "- Fatigue 0/1\n", "        Fatigue\n", "- Swelling 0/1\n", "- Chest Pain 0/1\n", "        Chest Pain\n", "- Weight Loss 0/1\n", "        Weight Loss\n", "- Coryzal Symptoms 0/1\n", "        Sneezing\n", "        <PERSON><PERSON>\n", "        Nasal <PERSON>harge\n", "        Nasal Congestion\n", "- Altered Bowel Habits 0/1\n", "        Defecating Blood\n", "        Black and Tarry Stools\n", "- Waking up <PERSON> of Breath 0/1\n", "        Waking up Short of Breath\n", "- Shortness of Breath when Laying Down 0/1\n", "        Shortness of Breath when Laying Down\n", "\n", "===== Processing PMHx =====\n", "Skipping nested accordion: Past Medical History (level 2, score 1)\n", "Keeping Past Medical History as main category (score 3 >= 0)\n", "Added main category: Past Medical History (level 0, score 3)\n", "Added main category: Medication History (level 0, score 1)\n", "Skipping Prescription Medications - already exists as subcategory of Medication History\n", "Skipping Respiratory Disease History - already exists as subcategory of Past Medical History\n", "Skipping Cardiovascular Disease History - already exists as subcategory of Past Medical History\n", "Skipping Haematological Disease History - already exists as subcategory of Past Medical History\n", "\n", "Raw data for PMHx: 2 categories\n", "  1. Past Medical History - {'current': 0, 'total': 3}\n", "  2. Medication History - {'current': 0, 'total': 1}\n", "Processed main category: Past Medical History\n", "Processed main category: Medication History\n", "\n", "Clean data for PMHx: 2 categories\n", "  1. Past Medical History - {'current': 0, 'total': 3}\n", "  2. Medication History - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Past Medical History 0/3\n", "    - Past Medical History 0/1\n", "            Recent Illness\n", "            Asking Generally\n", "            Previous Hospitalisations\n", "    - Respiratory Disease History 0/1\n", "            COPD\n", "            Asthma\n", "            Pneumonia\n", "            Bronchiectasis\n", "            Asking Generally\n", "            Interstitial Lung Disease\n", "    - Cardiovascular Disease History 0/1\n", "            Asking Generally\n", "            Valvular Heart Disease\n", "            Coronary Artery Disease\n", "    - Haematological Disease History 0/1\n", "            Anaemia\n", "            Iron Deficiency\n", "- Medication History 0/1\n", "    - Prescription Medications 0/1\n", "            Inhalers\n", "            Asking Generally\n", "            Antihypertensives\n", "\n", "===== Processing FMHx =====\n", "Added main category: Family History (level 2, score 1)\n", "\n", "Raw data for FMHx: 1 categories\n", "  1. Family History - {'current': 0, 'total': 1}\n", "Processed main category: Family History\n", "\n", "Clean data for FMHx: 1 categories\n", "  1. Family History - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Family History 0/1\n", "        COPD\n", "        Asthma\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "\n", "===== Processing Social =====\n", "Added main category: Social History (level 0, score 2)\n", "Skipping Alcohol History - already exists as subcategory of Social History\n", "Skipping Smoking History - already exists as subcategory of Social History\n", "Skipping Recreational Drug History - already exists as subcategory of Social History\n", "\n", "Raw data for Social: 1 categories\n", "  1. Social History - {'current': 0, 'total': 2}\n", "Processed main category: Social History\n", "\n", "Clean data for Social: 1 categories\n", "  1. Social History - {'current': 0, 'total': 2}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Social History 0/2\n", "    - Alcohol History 0/1\n", "            Drinking Status\n", "            Amount Currently\n", "            Amount at Baseline\n", "    - Smoking History 0/1\n", "            Smoking Status\n", "            Amount Currently\n", "            Amount in the Past\n", "    - Recreational Drug History 0/1\n", "            User Status\n", "\n", "===== Processing Basics =====\n", "Added main category: Introduction (level 2, score 1)\n", "Added main category: Patient Identity (level 2, score 1)\n", "Added main category: <PERSON><PERSON> (level 2, score 1)\n", "\n", "Raw data for Basics: 3 categories\n", "  1. Introduction - {'current': 0, 'total': 1}\n", "  2. Patient Identity - {'current': 0, 'total': 1}\n", "  3. Present<PERSON> <PERSON><PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "Processed main category: Introduction\n", "Processed main category: Patient Identity\n", "Processed main category: Present<PERSON> <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Clean data for Basics: 3 categories\n", "  1. Introduction - {'current': 0, 'total': 1}\n", "  2. Patient Identity - {'current': 0, 'total': 1}\n", "  3. Present<PERSON> <PERSON><PERSON><PERSON><PERSON> - {'current': 0, 'total': 1}\n", "\n", "===== Marking Rubric (structured) =====\n", "- Introduction 0/1\n", "        Introduction\n", "- Patient Identity 0/1\n", "        Preferred Name\n", "        Patient Identity\n", "- Presenting <PERSON><PERSON><PERSON> 0/1\n", "        Presenting <PERSON><PERSON><PERSON><PERSON>\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def extract_score_from_text(text):\n", "        \"\"\"Extract current score and total score from text like '0/16'\"\"\"\n", "        score_match = re.search(r'(\\d+)/(\\d+)', text)\n", "        if score_match:\n", "            return {\n", "                'current': int(score_match.group(1)),\n", "                'total': int(score_match.group(2))\n", "            }\n", "        return None\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = {\n", "                    'current': int(score_match.group(1)),\n", "                    'total': int(score_match.group(2))\n", "                }\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Strategy 1: Look for main accordions (big headings)\n", "    all_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # First pass: collect all potential main categories with their nesting info\n", "    potential_categories = []\n", "\n", "    for accordion in all_accordions:\n", "        try:\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                title = parsed['title']\n", "\n", "                # Check if this is a true nested accordion or a separate main category\n", "                try:\n", "                    parent_accordion = accordion.find_element(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                    # Even if it has a parent, it might still be a main category\n", "                    # Check the nesting level and content to decide\n", "                    nesting_level = len(accordion.find_elements(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\"))\n", "\n", "                    # Determine if this should be treated as a main category based on:\n", "                    # 1. Nesting level (≤ 2 for main categories)\n", "                    # 2. Score value (higher scores are more likely to be main categories)\n", "                    # 3. Content structure\n", "\n", "                    score = parsed.get('score', {})\n", "                    score_total = score.get('total', 0) if score else 0\n", "\n", "                    # For PMHx: prefer the outer containers (0/3) over inner subcategories (0/1)\n", "                    # But also capture disease-specific histories that are at the right level\n", "                    should_include = False\n", "\n", "                    if nesting_level == 1:\n", "                        # Top-level or first-level nested - likely main categories\n", "                        should_include = True\n", "                    elif nesting_level == 2:\n", "                        # Second-level nested - include if it has a good score or is disease-specific\n", "                        if score_total >= 2 or \"Disease History\" in title:\n", "                            should_include = True\n", "                        elif score_total == 1 and \"Past Medical History\" not in title:\n", "                            # Include specific subcategories but not generic \"Past Medical History\" with low scores\n", "                            should_include = True\n", "                    elif nesting_level == 3:\n", "                        # Third-level nested - include disease-specific histories that might be deeply nested\n", "                        if \"Disease History\" in title and score_total == 1:\n", "                            should_include = True\n", "\n", "                    if should_include:\n", "                        # Store potential category with nesting info for later processing\n", "                        potential_categories.append({\n", "                            'parsed': parsed,\n", "                            'title': title,\n", "                            'nesting_level': nesting_level,\n", "                            'score_total': score_total\n", "                        })\n", "                    else:\n", "                        print(\n", "                            f\"Skipping nested accordion: {title} (level {nesting_level}, score {score_total})\")\n", "\n", "                except:\n", "                    # No parent accordion found, this is definitely a top-level one\n", "                    score = parsed.get('score', {})\n", "                    score_total = score.get('total', 0) if score else 0\n", "\n", "                    potential_categories.append({\n", "                        'parsed': parsed,\n", "                        'title': title,\n", "                        'nesting_level': 0,  # Top-level\n", "                        'score_total': score_total\n", "                    })\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion: {e}\")\n", "            continue\n", "\n", "    # Helper function to check if a title is a subcategory of any potential category\n", "    def _is_subcategory_of_any(title, potential_cats):\n", "        for cat_info in potential_cats:\n", "            category = cat_info['parsed']\n", "            if _is_subcategory_of_recursive(title, category):\n", "                return True, category.get('title', 'Unknown')\n", "        return False, None\n", "\n", "    def _is_subcategory_of_recursive(title, category):\n", "        if 'subcategories' in category:\n", "            for subcat in category['subcategories']:\n", "                if subcat.get('title') == title:\n", "                    return True\n", "                # Check recursively in nested subcategories\n", "                if _is_subcategory_of_recursive(title, subcat):\n", "                    return True\n", "        return False\n", "\n", "    # Second pass: determine which categories should be main categories\n", "    # Sort by nesting level (top-level first) and score (higher scores first)\n", "    potential_categories.sort(key=lambda x: (\n", "        x['nesting_level'], -x['score_total']))\n", "\n", "    main_categories = []\n", "    final_processed_titles = {}\n", "\n", "    for cat_info in potential_categories:\n", "        parsed = cat_info['parsed']\n", "        title = cat_info['title']\n", "        nesting_level = cat_info['nesting_level']\n", "        score_total = cat_info['score_total']\n", "\n", "        # Check if this category is already a subcategory of ANY potential main category\n", "        # (not just the ones we've already processed)\n", "        # BUT exclude self-references (e.g., \"Past Medical History\" containing \"Past Medical History\")\n", "        is_subcategory_elsewhere, parent_title = _is_subcategory_of_any(\n", "            title, potential_categories)\n", "\n", "        # Special case: if the parent title is the same as the current title,\n", "        # this might be a self-reference, so we need to check if this is the main category\n", "        if is_subcategory_elsewhere and parent_title == title:\n", "            # This is a self-reference case like \"Past Medical History\" containing \"Past Medical History\"\n", "            # Keep the one with the higher score as the main category\n", "            current_score = score_total\n", "\n", "            # Find the parent category's score\n", "            parent_score = 0\n", "            for other_cat_info in potential_categories:\n", "                if other_cat_info['title'] == parent_title and other_cat_info != cat_info:\n", "                    parent_score = other_cat_info['score_total']\n", "                    break\n", "\n", "            if current_score >= parent_score:\n", "                # This category has equal or higher score, so it should be the main one\n", "                is_subcategory_elsewhere = False\n", "                print(\n", "                    f\"Keeping {title} as main category (score {current_score} >= {parent_score})\")\n", "            else:\n", "                print(\n", "                    f\"Skipping {title} - lower score ({current_score}) than parent ({parent_score})\")\n", "        elif is_subcategory_elsewhere:\n", "            print(\n", "                f\"Skipping {title} - already exists as subcategory of {parent_title}\")\n", "\n", "        if not is_subcategory_elsewhere:\n", "            # Add a suffix to distinguish multiple instances of the same title\n", "            if title in final_processed_titles:\n", "                final_processed_titles[title] += 1\n", "                unique_title = f\"{title} ({final_processed_titles[title]})\"\n", "                parsed['title'] = unique_title\n", "                print(\n", "                    f\"Added main category: {unique_title} (level {nesting_level}, score {score_total})\")\n", "            else:\n", "                final_processed_titles[title] = 1\n", "                print(\n", "                    f\"Added main category: {title} (level {nesting_level}, score {score_total})\")\n", "\n", "            main_categories.append(parsed)\n", "\n", "    # Strategy 2: If no main accordions found, look for alternative content structures\n", "    if not main_categories:\n", "        print(\"No main accordions found, looking for alternative content structures...\")\n", "\n", "        # Look for direct checkboxes (flat structure without main headings)\n", "        checkboxes = panel_element.find_elements(\n", "            By.XPATH, \".//input[@type='checkbox']\")\n", "        if checkboxes:\n", "            items = []\n", "            for checkbox in checkboxes:\n", "                name = checkbox.get_attribute('name')\n", "                if name and name.strip():\n", "                    items.append(name.strip())\n", "\n", "            if items:\n", "                # Create a category for direct items\n", "                direct_category = {\n", "                    'title': 'Items',\n", "                    'items': items\n", "                }\n", "                main_categories.append(direct_category)\n", "                print(f\"Found {len(items)} direct checkbox items\")\n", "\n", "        # Look for nested accordions that might not have a main container\n", "        # (This is now less needed since Strategy 1 handles nested accordions better)\n", "        nested_accordions = panel_element.find_elements(\n", "            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "        for accordion in nested_accordions:\n", "            try:\n", "                parsed = parse_accordion(accordion)\n", "                if parsed and parsed.get('title'):\n", "                    # Avoid duplicates from the main accordion search above\n", "                    # Check both exact title match and numbered versions\n", "                    existing_titles = [cat.get('title', '')\n", "                                       for cat in main_categories]\n", "                    base_title = parsed['title']\n", "\n", "                    # Check if this title (or a numbered version) already exists\n", "                    title_exists = any(\n", "                        existing_title == base_title or\n", "                        existing_title.startswith(f\"{base_title} (\")\n", "                        for existing_title in existing_titles\n", "                    )\n", "\n", "                    if not title_exists:\n", "                        main_categories.append(parsed)\n", "                        print(\n", "                            f\"Found additional nested accordion: {parsed['title']}\")\n", "            except Exception as e:\n", "                print(f\"Error parsing nested accordion: {e}\")\n", "                continue\n", "\n", "        # Look for any text content that might indicate items or structure\n", "        text_content = panel_element.text.strip()\n", "        if text_content and not main_categories:\n", "            print(\n", "                f\"Found text content (first 200 chars): {text_content[:200]}...\")\n", "\n", "            # Try to parse text content for potential items\n", "            lines = [line.strip()\n", "                     for line in text_content.split('\\n') if line.strip()]\n", "            if lines:\n", "                print(f\"Found {len(lines)} text lines\")\n", "                # Could potentially create items from text lines if they follow a pattern\n", "\n", "        # Look for other possible content structures\n", "        content_divs = panel_element.find_elements(\n", "            By.XPATH, \".//div[normalize-space(text())]\")\n", "        if content_divs and not main_categories:\n", "            print(f\"Found {len(content_divs)} content divs\")\n", "            # Show first 5 for debugging\n", "            for i, div in enumerate(content_divs[:5]):\n", "                div_text = div.text.strip()\n", "                if div_text and len(div_text) > 3:  # Skip very short text\n", "                    print(f\"  Content div {i+1}: {div_text[:100]}\")\n", "\n", "        # Strategy 3: Look for any form elements or interactive content\n", "        form_elements = panel_element.find_elements(\n", "            By.XPATH, \".//input | .//select | .//textarea | .//button\")\n", "        if form_elements and not main_categories:\n", "            print(f\"Found {len(form_elements)} form elements\")\n", "            for i, element in enumerate(form_elements[:3]):\n", "                element_type = element.get_attribute(\n", "                    'type') or element.tag_name\n", "                element_name = element.get_attribute(\n", "                    'name') or element.get_attribute('id') or 'unnamed'\n", "                print(f\"  Form element {i+1}: {element_type} - {element_name}\")\n", "\n", "        # Strategy 4: Check if the panel is truly empty or just not loaded\n", "        panel_html = panel_element.get_attribute('innerHTML')\n", "        if not panel_html.strip():\n", "            print(\"Panel appears to be completely empty (no HTML content)\")\n", "        elif len(panel_html.strip()) < 50:\n", "            print(f\"Panel has minimal content: {panel_html.strip()}\")\n", "        else:\n", "            print(\n", "                f\"Panel has content but no recognizable structure (HTML length: {len(panel_html)})\")\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # For the multi-category extraction approach, we want to keep all extracted categories\n", "    # as main categories, even if they appear as subcategories elsewhere\n", "    # This is different from the original single-category approach\n", "\n", "    # First pass: collect all subcategory titles (for internal deduplication within categories)\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category but keep all extracted main categories\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Process all categories that were explicitly extracted as main categories\n", "        # Don't filter based on subcategory appearance since we want them as main categories\n", "        if title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "            print(f\"Processed main category: {title}\")\n", "        else:\n", "            print(f\"Skipped duplicate title: {title}\")\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score['current']}/{score['total']}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    print(\n", "                        f\"\\nRaw data for {med_tab_name}: {len(rubric_data)} categories\")\n", "                    for i, cat in enumerate(rubric_data):\n", "                        print(\n", "                            f\"  {i+1}. {cat.get('title', 'Unknown')} - {cat.get('score', 'No score')}\")\n", "\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "                    print(\n", "                        f\"\\nClean data for {med_tab_name}: {len(clean_rubric_data)} categories\")\n", "                    for i, cat in enumerate(clean_rubric_data):\n", "                        print(\n", "                            f\"  {i+1}. {cat.get('title', 'Unknown')} - {cat.get('score', 'No score')}\")\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": 1, "id": "f8911639-4648-4053-bdd5-2a6b56e20ef9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== tab-Doctor Information =====\n", "\n", "===== Doctor Information (raw) =====\n", " <PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "Requirements\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "Finished?\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "Ready to see your results?\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== Doctor Information (structured) =====\n", "\n", "Intro:\n", "<PERSON> is a 56 year old male, who has presented to general practice with complaints of shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.\n", "\n", "Requirements:\n", "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.\n", "\n", "Finished?:\n", "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.\n", "\n", "Ready to see your results?:\n", "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went...\n", "\n", "===== tab-Script =====\n", "\n", "===== Script (structured) =====\n", "\n", "Setting:\n", " - My name is <PERSON>.\n", " - I’m a 56 year old man and I work as a Project Manager. My pronouns are he/him.\n", " - I’ve come to the doctor today because I have been getting a bit short of breath lately and my wife wanted me to come and have it checked out.\n", "\n", "Persona:\n", " - I’m a pretty relaxed and happy person.\n", " - I think it’s just part of getting old and I’ve only come to the doctor to make my wife happy.\n", " - I’m hoping the doctor will confirm that I’m just getting old and the breathlessness is nothing to worry about.\n", "\n", "HOPC:\n", " - I have been experiencing breathlessness for almost 2 years now and it seems to be getting gradually worse.\n", " - I can now only walk a few kilometres before getting very short of breath and needing to rest.\n", " - Initially I was only getting short of breath when I was working hard.\n", " - For the past 2 or 3 weeks I have gotten breathless with light exertion.\n", " - I get especially breathless when I exert myself. I tried to do a few maintenance things around the house the other day and I got really breathless and was exhausted for the rest of the day.\n", " - I have a cough that is worse in the mornings, it has stayed the same for as long as I can remember.\n", " - Most mornings I cough up about a teaspoon or so of thick, greyish-cloudy gunk, that’s relatively thick, and then a few smaller coughing fits during the day. I have never coughed up any blood.\n", " - I’m otherwise feeling well.\n", " - I have no trouble sleeping, no pain, no fevers, no runny nose or other no weight loss, no palpitations, and no night sweats.\n", "\n", "PMHx:\n", " - I’m usually pretty healthy and I have no underlying medical conditions.\n", " - I don’t have any allergies that I’m aware of. I don’t take any regular medications.\n", " - I take the occasional course of antibiotics for chest infections that I get most winters.\n", " - I can’t remember what the antibiotic I take is called.\n", " - I haven’t had a chest infection in over 6 months.\n", "\n", "FMHx:\n", " - My father has high blood pressure.\n", " - There are no other medical conditions in my family.\n", " - I have no siblings.\n", " - My children are both happy and healthy.\n", "\n", "SHx:\n", " - I work as a project manager and currently I'm managing the new build of the local hospital.\n", " - I don’t spend much time on construction sites, but when I am on site I always use the appropriate PPE.\n", " - I have never worked with asbestos or any dangerous chemicals or fumes.\n", " - I’ve been happily married to my wife for almost 30 years and live at home with my wife and two adult children, who I wish would move out already.\n", " - I have no pets.\n", " - I've smoked a pack of cigarettes every day since my late teens. I don’t want to quit smoking.\n", " - I smoked marijuana in my late teens.\n", " - I usually drink a beer or two most nights.\n", " - I eat a relatively balanced diet.\n", " - I don’t do much exercise, I prefer to watch sport on TV rather than play it.\n", " - I wouldn’t say that I’m overweight.\n", "\n", "===== tab-Marking Rubric =====\n", "\n", "===== Processing HOPC =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Shortness Of Breath 0/16\n", "    - Quality 0/1\n", "        - Character 0/1\n", "                Asking Generally\n", "    - Severity 0/3\n", "        - Qualitative 0/2\n", "                Ability to Sleep\n", "                Asking Generally\n", "                Affecting Daily Life\n", "        - Quantitative 0/1\n", "                Scale out of 10\n", "                Exercise Capacity\n", "    - Time Course 0/5\n", "        - Time Course 0/4\n", "                Onset\n", "                Offset\n", "                Duration\n", "                Change Over Time\n", "                Onset of Worsening\n", "                Frequency of Episodes\n", "        - Previous Episodes 0/1\n", "                Asking Generally\n", "    - Contributing Factors 0/7\n", "        - Relieving Factors 0/2\n", "                Resting\n", "                Positional\n", "                Medications\n", "                Asking Generally\n", "        - Context and Aggravating Factors 0/5\n", "                Ideas\n", "                Allergies\n", "                Positional\n", "                Context at Onset\n", "                Lifestyle Changes\n", "                Physical Activity\n", "                Infectious Contacts\n", "                Waking up Short of Breath\n", "                Shortness of Breath when Laying Down\n", "                Asking Generally about Aggravating Factors\n", "\n", "===== Processing Assoc. =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Cough 0/1\n", "        <PERSON><PERSON>\n", "- Fever 0/1\n", "        Fever\n", "- Pallor 0/1\n", "        <PERSON><PERSON><PERSON>\n", "- Wheeze 0/1\n", "        Wheeze\n", "- Fatigue 0/1\n", "        Fatigue\n", "- Swelling 0/1\n", "- Chest Pain 0/1\n", "        Chest Pain\n", "- Weight Loss 0/1\n", "        Weight Loss\n", "- Coryzal Symptoms 0/1\n", "        Sneezing\n", "        <PERSON><PERSON>\n", "        Nasal <PERSON>harge\n", "        Nasal Congestion\n", "- Altered Bowel Habits 0/1\n", "        Defecating Blood\n", "        Black and Tarry Stools\n", "- Waking up <PERSON> of Breath 0/1\n", "        Waking up Short of Breath\n", "- Shortness of Breath when Laying Down 0/1\n", "        Shortness of Breath when Laying Down\n", "\n", "===== Processing PMHx =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Past Medical History 0/3\n", "    - Past Medical History 0/1\n", "            Recent Illness\n", "            Asking Generally\n", "            Previous Hospitalisations\n", "    - Respiratory Disease History 0/1\n", "            COPD\n", "            Asthma\n", "            Pneumonia\n", "            Bronchiectasis\n", "            Asking Generally\n", "            Interstitial Lung Disease\n", "    - Cardiovascular Disease History 0/1\n", "            Asking Generally\n", "            Valvular Heart Disease\n", "            Coronary Artery Disease\n", "    - Haematological Disease History 0/1\n", "            Anaemia\n", "            Iron Deficiency\n", "- Medication History 0/1\n", "    - Prescription Medications 0/1\n", "            Inhalers\n", "            Asking Generally\n", "            Antihypertensives\n", "\n", "===== Processing FMHx =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Family History 0/1\n", "        COPD\n", "        Asthma\n", "        Asking Generally\n", "        Interstitial Lung Disease\n", "\n", "===== Processing Social =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Social History 0/2\n", "    - Alcohol History 0/1\n", "            Drinking Status\n", "            Amount Currently\n", "            Amount at Baseline\n", "    - Smoking History 0/1\n", "            Smoking Status\n", "            Amount Currently\n", "            Amount in the Past\n", "    - Recreational Drug History 0/1\n", "            User Status\n", "\n", "===== Processing Basics =====\n", "\n", "===== Marking Rubric (structured) =====\n", "- Introduction 0/1\n", "        Introduction\n", "- Patient Identity 0/1\n", "        Preferred Name\n", "        Patient Identity\n", "- Presenting <PERSON><PERSON><PERSON> 0/1\n", "        Presenting <PERSON><PERSON><PERSON><PERSON>\n", "Skipping medical history tab: Qs.\n", "Skipping medical history tab: Overall\n"]}], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "\n", "def extract_rubric_data(panel_element):\n", "    \"\"\"Extract hierarchical rubric data without using dynamic jss classes\"\"\"\n", "    import re\n", "\n", "    def parse_accordion(accordion_elem, level=0):\n", "        \"\"\"Parse a single accordion element\"\"\"\n", "        result = {}\n", "\n", "        # Find summary element using stable MUI class\n", "        try:\n", "            summary = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionSummary-root')]\")\n", "\n", "            # Get the full text content from the summary\n", "            summary_text = summary.text.strip()\n", "\n", "            # Split by lines and clean up\n", "            lines = [line.strip() for line in summary_text.split(\n", "                '\\n') if line.strip()]\n", "\n", "            # Parse the content - look for title and score patterns\n", "            title = \"\"\n", "            score = None\n", "\n", "            # The summary text might be like: \"Quality\\n0\\n/1\" or \"Quality 0/1\"\n", "            full_text = ' '.join(lines)\n", "\n", "            # Try to find score pattern in the full text\n", "            score_match = re.search(\n", "                r'(\\d+)\\s*/\\s*(\\d+)', full_text)\n", "            if score_match:\n", "                score = {\n", "                    'current': int(score_match.group(1)),\n", "                    'total': int(score_match.group(2))\n", "                }\n", "                # Remove the score part to get the title\n", "                title = re.sub(r'\\s*\\d+\\s*/\\s*\\d+\\s*',\n", "                               '', full_text).strip()\n", "            else:\n", "                # No score found, the whole text is the title\n", "                title = full_text\n", "\n", "            # Fallback: if title is empty, try the first line\n", "            if not title and lines:\n", "                title = lines[0]\n", "\n", "            result['title'] = title\n", "            if score:\n", "                result['score'] = score\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion summary: {e}\")\n", "            pass\n", "\n", "        # Find details element and look for nested content\n", "        try:\n", "            details = accordion_elem.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordionDetails-root')]\")\n", "\n", "            # Look for checkboxes (leaf items)\n", "            checkboxes = details.find_elements(\n", "                By.XPATH, \".//input[@type='checkbox']\")\n", "            if checkboxes:\n", "                items = []\n", "                for checkbox in checkboxes:\n", "                    name = checkbox.get_attribute('name')\n", "                    if name and name.strip():\n", "                        items.append(name.strip())\n", "                if items:\n", "                    result['items'] = items\n", "\n", "            # Look for nested accordions (subcategories)\n", "            nested_accordions = details.find_elements(\n", "                By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "            if nested_accordions:\n", "                subcategories = []\n", "                for nested in nested_accordions:\n", "                    try:\n", "                        nested_result = parse_accordion(\n", "                            nested, level + 1)\n", "                        if nested_result and nested_result.get('title'):\n", "                            subcategories.append(nested_result)\n", "                    except:\n", "                        continue\n", "                if subcategories:\n", "                    result['subcategories'] = subcategories\n", "        except:\n", "            pass\n", "\n", "        return result\n", "\n", "    # Strategy 1: Look for main accordions (big headings)\n", "    all_accordions = panel_element.find_elements(\n", "        By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "    # First pass: collect all potential main categories with their nesting info\n", "    potential_categories = []\n", "\n", "    for accordion in all_accordions:\n", "        try:\n", "            parsed = parse_accordion(accordion)\n", "            if parsed and parsed.get('title'):\n", "                title = parsed['title']\n", "\n", "                # Check if this is a true nested accordion or a separate main category\n", "                try:\n", "                    parent_accordion = accordion.find_element(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\")\n", "\n", "                    # Even if it has a parent, it might still be a main category\n", "                    # Check the nesting level and content to decide\n", "                    nesting_level = len(accordion.find_elements(\n", "                        By.XPATH, \"ancestor::div[contains(@class, 'MuiAccordion-root')]\"))\n", "\n", "                    # Generic approach: determine if this should be treated as a main category\n", "                    # Based on nesting level and relative importance within the structure\n", "\n", "                    score = parsed.get('score', {})\n", "                    score_total = score.get('total', 0) if score else 0\n", "\n", "                    should_include = False\n", "\n", "                    if nesting_level <= 1:\n", "                        # Top-level or first-level nested - always include as main categories\n", "                        should_include = True\n", "                    elif nesting_level == 2:\n", "                        # Second-level nested - include if it seems significant\n", "                        # Use relative scoring: if this category's score is significant compared to others\n", "                        # or if it has substantial content (items or subcategories)\n", "                        has_content = ('items' in parsed and len(parsed['items']) > 0) or \\\n", "                            ('subcategories' in parsed and len(\n", "                                parsed['subcategories']) > 0)\n", "\n", "                        # Include if it has a meaningful score (> 0) or substantial content\n", "                        if score_total > 0 or has_content:\n", "                            should_include = True\n", "                    elif nesting_level == 3:\n", "                        # Third-level nested - only include if it has significant content\n", "                        # This catches deeply nested but important categories\n", "                        has_substantial_content = ('items' in parsed and len(parsed['items']) >= 3) or \\\n", "                            ('subcategories' in parsed and len(\n", "                                parsed['subcategories']) >= 2)\n", "\n", "                        if score_total > 0 and has_substantial_content:\n", "                            should_include = True\n", "\n", "                    if should_include:\n", "                        # Store potential category with nesting info for later processing\n", "                        potential_categories.append({\n", "                            'parsed': parsed,\n", "                            'title': title,\n", "                            'nesting_level': nesting_level,\n", "                            'score_total': score_total\n", "                        })\n", "\n", "                except:\n", "                    # No parent accordion found, this is definitely a top-level one\n", "                    score = parsed.get('score', {})\n", "                    score_total = score.get('total', 0) if score else 0\n", "\n", "                    potential_categories.append({\n", "                        'parsed': parsed,\n", "                        'title': title,\n", "                        'nesting_level': 0,  # Top-level\n", "                        'score_total': score_total\n", "                    })\n", "\n", "        except Exception as e:\n", "            print(f\"Error parsing accordion: {e}\")\n", "            continue\n", "\n", "    # Helper function to check if a title is a subcategory of any potential category\n", "    def _is_subcategory_of_any(title, potential_cats):\n", "        for cat_info in potential_cats:\n", "            category = cat_info['parsed']\n", "            if _is_subcategory_of_recursive(title, category):\n", "                return True, category.get('title', 'Unknown')\n", "        return False, None\n", "\n", "    def _is_subcategory_of_recursive(title, category):\n", "        if 'subcategories' in category:\n", "            for subcat in category['subcategories']:\n", "                if subcat.get('title') == title:\n", "                    return True\n", "                # Check recursively in nested subcategories\n", "                if _is_subcategory_of_recursive(title, subcat):\n", "                    return True\n", "        return False\n", "\n", "    # Second pass: determine which categories should be main categories\n", "    # Sort by nesting level (top-level first) and score (higher scores first)\n", "    potential_categories.sort(key=lambda x: (\n", "        x['nesting_level'], -x['score_total']))\n", "\n", "    main_categories = []\n", "    final_processed_titles = {}\n", "\n", "    for cat_info in potential_categories:\n", "        parsed = cat_info['parsed']\n", "        title = cat_info['title']\n", "        nesting_level = cat_info['nesting_level']\n", "        score_total = cat_info['score_total']\n", "\n", "        # Check if this category is already a subcategory of ANY potential main category\n", "        # (not just the ones we've already processed)\n", "        # BUT exclude self-references (e.g., \"Past Medical History\" containing \"Past Medical History\")\n", "        is_subcategory_elsewhere, parent_title = _is_subcategory_of_any(\n", "            title, potential_categories)\n", "\n", "        # Special case: if the parent title is the same as the current title,\n", "        # this might be a self-reference, so we need to check if this is the main category\n", "        if is_subcategory_elsewhere and parent_title == title:\n", "            # This is a self-reference case like \"Past Medical History\" containing \"Past Medical History\"\n", "            # Keep the one with the higher score as the main category\n", "            current_score = score_total\n", "\n", "            # Find the parent category's score\n", "            parent_score = 0\n", "            for other_cat_info in potential_categories:\n", "                if other_cat_info['title'] == parent_title and other_cat_info != cat_info:\n", "                    parent_score = other_cat_info['score_total']\n", "                    break\n", "\n", "            if current_score >= parent_score:\n", "                # This category has equal or higher score, so it should be the main one\n", "                is_subcategory_elsewhere = False\n", "\n", "        if not is_subcategory_elsewhere:\n", "            # Add a suffix to distinguish multiple instances of the same title\n", "            if title in final_processed_titles:\n", "                final_processed_titles[title] += 1\n", "                unique_title = f\"{title} ({final_processed_titles[title]})\"\n", "                parsed['title'] = unique_title\n", "            else:\n", "                final_processed_titles[title] = 1\n", "\n", "            main_categories.append(parsed)\n", "\n", "    # Strategy 2: If no main accordions found, look for alternative content structures\n", "    if not main_categories:\n", "        print(\"No main accordions found, looking for alternative content structures...\")\n", "\n", "        # Look for direct checkboxes (flat structure without main headings)\n", "        checkboxes = panel_element.find_elements(\n", "            By.XPATH, \".//input[@type='checkbox']\")\n", "        if checkboxes:\n", "            items = []\n", "            for checkbox in checkboxes:\n", "                name = checkbox.get_attribute('name')\n", "                if name and name.strip():\n", "                    items.append(name.strip())\n", "\n", "            if items:\n", "                # Create a category for direct items\n", "                direct_category = {\n", "                    'title': 'Items',\n", "                    'items': items\n", "                }\n", "                main_categories.append(direct_category)\n", "                # print(f\"Found {len(items)} direct checkbox items\")\n", "\n", "        # Look for nested accordions that might not have a main container\n", "        # (This is now less needed since Strategy 1 handles nested accordions better)\n", "        nested_accordions = panel_element.find_elements(\n", "            By.XPATH, \".//div[contains(@class, 'MuiAccordion-root')]\")\n", "        for accordion in nested_accordions:\n", "            try:\n", "                parsed = parse_accordion(accordion)\n", "                if parsed and parsed.get('title'):\n", "                    # Avoid duplicates from the main accordion search above\n", "                    # Check both exact title match and numbered versions\n", "                    existing_titles = [cat.get('title', '')\n", "                                       for cat in main_categories]\n", "                    base_title = parsed['title']\n", "\n", "                    # Check if this title (or a numbered version) already exists\n", "                    title_exists = any(\n", "                        existing_title == base_title or\n", "                        existing_title.startswith(f\"{base_title} (\")\n", "                        for existing_title in existing_titles\n", "                    )\n", "\n", "                    if not title_exists:\n", "                        main_categories.append(parsed)\n", "            except Exception as e:\n", "                print(f\"Error parsing nested accordion: {e}\")\n", "                continue\n", "\n", "    return main_categories\n", "\n", "\n", "def deduplicate_rubric_data(categories):\n", "    \"\"\"Remove duplicate entries where items appear as both subcategories and standalone items\"\"\"\n", "\n", "    def collect_all_subcategory_titles(cats):\n", "        \"\"\"Collect all titles that appear as subcategories\"\"\"\n", "        titles = set()\n", "        for cat in cats:\n", "            if 'subcategories' in cat:\n", "                for subcat in cat['subcategories']:\n", "                    titles.add(subcat['title'])\n", "                    # Recursively collect from deeper levels\n", "                    titles.update(\n", "                        collect_all_subcategory_titles([subcat]))\n", "        return titles\n", "\n", "    def clean_category(category, all_subcat_titles):\n", "        \"\"\"Clean a single category by removing duplicate items and subcategories\"\"\"\n", "        cleaned = {\n", "            'title': category['title']\n", "        }\n", "\n", "        # Add score if present\n", "        if 'score' in category:\n", "            cleaned['score'] = category['score']\n", "\n", "        # Process subcategories first (recursively)\n", "        if 'subcategories' in category:\n", "            cleaned_subcats = []\n", "\n", "            # Collect titles of subcategories that have their own subcategories\n", "            # BUT exclude self-references (e.g., \"Time Course\" containing \"Time Course\")\n", "            parent_subcats = set()\n", "            for subcat in category['subcategories']:\n", "                if 'subcategories' in subcat:\n", "                    for nested in subcat['subcategories']:\n", "                        # Only add if it's not a self-reference\n", "                        if nested['title'] != subcat['title']:\n", "                            parent_subcats.add(nested['title'])\n", "\n", "            # First, identify the best version of each duplicate title\n", "            title_to_best_subcat = {}\n", "            for subcat in category['subcategories']:\n", "                title = subcat['title']\n", "                if title not in title_to_best_subcat:\n", "                    title_to_best_subcat[title] = subcat\n", "                else:\n", "                    # Compare with existing: prefer the one with subcategories\n", "                    existing = title_to_best_subcat[title]\n", "                    current_has_subcats = 'subcategories' in subcat\n", "                    existing_has_subcats = 'subcategories' in existing\n", "\n", "                    if current_has_subcats and not existing_has_subcats:\n", "                        # Current is better (has subcategories)\n", "                        title_to_best_subcat[title] = subcat\n", "                    elif current_has_subcats and existing_has_subcats:\n", "                        # Both have subcategories, prefer the one with more subcategories\n", "                        if len(subcat.get('subcategories', [])) > len(existing.get('subcategories', [])):\n", "                            title_to_best_subcat[title] = subcat\n", "\n", "            # Now process the best version of each subcategory\n", "            for title, best_subcat in title_to_best_subcat.items():\n", "                # Skip if this title is a subcategory of another subcategory at this level\n", "                if title not in parent_subcats:\n", "                    cleaned_subcat = clean_category(\n", "                        best_subcat, all_subcat_titles)\n", "                    cleaned_subcats.append(cleaned_subcat)\n", "\n", "            if cleaned_subcats:\n", "                cleaned['subcategories'] = cleaned_subcats\n", "\n", "        # Process items - only include items that are NOT subcategory titles\n", "        if 'items' in category:\n", "            # If this category has subcategories, don't include items that belong to subcategories\n", "            if 'subcategories' in cleaned:\n", "                # Collect all items that belong to subcategories\n", "                subcat_items = set()\n", "                for subcat in cleaned['subcategories']:\n", "                    if 'items' in subcat:\n", "                        subcat_items.update(subcat['items'])\n", "                    # Also collect items from deeper subcategories\n", "\n", "                    def collect_deep_items(cat):\n", "                        items = set()\n", "                        if 'items' in cat:\n", "                            items.update(cat['items'])\n", "                        if 'subcategories' in cat:\n", "                            for sc in cat['subcategories']:\n", "                                items.update(\n", "                                    collect_deep_items(sc))\n", "                        return items\n", "                    subcat_items.update(collect_deep_items(subcat))\n", "\n", "                # Only keep items that don't belong to any subcategory\n", "                filtered_items = []\n", "                for item in category['items']:\n", "                    if item not in subcat_items and item not in all_subcat_titles:\n", "                        filtered_items.append(item)\n", "\n", "                if filtered_items:\n", "                    cleaned['items'] = filtered_items\n", "            else:\n", "                # No subcategories, keep all items\n", "                cleaned['items'] = category['items']\n", "\n", "        return cleaned\n", "\n", "    # For the multi-category extraction approach, we want to keep all extracted categories\n", "    # as main categories, even if they appear as subcategories elsewhere\n", "    # This is different from the original single-category approach\n", "\n", "    # First pass: collect all subcategory titles (for internal deduplication within categories)\n", "    all_subcat_titles = collect_all_subcategory_titles(categories)\n", "\n", "    # Second pass: clean each category but keep all extracted main categories\n", "    cleaned_categories = []\n", "    processed_titles = set()\n", "\n", "    for category in categories:\n", "        title = category['title']\n", "        # Process all categories that were explicitly extracted as main categories\n", "        # Don't filter based on subcategory appearance since we want them as main categories\n", "        if title not in processed_titles:\n", "            cleaned = clean_category(category, all_subcat_titles)\n", "            cleaned_categories.append(cleaned)\n", "            processed_titles.add(title)\n", "\n", "    return cleaned_categories\n", "\n", "\n", "def format_rubric_display(categories, indent=0):\n", "    \"\"\"Format rubric data for display\"\"\"\n", "    output = []\n", "    indent_str = \"    \" * indent\n", "\n", "    for category in categories:\n", "        title = category.get('title', 'Unknown')\n", "        score = category.get('score')\n", "\n", "        if score:\n", "            score_str = f\" {score['current']}/{score['total']}\"\n", "        else:\n", "            score_str = \"\"\n", "\n", "        output.append(f\"{indent_str}- {title}{score_str}\")\n", "\n", "        # Add subcategories\n", "        if 'subcategories' in category:\n", "            sub_output = format_rubric_display(\n", "                category['subcategories'], indent + 1)\n", "            output.extend(sub_output)\n", "\n", "        # Add items (checkboxes)\n", "        if 'items' in category:\n", "            for item in category['items']:\n", "                output.append(f\"{indent_str}        {item}\")\n", "\n", "    return output\n", "\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Find all sub-tabs within the marking rubric (HOPC, Assoc., PMHx, etc.)\n", "            medical_history_tabs = panel.find_elements(\n", "                By.XPATH, \".//li[@role='tab']\")\n", "\n", "            # Define the medical history categories we want to process\n", "            target_categories = ['HOPC', 'Assoc.',\n", "                                 'PMHx', 'FMHx', 'Social', 'Basics']\n", "\n", "            all_rubric_data = {}\n", "\n", "            for med_tab in medical_history_tabs:\n", "                med_tab_name = med_tab.text.strip()\n", "\n", "                # Only process the medical history categories\n", "                if med_tab_name not in target_categories:\n", "                    print(f\"Skipping medical history tab: {med_tab_name}\")\n", "                    continue\n", "\n", "                print(f\"\\n===== Processing {med_tab_name} =====\")\n", "\n", "                # Click the medical history tab\n", "                try:\n", "                    med_tab.click()\n", "                    time.sleep(2)  # Wait for content to load\n", "                except Exception as e:\n", "                    print(\n", "                        f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                    continue\n", "\n", "                # Find the associated panel for this medical history tab\n", "                med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                try:\n", "                    med_panel = WebDriverWait(driver, 5).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, med_panel_xpath))\n", "                    )\n", "\n", "                    # Check if the panel has content\n", "                    if not med_panel.text.strip():\n", "                        print(f\"No content found for {med_tab_name}\")\n", "                        all_rubric_data[med_tab_name] = []\n", "                        continue\n", "\n", "                    rubric_data = extract_rubric_data(med_panel)\n", "                    clean_rubric_data = deduplicate_rubric_data(rubric_data)\n", "\n", "                    print(\"\\n===== Marking Rubric (structured) =====\")\n", "                    formatted_output = format_rubric_display(clean_rubric_data)\n", "                    for line in formatted_output:\n", "                        print(line)\n", "\n", "                except Exception as e:\n", "                    print(f\"Could not find panel for {med_tab_name}: {e}\")\n", "                    all_rubric_data[med_tab_name] = []\n", "                    continue\n", "\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": null, "id": "bd9b9d0e-20c8-46a4-b0c2-2a54656842c1", "metadata": {}, "outputs": [], "source": ["import json\n", "import uuid\n", "from datetime import datetime\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "from utils import extract_rubric_data, deduplicate_rubric_data\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-minimized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "all_patient_data = {}\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    page = 0\n", "    last_page = False\n", "    while True:\n", "        i = 0\n", "        if last_page:\n", "            break  # all cards of last page scraped\n", "        while True:\n", "            driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "            # Wait for the scripts page to load by waiting for script cards\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "            )\n", "            for count in range(page):\n", "                next_button = driver.find_element(\n", "                    By.XPATH, \"//button[@aria-label='Go to next page']\")\n", "                if \"disabled\" in next_button.get_attribute(\"class\"):\n", "                    last_page = True\n", "\n", "                next_button.click()\n", "\n", "                # Wait for new page to load by waiting for script cards to refresh\n", "                WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located(\n", "                        (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "                )\n", "\n", "            cards = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"(//div[contains(@class, 'jss308')])\"))\n", "            )\n", "            cards = cards[:1]\n", "\n", "            if i > len(cards) - 1:\n", "                page += 1\n", "                break\n", "\n", "            card = cards[i]\n", "            patient_uuid = uuid.uuid4()\n", "            name = card.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "            actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "            actions.move_to_element(card).perform()\n", "\n", "            # 7. Click \"Open Script\" button that appears\n", "            open_button = WebDriverWait(driver, 10).until(\n", "                EC.element_to_be_clickable(\n", "                    (By.XPATH, f\"(//span[text()='Open Script'])[{i+1}]\"))\n", "            )\n", "            open_button.click()\n", "\n", "            # 8. Wait for tab list to load (using tour-id which is stable)\n", "            tabs = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "            )\n", "\n", "            # Initialize the main data structure\n", "            script_data = {\n", "                \"metadata\": {\n", "                    \"name\": name,\n", "                    \"scraped_at\": datetime.now().isoformat(),\n", "                    \"script_url\": driver.current_url\n", "                },\n", "                \"tabs\": {}\n", "            }\n", "\n", "            # 9. Iterate through each tab\n", "            for tab in tabs:\n", "                # e.g. \"tab-Doctor Information\"\n", "                tab_name = tab.get_attribute(\"tour-id\")\n", "                tab.click()\n", "\n", "                # Wait for the tab content to load by waiting for the active panel\n", "                WebDriverWait(driver, 10).until(\n", "                    EC.presence_of_element_located((\n", "                        By.XPATH,\n", "                        \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                    ))\n", "                )\n", "\n", "                if \"Doctor Information\" in tab_name:\n", "                    active_panel = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located((\n", "                            By.XPATH,\n", "                            \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                        ))\n", "                    )\n", "                    raw_text = active_panel.text\n", "\n", "                    # Structure by headings\n", "                    sections = {}\n", "                    lines = raw_text.split(\"\\n\")\n", "                    current = \"Intro\"\n", "                    sections[current] = \"\"\n", "                    for line in lines:\n", "                        if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                            current = line\n", "                            sections[current] = \"\"\n", "                        else:\n", "                            sections[current] += line + \" \"\n", "\n", "                    # Clean up the sections\n", "                    cleaned_sections = {}\n", "                    for k, v in sections.items():\n", "                        cleaned_sections[k] = v.strip()\n", "\n", "                    script_data[\"tabs\"][\"doctor_information\"] = cleaned_sections\n", "\n", "                elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "                    # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    # Inside that panel, wait for the content container\n", "                    container = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "                    )\n", "\n", "                    # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "                    sections = {}\n", "                    headings = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_all_elements_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "                    )\n", "\n", "                    for heading in headings:\n", "                        title = heading.text.strip()\n", "                        try:\n", "                            ul = heading.find_element(\n", "                                By.XPATH, \"following-sibling::ul[1]\")\n", "                            items = [li.text.strip()\n", "                                     for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                            sections[title] = items\n", "                        except:\n", "                            sections[title] = []\n", "\n", "                    script_data[\"tabs\"][\"script\"] = sections\n", "\n", "                elif \"Marking Rubric\" in tab_name:\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 10).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    medical_history_tabs = panel.find_elements(\n", "                        By.XPATH, \".//li[@role='tab']\")\n", "\n", "                    target_categories = ['HOPC', 'Assoc.',\n", "                                         'PMHx', 'FMHx', 'Social', 'Basics']\n", "                    marking_rubric_data = {}\n", "\n", "                    for med_tab in medical_history_tabs:\n", "                        med_tab_name = med_tab.text.strip()\n", "\n", "                        if med_tab_name not in target_categories:\n", "                            continue\n", "\n", "                        try:\n", "                            med_tab.click()\n", "                            # Wait for the medical tab content to load\n", "                            WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, f\"//div[@role='tabpanel' and @id='{med_tab.get_attribute('aria-controls')}']\"))\n", "                            )\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                            continue\n", "\n", "                        med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                        med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                        try:\n", "                            med_panel = WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, med_panel_xpath))\n", "                            )\n", "\n", "                            if not med_panel.text.strip():\n", "                                print(f\"No content found for {med_tab_name}\")\n", "                                marking_rubric_data[med_tab_name] = []\n", "                                continue\n", "\n", "                            rubric_data = extract_rubric_data(med_panel)\n", "                            clean_rubric_data = deduplicate_rubric_data(\n", "                                rubric_data)\n", "\n", "                            marking_rubric_data[med_tab_name] = clean_rubric_data\n", "\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not find panel for {med_tab_name}: {e}\")\n", "                            marking_rubric_data[med_tab_name] = []\n", "                            continue\n", "\n", "                    script_data[\"tabs\"][\"marking_rubric\"] = marking_rubric_data\n", "\n", "            all_patient_data[patient_uuid] = script_data\n", "            i += 1\n", "\n", "    filename = f\"oscer_data.json\"\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(all_patient_data, f, indent=4, ensure_ascii=False)\n", "    print(f\"data saved to {filename}\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}, {"cell_type": "code", "execution_count": null, "id": "833831a0-c234-43f2-acdb-57303cbc0d05", "metadata": {}, "outputs": [], "source": ["import json\n", "import uuid\n", "import os\n", "import time\n", "import random\n", "from datetime import datetime\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "from utils import extract_rubric_data, deduplicate_rubric_data\n", "\n", "\n", "def human_like_delay(min_seconds=1, max_seconds=3):\n", "    \"\"\"Add random delay to mimic human behavior and avoid rate limiting\"\"\"\n", "    delay = random.uniform(min_seconds, max_seconds)\n", "    print(f\"Waiting {delay:.1f} seconds...\")\n", "    time.sleep(delay)\n", "\n", "\n", "def save_patient_data(patient_uuid, script_data, page_number):\n", "    \"\"\"Save individual patient data to organized folder structure\"\"\"\n", "    # Create the main oscer_cases directory if it doesn't exist\n", "    base_dir = \"oscer_cases\"\n", "    if not os.path.exists(base_dir):\n", "        os.makedirs(base_dir)\n", "\n", "    # Create the page directory if it doesn't exist\n", "    page_dir = os.path.join(base_dir, str(page_number))\n", "    if not os.path.exists(page_dir):\n", "        os.makedirs(page_dir)\n", "\n", "    # Save the patient data as uuid.json\n", "    filename = os.path.join(page_dir, f\"{patient_uuid}.json\")\n", "    with open(filename, 'w', encoding='utf-8') as f:\n", "        json.dump(script_data, f, indent=4, ensure_ascii=False)\n", "\n", "    print(f\"Saved patient data to {filename}\")\n", "\n", "\n", "# Setup Chrome options with anti-detection measures\n", "options = Options()\n", "options.add_argument(\"--start-minimized\")\n", "options.add_argument(\"--disable-blink-features=AutomationControlled\")\n", "options.add_experimental_option(\"excludeSwitches\", [\"enable-automation\"])\n", "options.add_experimental_option('useAutomationExtension', False)\n", "options.add_argument(\n", "    \"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\")\n", "\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "# Execute script to remove webdriver property\n", "driver.execute_script(\n", "    \"Object.defineProperty(navigator, 'webdriver', {get: () => undefined})\")\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 60).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 60).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    page = 0\n", "    last_page = False\n", "    while True:\n", "        i = 0\n", "        if last_page:\n", "            break  # all cards of last page scraped\n", "        while True:\n", "            driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "            # Wait for the scripts page to load by waiting for script cards\n", "            WebDriverWait(driver, 60).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "            )\n", "            for count in range(page):\n", "                next_button = driver.find_element(\n", "                    By.XPATH, \"//button[@aria-label='Go to next page']\")\n", "                if \"disabled\" in next_button.get_attribute(\"class\"):\n", "                    last_page = True\n", "\n", "                next_button.click()\n", "\n", "                # Wait for new page to load by waiting for script cards to refresh\n", "                WebDriverWait(driver, 60).until(\n", "                    EC.presence_of_element_located(\n", "                        (By.XPATH, \"//div[contains(@class, 'jss308')]\"))\n", "                )\n", "\n", "            cards = WebDriverWait(driver, 60).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"(//div[contains(@class, 'jss308')])\"))\n", "            )\n", "            # cards = cards[:1]\n", "\n", "            if i > len(cards) - 1:\n", "                page += 1\n", "                break\n", "\n", "            card = cards[i]\n", "            patient_uuid = uuid.uuid4()\n", "            name = card.find_element(\n", "                By.XPATH, \".//div[contains(@class, 'jss322')]\").text\n", "            actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "            actions.move_to_element(card).perform()\n", "\n", "            # 7. Click \"Open Script\" button that appears\n", "            open_button = WebDriverWait(driver, 60).until(\n", "                EC.element_to_be_clickable(\n", "                    (By.XPATH, f\"(//span[text()='Open Script'])[{i+1}]\"))\n", "            )\n", "            open_button.click()\n", "\n", "            # 8. Wait for tab list to load (using tour-id which is stable)\n", "            tabs = WebDriverWait(driver, 60).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "            )\n", "\n", "            # Initialize the main data structure\n", "            script_data = {\n", "                \"metadata\": {\n", "                    \"name\": name,\n", "                    \"scraped_at\": datetime.now().isoformat(),\n", "                    \"script_url\": driver.current_url\n", "                },\n", "                \"tabs\": {}\n", "            }\n", "\n", "            # 9. Iterate through each tab\n", "            for tab in tabs:\n", "                # e.g. \"tab-Doctor Information\"\n", "                tab_name = tab.get_attribute(\"tour-id\")\n", "                tab.click()\n", "\n", "                # Add small delay after clicking tab\n", "                human_like_delay(0.5, 1.5)\n", "\n", "                # Wait for the tab content to load by waiting for the active panel\n", "                WebDriverWait(driver, 60).until(\n", "                    EC.presence_of_element_located((\n", "                        By.XPATH,\n", "                        \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                    ))\n", "                )\n", "\n", "                if \"Doctor Information\" in tab_name:\n", "                    active_panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((\n", "                            By.XPATH,\n", "                            \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                        ))\n", "                    )\n", "                    raw_text = active_panel.text\n", "\n", "                    # Structure by headings\n", "                    sections = {}\n", "                    lines = raw_text.split(\"\\n\")\n", "                    current = \"Intro\"\n", "                    sections[current] = \"\"\n", "                    for line in lines:\n", "                        if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                            current = line\n", "                            sections[current] = \"\"\n", "                        else:\n", "                            sections[current] += line + \" \"\n", "\n", "                    # Clean up the sections\n", "                    cleaned_sections = {}\n", "                    for k, v in sections.items():\n", "                        cleaned_sections[k] = v.strip()\n", "\n", "                    script_data[\"tabs\"][\"doctor_information\"] = cleaned_sections\n", "\n", "                elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "                    # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    # Inside that panel, wait for the content container\n", "                    container = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "                    )\n", "\n", "                    # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "                    sections = {}\n", "                    headings = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_all_elements_located(\n", "                            (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "                    )\n", "\n", "                    for heading in headings:\n", "                        title = heading.text.strip()\n", "                        try:\n", "                            ul = heading.find_element(\n", "                                By.XPATH, \"following-sibling::ul[1]\")\n", "                            items = [li.text.strip()\n", "                                     for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                            sections[title] = items\n", "                        except:\n", "                            sections[title] = []\n", "\n", "                    script_data[\"tabs\"][\"script\"] = sections\n", "\n", "                elif \"Marking Rubric\" in tab_name:\n", "                    tab_li = tab.find_element(\n", "                        By.XPATH, \"ancestor::li[@role='tab']\")\n", "                    panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "                    panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "                    panel = WebDriverWait(driver, 60).until(\n", "                        EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "                    )\n", "\n", "                    medical_history_tabs = panel.find_elements(\n", "                        By.XPATH, \".//li[@role='tab']\")\n", "\n", "                    target_categories = ['HOPC', 'Assoc.',\n", "                                         'PMHx', 'FMHx', 'Social', 'Basics']\n", "                    marking_rubric_data = {}\n", "\n", "                    for med_tab in medical_history_tabs:\n", "                        med_tab_name = med_tab.text.strip()\n", "\n", "                        if med_tab_name not in target_categories:\n", "                            continue\n", "\n", "                        try:\n", "                            med_tab.click()\n", "                            # Add small delay after clicking medical tab\n", "                            human_like_delay(0.5, 1.0)\n", "                            # Wait for the medical tab content to load\n", "                            WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, f\"//div[@role='tabpanel' and @id='{med_tab.get_attribute('aria-controls')}']\"))\n", "                            )\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not click medical history tab {med_tab_name}: {e}\")\n", "                            continue\n", "\n", "                        med_panel_id = med_tab.get_attribute(\"aria-controls\")\n", "                        med_panel_xpath = f\"//div[@role='tabpanel' and @id='{med_panel_id}']\"\n", "\n", "                        try:\n", "                            med_panel = WebDriverWait(driver, 5).until(\n", "                                EC.presence_of_element_located(\n", "                                    (By.XPATH, med_panel_xpath))\n", "                            )\n", "\n", "                            if not med_panel.text.strip():\n", "                                print(f\"No content found for {med_tab_name}\")\n", "                                marking_rubric_data[med_tab_name] = []\n", "                                continue\n", "\n", "                            rubric_data = extract_rubric_data(med_panel)\n", "                            clean_rubric_data = deduplicate_rubric_data(\n", "                                rubric_data)\n", "\n", "                            marking_rubric_data[med_tab_name] = clean_rubric_data\n", "\n", "                        except Exception as e:\n", "                            print(\n", "                                f\"Could not find panel for {med_tab_name}: {e}\")\n", "                            marking_rubric_data[med_tab_name] = []\n", "                            continue\n", "\n", "                    script_data[\"tabs\"][\"marking_rubric\"] = marking_rubric_data\n", "\n", "            # Save individual patient data to organized folder structure\n", "            save_patient_data(patient_uuid, script_data, page + 1)\n", "            i += 1\n", "\n", "            # Add delay between processing cards to avoid rate limiting\n", "            if i < len(cards):  # Don't delay after the last card\n", "                human_like_delay(2, 5)  # 2-5 second delay between cards\n", "\n", "    print(\"All patient data has been saved to individual files in oscer_cases folder\")\n", "\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}