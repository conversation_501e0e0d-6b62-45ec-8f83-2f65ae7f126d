{"metadata": {"name": "<PERSON>, 55F", "scraped_at": "2025-09-03T00:40:54.100400", "script_url": "https://www.oscer.ai/pwf/script/XIa2BIZMVgrP"}, "tabs": {"doctor_information": {"Intro": "You have been asked to see <PERSON>. <PERSON> is a 55 year old female presenting to your general practice complaining of chest pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["<PERSON> is a 55 y.o. Female (she/her) who presents to your GP practice complaining of recent episodes of chest pain."], "Persona": ["I am normally pretty relaxed and would describe myself as a free spirit. I have been a florist and owned my own shop now for over 20 years!", "I have been having this chest pain for a while now and thought I should finally come and get it seen to."], "HOPC": ["I have noticed a vague pressure in my chest when I go for a walk over the last three months.", "I seem to notice it only after 2 kms. It seems to settle within 10 or so minutes of me stopping.", "It never comes on at rest but it happens every single time I walk more than a couple of kms.", "I feel some shortness of breath when I get the chest pain.", "The feeling is just in my chest and not anywhere else. I don’t have any neck, jaw or arm discomfort.", "I do not have chest pain at rest.", "I have never had palpitations, nausea or vomiting with this.", "I have never lost consciousness.", "At first I put it down to getting older and losing my fitness over the last few years as I just don't have time for exercise with the shop being so busy.", "I recently googled it though and have become a little more concerned and thought it was time I come in to see a <PERSON>."], "PMHx": ["I have a bit of osteoarthritis in my knees from my netball days but I don’t have any real worries! I sometimes take a bit of panadol osteo on the cold days when I feel it the most.", "I do not take any regular prescription or complementary medications.", "I do not have any allergies.", "I am not very good at remembering to get my flu vaccines - it has been a few years since my last one. All my other vaccines should be up to date though!"], "FMHx": ["My dad died of a heart attack when he was 63 which is one of the reasons I thought this was worth checking out.", "<PERSON> had alzheimers and died of pneumonia when she was 80.", "My mum had quite bad anxiety. I think that’s where I get some of my worrying traits from."], "SHx": ["I have been smoking since I was 18. I have had about 1 pack per day during this time.", "I have tried quitting a couple of times. I don’t feel like quitting as I use smoking to manage my stress levels at work.", "I work as a florist. I love my job because it is so calm and peaceful when I am working in the flower shop.", "I drink probably more than I should! I usually have a glass of wine with dinner on weeknights and will go out with friends one night per week. On these nights I might have 5 or 6 glasses of wine.", "I live with my girlfriend of 6 months who only just moved in with me.", "I identify as homosexual.", "My girlfriend and I both work very long hours and get take out 3-4 nights per week. The other nights I will make up quick stir-frys.", "Breakfast is porridge and lunch is usually out somewhere near the courts.", "I exercise by going for walks in the state forest near my house.", "I walk 2-3 kms 3-4 times per week, usually early in the morning before work."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 13, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "Burning", "Ripping", "Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Offset", "Most Recent Episode", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Ideas", "Resting", "Injuries", "Palpating", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Anxious", "score": 1, "items": ["Anxious"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Mental Health History", "score": 1, "items": ["Anxiety", "Asking Generally", "Depression or Low Mood"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Dyslipidaemia", "Asking Generally", "Coronary Artery Disease"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia", "Iron Deficiency"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Arrhythmia", "Heart Disease", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally", "Vegan or Vegetarian"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Patient Identity"]}]}}}