{"metadata": {"name": "<PERSON>, 52M", "scraped_at": "2025-09-05T12:45:29.038943", "script_url": "https://www.oscer.ai/pwf/script/w6zv7HJw7tzy"}, "tabs": {"doctor_information": {"Intro": "You are a medical student based in an Emergency Department. <PERSON> is a 52 year old male who has presented with leg pain. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I’m 52 years old man (he/him) and I work as a sports turf curator.", "I’ve come in to see the doctor because I’ve got pain in my left leg."], "Persona": ["I'm concerned that this could be something serious."], "HOPC": ["I flew back from an overseas turf management convention in the United States 2 days ago, it was a lot of travel. I think one of the flights was 12 hours non-stop! I didn't manage to get up much during the flight, being in the window seat.", "The pain in my leg came on gradually after my flight. It has been constant.", "This morning I woke up and the pain was much worse.", "The pain in my calf is dull, it's getting pretty severe. I'd say it's a 7/10.", "My left calf is swollen, compared to my right. It feels tighter too.", "My left calf is warmer to touch, and the skin around it is a bit darker.", "My calf is quite tender, it hurt when I was trying to massage the cramp out.", "I’ve been hobbling around the last few days, but in fact some light walking eases the pain a little.", "Walking does not increase the pain in my leg.", "I’ve haven’t found anything that makes the pain in my calf better, except elevating my leg, and walking a bit. These don't get rid of the pain, but reduce the severity slightly.", "I’ve been taking paracetamol to try help with the pain, but that’s not doing much.", "I'm concerned something serious is going on, as this has come on out of the blue. I haven't injured myself or anything.", "I don’t have a cough or any shortness of breath.", "I don't have any knee deformities.", "I haven’t had any muscle weakness or changes in sensation.", "I haven’t had a fever.", "I haven’t had any cuts or lesions to my left calf."], "PMHx": ["I haven't been sick recently, nor have I been around any sick contacts.", "I don't have a history of any medical conditions.", "I don’t have any allergies.", "My vaccinations are all up to date.", "I don’t have any regular medications.", "I very occasionally take paracetamol or an ibuprofen if I need it, like the last few days.", "I don’t take any supplements or herbal medications."], "FMHx": ["My mother died when she was 64 years old after a complication from her hip surgery. The doctors said a big clot got stuck in her lung.", "My father is 72 years old, he’s got type 2 diabetes but other than that he’s perfectly well."], "SHx": ["I work for a turf company. We supply turf to a lot of big sporting venues, so there’s lots of driving and travel involved.", "I live at home with my wife <PERSON> and our two children.", "I eat quite well at home because we’ve got the kids. But my job involves a lot of travel, so I don’t eat great when I’m out on the road.", "I do smoke. I know it's a bad habit, and I do want to quit. I'm just not ready for that conversation today.", "I have 15 cigarettes a day I suppose, it’s been like that for the last 30 years.", "I drink about 2 beers a day, when I get home from work.", "I don't do any recreational drugs. I never have.", "I don’t do a whole lot of exercise, but <PERSON> and I normally go for a walk for an hour when I get back from work. It’s our quality time together.", "I weight 95 kgs.", "I guess I’m about 15 kilograms heavier than I should be for my size."]}, "marking_rubric": {"HOPC": [{"title": "Leg <PERSON>", "score": 11, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON><PERSON>", "<PERSON>", "Burning", "Throbbing", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Lower Leg", "Upper Leg", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Injuries", "Immobility", "Context at Onset", "Physical Activity"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["Legs"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Back Pain", "score": 1, "items": ["Back Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Leg Colour", "score": 1, "items": ["Red", "Asking Generally"]}, {"title": "Nail Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Joint Stiffness", "score": 1, "items": ["Joint Stiffness"]}, {"title": "Popliteal Bulge", "score": 1, "items": ["Popliteal Bulge"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Asking Generally", "Urinary Incontinence", "Hesitancy, Intermittency or Dribbling"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Asking Generally", "Faecal Incontinence"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Asking Generally"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Clotting Disorders", "Deep Vein Thrombosis"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Dyslipidaemia", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Oste<PERSON>th<PERSON>is", "Asking Generally", "Clotting Disorders", "Deep Vein Thrombosis"]}], "Social": [{"title": "Social History", "score": 6, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel", "Amount of Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}