{"metadata": {"name": "<PERSON><PERSON><PERSON>, 81M", "scraped_at": "2025-09-05T12:48:07.203698", "script_url": "https://www.oscer.ai/pwf/script/4UwZZdSgkEe3"}, "tabs": {"doctor_information": {"Intro": "You are a medical student on placement in a regional GP clinic. You have been asked to interview <PERSON><PERSON><PERSON>, who is an 81 year old male (he/him) that has presented with dizziness. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON><PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON><PERSON> and I am a 81 year old gentleman (he/him).", "I've come into the clinic because I've been feeling a bit dizzy of late."], "Persona": ["I'm a cheerful old gentleman.", "My biggest concern is being able to get back to my usual duties around the house and at the cricket club."], "HOPC": ["I've had several episodes of feeling dizzy recently.", "The dizzness started 2 weeks ago.", "The dizziness feels like I'm totally light headed and could even topple over.", "It doesn't feel like the room is spinning when I have the dizziness.", "I have't fallen or lost consciousness yet.", "The dizziness is improved by taking a rest, then I eventually come good.", "Walking up the stairs makes the dizziness much worse, which is difficult as I have a two storey house.", "In the last 4 months I've also noticed I've been having to stop and catch my breath while doing housework.", "Now I can't walk up the stairs at home because I get light headed and breathless.", "I've been getting this feeling of chest tightness as well if I do something too strenuous.", "The chest pain is a 3/10 when I walk up steep hills.", "The chest pain feels like a pressure behind my sternum and goes away when I rest.", "The chest pain doesn’t radiate anywhere.", "I'm heavily involved down in the local cricket club, so this is all really getting in the way of the maintenance duties I have down there.", "I feel fatigued in the way of being out of breath, but apart from that my energy levels have been fine.", "I haven't had any troubles with my balance.", "I haven't had any hearing loss or ringing in my ears.", "I have not had any palpitations.", "I have not had any limb swelling.", "I have not had any nausea or vomiting."], "PMHx": ["I was told by a doctor years ago that I had a heart murmur.", "I've never been told that I've had rheumatic fever.", "I don't take any cardiac medications or any other regular medications.", "I take some paracetamol if I have a back niggle or things like that.", "I don't have any allergies.", "My vaccinations are all up to date."], "FMHx": ["My father had some high blood pressure that was diagnosed in his seventies I suppose.", "My mother died when she was 64 years old from breast cancer.", "I have one younger sister, who is healthy and well as far as I know.", "Apart from the high blood pressure, there's no real heart disease in my family."], "SHx": ["I live at home with my wife <PERSON>.", "We have plenty of family support, we have three kids. Two of our children live locally and one lives overseas, but we all talk to each other very frequently.", "The diet isn't as good as it could be. You see, my wife and I lived in a different time where we didn't know how important all the greens were.", "My usual diet is toast for breakfast, a sandwich for lunch, and meat with some carbs for dinner.", "I do a bit of gardening at home, as well as maintenance at my local cricket club, they'd be my main forms of exercise.", "I've never smoked in my life, and I've made sure my children haven't either. It's a filthy habit.", "I do drink alcohol, I started that when I was 18 years old.", "I've never been a heavy drinker, I've drunken similar levels of alcohol for the past 50 years or so.", "Nowadays, I drink one red wine every second night. If my football team is playing, <PERSON> allows me to have two red wines that night.", "I've never used any illicit drugs, they're the only thing worse than smoking."]}, "marking_rubric": {"HOPC": [{"title": "Dizziness", "score": 13, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Vertigo", "Off Balance", "Pre-syncope", "Light-headed", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Walk", "Asking Generally", "Ability to Move Head", "Affecting Daily Life"]}, {"title": "Clinical Markers", "score": 1, "items": ["Falls", "Loss of Consciousness"]}]}, {"title": "Temporal", "score": 6, "subcategories": [{"title": "Time Course", "score": 5, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Patient Concerns", "score": 1, "subcategories": [{"title": "Patient Concerns", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Morning", "Exercise", "Standing", "Moving Head", "Recent Illness", "Context at Onset", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Bleeding", "score": 1, "items": ["Bleeding"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Vision Changes", "score": 1, "items": ["Blurry Vision", "Asking Generally"]}, {"title": "Vomiting Blood", "score": 1, "items": ["Vomiting Blood"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Defecating Blood", "Black and Tarry Stools"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes", "Metabolic Syndrome"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Arrhythmia", "Hypertension", "Dyslipidaemia", "Heart Failure", "Asking Generally", "Coronary Artery Disease"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia", "Iron Deficiency"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Diuretics", "ACE Inhibitor", "Antiarrhythmics", "Asking Generally", "Beta Antagonists", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Heart Surgery", "Asking Generally", "Coronary Interventions"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Heart Surgery", "Asking Generally", "Coronary Interventions"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Heart Failure", "Asking Generally", "Coronary Artery Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Vegan or Vegetarian", "Decreased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Exercise", "Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "Confidentiality"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Gender", "Preferred Name", "Patient Identity"]}]}}}