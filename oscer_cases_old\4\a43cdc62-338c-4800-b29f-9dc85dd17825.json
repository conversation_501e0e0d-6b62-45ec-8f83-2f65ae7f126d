{"metadata": {"name": "<PERSON>, 44M", "scraped_at": "2025-09-03T00:25:51.713387", "script_url": "https://www.oscer.ai/pwf/script/LwHqK6xIhHsF"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 44 year old male (he/him) who has presented to the emergency department complaining of a headache. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I'm a 44 year old man and I work as a landscape gardener.", "I’ve come to the doctor today because I have been suffering repeated headaches, and I can’t bear them anymore."], "Persona": ["I am a pretty cool guy, and I have a lot of friends.", "I enjoy nature and being outside.", "The headaches are starting to get to me, and I really need a solution for them."], "HOPC": ["I have been experiencing the headaches for about 1 week.", "The headaches are always on the right side of my head.", "They feel like they are behind my right eye.", "The headaches feel like an extreme pulsating sensation.", "The pain doesn’t radiate anywhere.", "I experience the headaches approximately 5 times a day.", "Each time I get the headache, it lasts for about 20 minutes.", "Between each episode, I feel like my head is very cloudy.", "These headaches are the most severe pain I have ever felt. I would classify them as a 10 out of 10.", "I can’t think of anything that brings the headaches on.", "Nothing helps to relieve the headaches; I have tried everything.", "Nothing seems to aggravate the headaches, though I don’t see how they could be any worse than they are.", "During the headache episodes, I find that bright lights and sounds really bother me.", "The episodes also make me feel nauseous, and I’ve vomited on 2 occasions. The vomit just looks like normal vomit.", "During the headache episodes, I have also noticed that my right eye becomes red and watery.", "Before they began, I’d never experienced headaches like this before.", "I haven’t hurt myself, experienced any trauma, had any other focal neurological signs.", "I don’t have a fever.", "The headache was not like a thunderclap.", "I do not have any neck stiffness.", "I don’t feel suicidal or like harming myself, but these headaches are getting to me."], "PMHx": ["I’m usually pretty healthy,", "I have no underlying medical conditions.", "I don’t have any allergies that I’m aware of.", "I don’t take any regular medications.", "I’ve never needed to go to the hospital or get a surgery."], "FMHx": ["My father used to get similar headaches.", "Everyone else in my family is healthy.", "There are no medical conditions that run in my family."], "SHx": ["I work as a landscape gardener.", "I mostly work on domestic projects.", "I always wear appropriate PPE when I’m at work.", "I’ve never worked with dangerous chemicals or fumes.", "I live at home with my wife and pet dog.", "We have been married for 15 years. I feel very safe at home.", "I smoke about a pack a day of cigarettes. I have done so for about 25 years.", "I don’t want to quit smoking.", "I drink alcohol.", "I generally drink 5 to 6 beers a night.", "I have been drinking this amount for about 25 years.", "I’ve never used any recreational drugs.", "I eat a fairly balanced diet at home, though I do enjoy a meat pie for lunch at work.", "I don’t do formal exercise, but my job is fairly physical so I think that makes up for it.", "I wouldn’t classify myself as being overweight."]}, "marking_rubric": {"HOPC": [{"title": "Headache", "score": 14, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Tension", "Throbbing", "Thunderclap", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Sided", "Asking Generally", "Generalised or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Eyes", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Diurnal Variation", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Darkness", "Quietness", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Stress", "Injuries", "Patient Ideas", "Light Exposure", "Noise Exposure", "Context at Onset", "Morning or Night-time", "Position or Movements", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Crying", "score": 1, "items": ["Crying"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Seizures", "score": 1, "items": ["Seizures"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Face Pain", "score": 1, "items": ["Face Pain"]}, {"title": "Smell Changes", "score": 1, "items": ["Loss of Smell"]}, {"title": "Neck Stiffness", "score": 1, "items": ["Neck Stiffness"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Blurry Vision", "Asking Generally"]}, {"title": "Nasal <PERSON>harge", "score": 1}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Auditory Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Speech Difficulty", "score": 1, "items": ["Speech Difficulty"]}, {"title": "Sensitive to Light", "score": 1, "items": ["Sensitive to Light"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Agitation", "Irritability"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Shin<PERSON>"]}, {"title": "Neurological Disease History", "score": 2, "items": ["Stroke", "Migraine", "Asking Generally", "Cluster Headaches"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Headache", "Migraine", "Asking Generally", "Cluster Headaches"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}