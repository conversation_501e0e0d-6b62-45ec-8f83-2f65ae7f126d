{"metadata": {"name": "<PERSON>, 72F", "scraped_at": "2025-09-03T00:20:29.832047", "script_url": "https://www.oscer.ai/pwf/script/SPoZi3bYBsYm"}, "tabs": {"doctor_information": {"Intro": "You have been asked to see <PERSON>. <PERSON> is a 72 year old female, presenting to the emergency department with shortness of breath. Please take a history from <PERSON>, with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 72 year old woman and I am a retiree.", "I’ve come to the Emergency Department today because of increasing shortness of breath and a productive cough."], "Persona": ["I’m annoyed that I have had to come into the ED today but I eventually agreed to come in after my neighbor called the ambulance for me.", "I don’t want to have to move into a nursing home, I am determined to stay in my own home."], "HOPC": ["I have been getting increasingly short of breath over the past week.", "I am usually short of breath because of my COPD, but in the past week it has gotten so bad that I am no longer able to shower myself because of my breathlessness.", "I usually have a productive cough of white sputum, but it has turned green over the past week and I seem to be coughing up more than usual.", "The cough is worse in the mornings.", "Most mornings I cough up about a teaspoon or so of thick, green sputum, that’s relatively thick, and then a few smaller coughing fits during the day.", "I have never coughed up any blood.", "I have tried taking my inhalers (fluticasone/salmeterol), as well my salbutamol inhaler but they aren’t helping much.", "I don't know of anyone around me that has been sick.", "I have not noticed any swelling in my limbs.", "I have not had any recent unexpected weight loss, night sweats or fevers."], "PMHx": ["I have COPD that was diagnosed 2 years ago.", "I take fluticasone/salmeterol inhaler twice daily and salbutamol as required for my COPD.", "I have been to hospital for lower respiratory tract infections three times already this year, my last admission was 2 months ago.", "My last respiratory function tests done 2 months ago identified a FEV1 of 45% of predicted values and my functional mobility was 50% of predicted values.", "I also have a history of obesity and depression.", "I have never had surgery.", "I have no allergies.", "I have never had any side effects from my medicines.", "All of my vaccinations are up to date."], "FMHx": ["I have no family history that I can remember.", "I have two children who are alive and well.", "My husband died 1 year ago from old age."], "SHx": ["I am a retired cleaner and I live at home alone.", "I've smoked a pack of cigarettes every day for the past 40 years. I don’t want to quit smoking.", "I don’t drink alcohol, I never have.", "I eat a relatively balanced diet, but I will admit that I have a sweet tooth and probably eat too much.", "I don’t do any exercise because of my shortness of breath.", "I have help at home for cleaning, but I can do everything else like cooking and dressing independently.", "Recently I have been having trouble showering myself because I am getting breathless, but before this week I have been independent with showering.", "I have a past history of depression, but my mood at the moment is pretty good.", "I have no suicidal thoughts or thoughts of self harm.", "My 2 children live close by and check in on me at least once a week."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Onset of Worsening"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Resting", "Night-time", "Positional", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Infectious Contacts", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Swelling", "score": 1}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Voice Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Coughing <PERSON>um", "score": 1, "items": ["Coughing <PERSON>um"]}, {"title": "Coryzal Symptoms", "score": 1, "items": ["Sneezing", "<PERSON><PERSON>", "Nasal <PERSON>harge", "Nasal Congestion"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["Urinating at Night"]}, {"title": "Painful Swallowing", "score": 1, "items": ["Painful Swallowing"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Thyroid Disease History", "score": 1, "items": ["Thyroid Disease"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Pneumonia", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Arrhythmia", "Hypertension", "Asking Generally"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Anaemia"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications", "Blood Pressure Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["COPD", "Lung Cancer", "Heart Disease", "Heart Failure", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Retired", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}