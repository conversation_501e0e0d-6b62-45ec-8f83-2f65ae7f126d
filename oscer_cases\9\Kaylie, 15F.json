{"metadata": {"name": "<PERSON><PERSON>, 15F", "scraped_at": "2025-09-05T12:14:27.614300", "script_url": "https://www.oscer.ai/pwf/script/YAmn3B2fhbIJ"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in general practice. You have been asked to see <PERSON><PERSON>, a 15 year old girl presenting with weight loss. Take a focussed history to determine the most likely diagnosis.", "Requirements": "Please interview <PERSON><PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON><PERSON> and I'm a 15 year old girl. My pronouns are she/her.", "I've come in to the GP because I've been losing weight and I can't tell why.", "My mother is present in the room, I feel very comfortable and safe around her. I can answer any question."], "Persona": ["I am concerned about the weight loss, scared I might have what mum has.", "I know all the details, able to share my story without assistance."], "HOPC": ["The weight loss has been unintentional.", "I noticed my clothes were getting loose, so I decided to monitor my weight on the scales.", "I lost 8 kilograms in the past 4 weeks.", "I have been exhausted for the past 4 weeks.", "The fatigue has been gradual.", "I have been feeling thirstier than usual over the past week.", "I carry around a 1L water bottle and I refill it after every class. I must be having around 4L of water a day.", "I have to sleep with a glass of water on my bedside because I get thirsty throughout the night.", "I have been peeing more than usual over the past week. I have to wake up once a night to go to the bathroom.", "I haven't changed the number of times I urinate, only the amount. There's more volume now.", "I have no pain on urination.", "There is no blood in my urine.", "My bowel habits are normal.", "I have no abdominal pain.", "I don't have any difficulty breathing.", "I don't have any nausea or vomiting.", "I have not had a change in diet or exercise."], "PMHx": ["I have no medical conditions.", "I don't take any medications or supplements.", "I don't have any allergies.", "All my vaccinations are up to date, and mum makes sure I get the flu shot every year."], "FMHx": ["My mother was diagnosed with coeliac disease when she was 24.", "Everyone else is well."], "SHx": ["I have never smoked.", "I have never drunk alcohol.", "I have never used recreational drugs.", "I live at home with my mum and dad, and our dog, <PERSON><PERSON>.", "There are no issues with my mood.", "I eat a balanced diet, including red meat.", "I love soccer. I train three times a week and play a game on the weekends.", "There has been no chance in my exercise. I have been playing soccer for as long as I can remember."]}, "marking_rubric": {"HOPC": [{"title": "Weight Loss", "score": 9, "subcategories": [{"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life", "Ability to Fit Clothes"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Weight Loss"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Amount of Food"]}, {"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Exercise", "Patient Ideas", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Urinary Symptoms", "score": 2, "items": ["Urinary Urgency", "Urinating Blood", "Asking Generally", "Painful Urination", "Urinating at Night"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Purging", "score": 1, "items": ["Purging"]}, {"title": "Thirsty", "score": 1, "items": ["Amount of Fluids", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Headache", "score": 1, "items": ["Headache"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Binge Eating", "score": 1, "items": ["Binge Eating"]}, {"title": "Skin Changes", "score": 1, "items": ["Asking Generally"]}, {"title": "Skin Lesions", "score": 1}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Acetone Breath", "score": 1, "items": ["Acetone Breath"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Asking Generally"]}, {"title": "Appetite Change", "score": 1, "items": ["Asking Generally", "Loss of Appetite", "Increased Appetite"]}, {"title": "Breathing Deeply", "score": 1, "items": ["Breathing Deeply"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Ability to Flush", "Asking Generally"]}, {"title": "Disturbed Body Image", "score": 1, "items": ["Feeling Fat", "Importance of Weight", "Concerned about Body or Weight"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Hopelessness", "Asking Generally", "Lack of Pleasure", "Depression or Low Mood"]}, {"title": "Altered Menstrual Cycle", "score": 1, "items": ["Asking Generally", "Irregular Menstrual Cycle"]}, {"title": "Temperature Intolerance", "score": 1, "items": ["Heat or Cold"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Antidepressants", "Asking Generally", "Past Medications", "Appetite Suppressants"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Thyroid Disease", "Type 1 Diabetes", "Asking Generally"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Urinary Tract Infection"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Coeliac Disease", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Blood Sugar Level"]}]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Coeliac Disease", "Type 1 Diabetes", "Asking Generally", "Eating Disorders", "Autoimmune Disease", "Depression or Low Mood"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Sugary Foods", "Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "Recreational Drug History", "score": 1, "items": ["Cocaine", "User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}