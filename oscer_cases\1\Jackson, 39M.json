{"metadata": {"name": "<PERSON>, 39M", "scraped_at": "2025-09-02T23:55:56.179597", "script_url": "https://www.oscer.ai/pwf/script/2RoipgXN6NAA"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 39-year-old man presenting to the ED with dizziness. You are a junior doctor on your ED rotation and are asked to see <PERSON>. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am 39 years old, and my pronouns are he/him", "I have come to the ED because I am very worried about the dizziness I have been experiencing"], "Persona": ["I am usually a very fit and healthy 39-year-old, I don’t remember the last time I had to see a doctor", "I am worried about this dizziness that I have been experiencing", "I am a professional photographer, working mostly in advertising"], "HOPC": ["I have been experiencing some dizziness for the last 24 hours", "It came on very suddenly yesterday morning", "It lasted about 12 hours, I felt terrible", "Since the first episode, I have had smaller attacks that only last for 20 or 30 seconds", "I have never had anything like this happen before", "The attacks come sometimes when I make sudden movements with my head", "It feels as though the world is spinning", "I have multiple attacks a day", "Nothing seems to help", "Nothing seems to make the attacks worse once they have begun", "I have also noticed some hearing loss in my right ear today", "I just don’t seem to be able to hear as well as usual", "I haven’t noticed any changes in my left ear", "I had a cold about a week ago, I don’t have any upper respiratory tract viral symptoms anymore", "I have not traveled recently", "I was really nauseous yesterday during that long 12-hour attack", "I did not vomit", "I have felt a bit off-balance since the first attack finished", "I feel as though my coordination is a little bit off and my walking is not as smooth", "I have not felt nauseous or vomited since the initial long episode of dizziness", "I do not have a headache", "I have not had any weakness in my arms and legs", "I am not confused", "I do not have a fever", "I haven’t had any neck stiffness", "I haven’t had any sick contacts", "I do not feel confused", "I have not changed my diet recently", "I have not lost any weight recently"], "PMHx": ["I don’t have any medical conditions that I see my GP for", "I have never had any surgery", "I do not take any regular medications", "I don’t have any allergies", "I am up to date with all my vaccinations", "I have never had to come to the hospital"], "FMHx": ["My parents are both alive and well. Dad has high blood pressure but he is still able to manage it with his diet and exercise, while mum has high cholesterol that she takes some tablets for"], "SHx": ["I am a photographer, most of the work that I do is in advertising", "I love what I do, I look forward to coming to work", "I have no real stressors in my life", "I am quite fit I run 3-4 times a week for at least an hour and play some golf on the weekends", "My diet is very good, I am careful with what I eat", "I weigh 84 kilograms and I am 192 centimeters tall; my BMI is around 23", "I have been married for 15 years", "I live at home with my husband Peter", "We do not have any children", "We have two cats, their names are <PERSON> and <PERSON><PERSON>", "I don't smoke cigarettes, I have never smoked", "I am a social drinker, I don’t really drink during the week but have a couple of wines when <PERSON> and I have friends over", "I have never used recreational drugs", "I feel very safe at home, <PERSON> and I have a great relationship", "I would say my mental health is good"]}, "marking_rubric": {"HOPC": [{"title": "Dizziness", "score": 15, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}, {"title": "Clinical Markers", "score": 1, "items": ["Falls", "Loss of Consciousness"]}]}, {"title": "Temporal", "score": 7, "subcategories": [{"title": "Time Course", "score": 6, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Fixation", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Moving Head", "Recent Illness", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Ear Discharge", "score": 1, "items": ["Asking Generally"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Double Vision", "Asking Generally"]}, {"title": "Auditory Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Hearing Loss", "Localising Sound"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Irritability"]}, {"title": "Coordination Difficulty", "score": 1, "items": ["Balance Difficulty", "Coordination Difficulty"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Infectious Mononucleosis"]}, {"title": "Otological Disease History", "score": 1, "items": ["Labyrinthitis", "Ear Infections", "Asking Generally", "<PERSON><PERSON><PERSON>'s Disease", "Benign Paroxysmal Positional Vertigo"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Migraines", "Asking Generally", "Multiple Sclerosis"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Regularity", "Asking Generally", "Past Medications"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Complications", "Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Complications", "Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asking Generally", "<PERSON><PERSON><PERSON>'s Disease", "Multiple Sclerosis", "Benign Paroxysmal Positional Vertigo"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Asking Generally"]}, {"title": "Life Stressors", "score": 1, "items": ["Life Stressors", "Financial Stability", "Mental Health at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Patient Demographics", "score": 1, "items": ["Weight"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}