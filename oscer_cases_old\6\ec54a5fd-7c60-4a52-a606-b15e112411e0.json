{"metadata": {"name": "<PERSON>, 78M", "scraped_at": "2025-09-03T00:39:05.573487", "script_url": "https://www.oscer.ai/pwf/script/pQj4tDBx6Jze"}, "tabs": {"doctor_information": {"Intro": "You are a medical student in outpatients. You are seeing <PERSON>, a 78 year old male, who is complaining of shortness of breath. Please take a history with a view of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 78 year old man (he/him) and I’m retired.", "I’ve come to the doctor today because I’ve been getting more and more short of breath recently."], "Persona": ["I can be quite short and stubborn.", "I have become more concerned about the shortness of breath because it hasn’t gone away.", "It took me a while to come in to the doctors because I wouldn’t admit something wasn’t right."], "HOPC": ["I have been getting short of breath for the past 2 months.", "Initially it was only when I’d be doing something more strenuous like walking up a big hill, but now I’m struggling to take the dog for a walk.", "I used to be able to go for a 30 minute walk every day, but now I can only go for 15 minutes before I feel out of breath.", "I also get episodes where I’m completely dizzy. It’s only walking up stairs or steep hills that do it.", "I’m finding I get more short of breath than I used to when I’m exercising.", "I almost fainted the other day when I stood up from a seat, luckily I grabbed onto the table or else I could have hit my head.", "I’ve only started having these “dizzy” episodes in the past month.", "I’ve also been getting some 3/10 chest pain when I walk up steep hills.", "I’ve been getting this chest pain for the past couple months.", "The chest pain feels like a pressure behind my sternum and goes away when I rest.", "It doesn’t radiate anywhere.", "I have not had any palpitations.", "I have not had any limb swelling.", "I have not had any nausea or vomiting."], "PMHx": ["I have had hypertension for the past 15 years, but it’s all under control the docs tell me.", "I take <PERSON><PERSON><PERSON> daily for my hypertension. I've been taking 5mg daily for more than 15 years now.", "I don’t take any other medications.", "I don’t have any allergies and all my vaccinations are up to date.", "I think I've only been to hospital once for an appendectomy when I was about 6."], "FMHx": ["My dad died of a heart attack when he was 74 and my mum died of old age at 85.", "I’ve got a younger sister, she’s just started on a blood pressure tablet but apart from that she’s always been healthy and well."], "SHx": ["I live by myself at home with my dog.", "My wife passed away 10 years ago from ovarian cancer.", "I have a son and a daughter but they’re all grown up now with families of their own.", "I started drinking when I was 16. I usually have a bottle of beer at night.", "I gave up smoking almost 20 years ago now. I smoked for about 30 years before I quit, it’s one of my regrets in life.", "I used to smoke about 3 packs per week.", "I’ve never taken illicit drugs, alcohol and the smokes were always enough.", "I have been retired for the past 10 years. I used to drive buses for the local council.", "I usually walk my dog everyday but it’s hard at the moment with how short of breath it makes me. It has been bringing on the chest pain too.", "I like to get out on the golf course with a group of friends. I haven’t been recently because I don’t want the guys there to see me like this.", "I’m a fairly simple eater, it’s usually the standard meat and 3 veggies.", "I am completely independent with all my activities of daily living.", "I have never had exposure to silica dust or asbestos."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 10, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Change Over Time", "Onset of Worsening"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Dizziness", "Context at Onset", "Lifestyle Changes", "Physical Activity", "Waking up Short of Breath", "Shortness of Breath when Laying Down", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaw Pain", "score": 1, "items": ["Jaw Pain"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Swelling", "score": 1}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Chest Tightness", "score": 1, "items": ["Chest Tightness"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}]}], "PMHx": [{"title": "Past Medical History", "score": 4, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["Pneumonia", "Asking Generally", "Interstitial Lung Disease"]}, {"title": "Cardiovascular Disease History", "score": 2, "items": ["Arrhythmia", "Hypertension", "<PERSON><PERSON><PERSON><PERSON>", "Heart Failure", "Asking Generally", "Vascular Disease", "Coronary Artery Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Risk and Protective Factors", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Duration", "Adherence", "Asking Generally", "Past Medications"]}]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asthma", "Heart Failure", "Asking Generally", "Respiratory Disease", "Coronary Artery Disease", "Interstitial Lung Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Increased Fluid Intake"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally", "Amount at Baseline"]}, {"title": "Alcohol History", "score": 1, "items": ["Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 2, "items": ["Amount Currently", "Amount in the Past"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Risk and Protective Factors", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Retired", "Occupation", "Specific Role", "Previous Occupation"]}]}]}], "Basics": [{"title": "Basics", "score": 2, "subcategories": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}]}}}