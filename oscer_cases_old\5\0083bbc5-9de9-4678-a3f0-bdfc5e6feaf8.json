{"metadata": {"name": "Douglas, 60M", "scraped_at": "2025-09-03T00:33:35.232441", "script_url": "https://www.oscer.ai/pwf/script/tqODx6qTy3rx"}, "tabs": {"doctor_information": {"Intro": "You are working as a GP in a rural practice. <PERSON>, who is a 60 year old man, has come in presenting with shortness of breath. His long-term <PERSON> is away so he has come to see you. This is your first time seeing him.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I am a 60 year old man (he/him).", "I work as an above ground mining truck operator.", "I identify as an Aboriginal Australian."], "Persona": ["I generally avoid coming to the doctor but the progressive nature of the symptoms I have been having are really worrying me.", "My father died of lung cancer so this also concerns me."], "HOPC": ["Over the last 6 months or so I have been noticing that I get increasingly short of breath when I am doing anything active.", "I get breathless walking up short flights of stairs or sometimes even doing things around the house.", "I am not short of breath when I am sitting around doing nothing or watching TV.", "I also notice a bit of a wheeze sometimes.", "I have also had a pesky cough for the last year that has been gradually getting worse. I bring up a fair bit of whitish mucus, mostly in the mornings.", "I feel like I have gained a bit of weight over the last year as well.", "I have been needing to take my asthma reliever more often than usual.", "I have not done any recent travel and have not been in contact with a known case of COVID.", "I have no leg swelling, orthopnea or nocturnal paroxysmal dyspnoea.", "I have not had any night sweats or fever.", "I do not have any post-nasal drip or allergic rhinitis.", "I have not been coughing up any blood.", "I have not had any chest pain, dizziness, nausea, or palpitations when I feel short of breath.", "I have not had any calf pain."], "PMHx": ["I had chronic ear infections as a kid and now have some hearing difficulties. The noise from working in the mines probably hasn’t helped.", "I have asthma that I take <PERSON><PERSON><PERSON> for occasionally and Seretide. I have high blood pressure. I am meant to take <PERSON><PERSON><PERSON> for it but I often forget.", "I have been diagnosed with depression and also PTSD related to an accident at work. I take <PERSON><PERSON><PERSON> every day.", "I have been taking Champix for the past year in an attempt to quit smoking. It seems to be working for me.", "I have never been hospitalised for anything and I've never had any surgeries.", "I don't take anything over the counter or any supplements.", "I have a mild pollen allergy.", "My vaccinations should be all up to date."], "FMHx": ["My father died of lung cancer when he was 54 years old.", "My mother died of a heart attack at 70.", "I have no siblings."], "SHx": ["I work in the tin mines as an underground truck operator. I enjoy the work and the roster allows me to spend plenty of  time with my family. It is dangerous work though so I can find it stressful at times. I have worked here for 5 years. Before this I worked on a cattle farm.", "I always wear full PPE at work and a mask.", "I am happily married to my wife of 30 years and we have two adult children, who both moved to the city to study and work there now. Everyone is pretty healthy.", "I quit smoking one year ago and have been taking Champix since. Before then, I was smoking 20-30 cigarettes per day and had been since I was about 16.", "I wanted to quit smoking because my father died of lung cancer around this age.", "I don’t drink alcohol and only very occasionally drank as a teenager and young adult.", "I have never used any recreational drugs.", "I try to eat a healthy and balanced diet with a mix of meat, vegies and fruits.", "I don't really exercise outside of work because work is hard enough."]}, "marking_rubric": {"HOPC": [{"title": "Shortness Of Breath", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10", "Exercise Capacity"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Positional", "Context at Onset", "Physical Activity", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Sweating", "score": 1, "items": ["Sweating"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Night Sweats", "score": 1, "items": ["Night Sweats"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}, {"title": "Waking up Short of Breath", "score": 1, "items": ["Waking up Short of Breath"]}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Inhalers", "Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "General History", "score": 2, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Covid-19", "Current Vaccination Status"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally", "Previous Hospitalisations"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Influenza", "Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Asthma", "Cancer", "Asking Generally", "Respiratory Disease"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 2, "items": ["Smoking Status", "Amount Currently", "Amount in the Past"]}, {"title": "Infectious Contacts", "score": 1, "items": ["<PERSON><PERSON>", "Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Specific Role"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}