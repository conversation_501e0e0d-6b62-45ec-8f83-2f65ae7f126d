{"metadata": {"name": "<PERSON>, 61M", "scraped_at": "2025-09-03T11:57:03.355777", "script_url": "https://www.oscer.ai/pwf/script/OhQy3cxRyuEk"}, "tabs": {"doctor_information": {"Intro": "You are working in a 24 hour GP clinic. Your next patient is <PERSON>. He is a new patient at your practice and he presents with toe pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>. I am a 61 year old male.", "I identify as male. My prefered pronound are he/his.", "I work as a concreter.", "I have come in today with an incredible painful toe. I have been putting up with it since early this morning but has gotten worse and worse throughout the day. Now I can't sleep."], "Persona": ["I am quietly spoken. You might need to ask specific questions as I am not particuarly forthecoming."], "HOPC": ["The pain is in my right big toe. It started about 10am this morning and has progressively gotten worse throughout the day.", "The pain is so bad at the moment that it is excrutiating even when my toe touches the sheets. If I had to rate the pain, I would rate the pain 10 out of 10.", "My toe is swollen and red.", "I have not had this pain before.", "I do not have any fevers or chills.", "I have no other painful or swollen joints.", "I have no lower back pain and have not had issues with that in the past.", "<PERSON><PERSON><PERSON><PERSON> and Panado<PERSON> have not seemed to get on top of the pain."], "PMHx": ["I have been diagnosed with high blood pressure but I am trying to control it with diet.", "I had surgery one week ago for a wisdom tooth extraction. There were no complications.", "I do not have any other diagnosed medical conditions."], "FMHx": ["My dad had a heart attack but that was when he was 90. He was first diagnosed with heart troubled when he was in his 70s.", "My mum has COPD but has smoked most of her life.", "My aunty has schizophrenia.", "I do not have any siblings.", "I am not aware of any other medical conditions in the family."], "SHx": ["I live with my wife, <PERSON>. We are both still working full-time.", "I work as a concreter.", "I drink maybe a carton (24 beers) a week. I drink on Saturday and Sunday only (about half a carton per day).", "I do not smoke and never have.", "I am trying to eat a healthy diet at the moment as the GP is trying to control my blood pressure. I have been pretty good with it.", "I do not exercise bery much but my work is very active."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON><PERSON>", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["<PERSON>", "Deformities", "Asking Generally", "Stiffness or Dull"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 3, "subcategories": [{"title": "Qualitative", "score": 2, "items": ["Ability to Sleep", "Asking Generally", "Ability to do Physical Activity", "Ability to Palpate or Wear Shoes"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 3, "subcategories": [{"title": "Time Course", "score": 2, "items": ["Onset", "Duration", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Resting", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Resting", "Injuries", "Context at Onset", "Morning or Night-time", "Palpating or Wearing Shoes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "<PERSON><PERSON>ing", "score": 1, "items": ["<PERSON><PERSON>ing"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Nail Changes", "score": 1, "items": ["<PERSON><PERSON><PERSON>", "Pitting", "Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Toe Deformity", "score": 1, "items": ["Tophus"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Malignancy History", "score": 1, "items": ["Lung Cancer", "Osteosarcoma", "Breast Cancer"]}, {"title": "Past Medical History", "score": 1, "items": ["Chlamydia", "Recent Illness", "Previous Hospitalisations"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Osteoporosis"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Infectious Disease History", "score": 1, "items": ["HIV"]}, {"title": "Dermatological Disease History", "score": 1, "items": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Coeliac Disease", "Asking Generally", "Vitamin D Deficiency", "Inflammatory Bowel Disease"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Asking Generally"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Gout", "<PERSON><PERSON><PERSON><PERSON>", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Psoriatic Arthritis", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Seafood", "Meat-Rich Diet", "Asking Generally"]}, {"title": "Exercise", "score": 1, "items": ["Amount Currently", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexual Partners", "Sexually Active"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}