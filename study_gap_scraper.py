import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager


def setup_driver():
    """Setup Chrome driver with anti-detection measures"""
    options = Options()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    options.add_argument("--ignore-ssl-errors")

    driver = webdriver.Chrome(service=Service(
        ChromeDriverManager().install()), options=options)

    # Execute script to remove webdriver property
    driver.execute_script(
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver


def login_to_oscer(driver):
    """Login to the Oscer platform"""
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. Wait for login to complete
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )


def get_study_gap_patients(driver):
    """Extract patient names from Study Gap section"""
    study_gap_patients = []

    try:
        # Wait for Study Gap section to be present
        study_gap_section = WebDriverWait(driver, 5).until(
            EC.presence_of_element_located(
                (By.XPATH, "//div[contains(text(), 'Study Gap')]//ancestor::div[@data-tut='false']"))
        )

        # Find all patient cards in the Study Gap section
        patient_cards = study_gap_section.find_elements(
            By.XPATH, ".//div[contains(@class, 'jss736')]")

        for card in patient_cards:
            patient_name = card.text.strip()
            if patient_name:  # Only add non-empty names
                study_gap_patients.append(patient_name)

    except TimeoutException:
        print("No Study Gap section found for this category")
    except Exception as e:
        print(f"Error extracting Study Gap patients: {e}")

    return study_gap_patients


def main():
    driver = setup_driver()

    try:
        # Login to the platform
        login_to_oscer(driver)

        # Navigate to home page where categories are
        driver.get("https://www.oscer.ai/dashboard/home")

        print("\n=======Waiting for page to load=======\n")
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//input[@placeholder='System']"))
        )
        print("\n=======Page Loaded=======\n")
        patients = get_study_gap_patients(driver)
        print(f"Total Patients: {len(patients)}")

    finally:
        input("Press Enter to close the browser...")
        driver.quit()


if __name__ == "__main__":
    main()
