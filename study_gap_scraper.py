import json
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager


def setup_driver():
    """Setup Chrome driver with anti-detection measures"""
    options = Options()
    options.add_argument("--start-maximized")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option('useAutomationExtension', False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    options.add_argument("--ignore-ssl-errors")

    driver = webdriver.Chrome(service=Service(
        ChromeDriverManager().install()), options=options)

    # Execute script to remove webdriver property
    driver.execute_script(
        "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    return driver


def login_to_oscer(driver):
    """Login to the Oscer platform"""
    # 1. Open the sign-in page directly
    driver.get("https://www.oscer.ai/signin")

    # 2. Fill in credentials
    email_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "email"))
    )
    password_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )

    email_input.clear()
    email_input.send_keys("<EMAIL>")
    password_input.clear()
    password_input.send_keys("techrise01badoscer")

    # 3. Click the "Sign in" button
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable(
            (By.XPATH, "//button[.//span[text()='Sign in']]"))
    )
    login_button.click()

    # 4. Wait for login to complete
    WebDriverWait(driver, 20).until(
        EC.url_contains("/dashboard")
    )


def get_all_patients(driver):
    """Extract all patient names from all sections/categories"""
    all_patients = []

    try:
        # Wait for the page to load and find all patient cards across all sections
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//input[@placeholder='System']"))
        )

        # Find all patient cards on the page (regardless of section)
        patient_cards = driver.find_elements(
            By.XPATH, "//div[contains(@class, 'jss736')]")

        print(f"Found {len(patient_cards)} patient cards on the page")

        for card in patient_cards:
            patient_name = card.text.strip()
            if patient_name:  # Only add non-empty names
                all_patients.append(patient_name)
                print(f"Patient: {patient_name}")

    except TimeoutException:
        print("Timeout waiting for page elements")
    except Exception as e:
        print(f"Error extracting patients: {e}")

    return all_patients


def main():
    driver = setup_driver()

    try:
        # Login to the platform
        login_to_oscer(driver)

        # Navigate to home page where categories are
        driver.get("https://www.oscer.ai/dashboard/home")

        print("\n=======Waiting for page to load=======\n")
        # Wait for page to load
        WebDriverWait(driver, 10).until(
            EC.presence_of_element_located(
                (By.XPATH, "//input[@placeholder='System']"))
        )
        print("\n=======Page Loaded=======\n")
        patients = get_all_patients(driver)
        print(f"\n=======RESULTS=======")
        print(f"Total Patients Found: {len(patients)}")
        print(f"======================\n")

    finally:
        input("Press Enter to close the browser...")
        driver.quit()


if __name__ == "__main__":
    main()
