{"metadata": {"name": "Constantine, 45M", "scraped_at": "2025-09-03T00:06:38.129745", "script_url": "https://www.oscer.ai/pwf/script/0DCdvP1y4GHO"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 45 year old male, presenting to the emergency department with abdominal pain. Please take a history from Constantine with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I am a 45 year old man and I work as an accountant. My pronouns are he/him.", "I have come to the hospital emergency department due to a pain in my upper abdomen which is quite severe."], "Persona": ["I am frustrated that I am in pain and do not like being in the hospital.", "I do not think this pain is anything serious.", "I just want something to fix the pain so I can go back home."], "HOPC": ["The pain is in my epigastric region and radiates to my back.", "The pain is sharp in quality.", "The pain is very severe, about an 8 out of 10.", "The pain began this evening quite suddenly and has been getting worse since it started.", "The pain is constant.", "I’m not sure what caused it but eating dinner made the pain worse.", "Leaning forwards makes the pain feel better.", "I feel nauseous and I have vomited twice.", "The vomit looks like normal vomit. There has not been any blood in my vomit.", "I do not have an appetite right now.", "I am not jaundiced.", "I have normal bowel motions which have been normal leading up to the illness.", "I have no dyspnoea or chest pain.", "I do not have a fever.", "I have not been feeling particularly fatigued.", "My urine has been a normal colour."], "PMHx": ["I do not really go to the doctor but I don't have any health issues that I know of.", "I take no medications including over the counter medications.", "I have no allergies.", "My blood pressure and cholesterol has never been a problem.", "I have never been diagnosed with any liver problems.", "I have never had gall stones before.", "I have not had my gallbladder removed."], "FMHx": ["My father had a heart attack at age 63. He is still alive though.", "My mother has type 2 diabetes and takes insulin.", "I have two sisters and one brother who are healthy."], "SHx": ["I drink 3 to 4 standard drinks every night and have been doing so for over 15 years", "I occasionally drink up to 7 standard drinks approximately once per month. I drank approximately this much this evening.", "I do not smoke and never have.", "I do not take any recreational drugs and never have.", "I live with my wife and daughter at home. I am happy with my living situation.", "I work as an accountant and I enjoy my job. I do not find it too stressful.", "My diet is pretty healthy. I try to eat a mix of things and do not eat out much. It often ends up being whatever my wife cooks for dinner."]}, "marking_rubric": {"HOPC": [{"title": "Abdominal Pain", "score": 13, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Burning", "Ripping", "Cramping", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Location at Onset"]}, {"title": "Radiation", "score": 1, "items": ["Back", "Chest", "Flank", "Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Vomiting", "Medications", "Asking Generally", "Positional or Movements", "Defecating or Flatulating"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Ideas", "Stress", "Injuries", "Palpating", "Eating or Diet", "Context at Onset", "Lifestyle Changes", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 8, "subcategories": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Chest Pain", "score": 1, "items": ["Chest Pain"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "<PERSON>le Stools", "Asking Generally", "Defecating Blood", "Malodorous Stools", "Black and Tarry Stools", "Most Recent Bowel Movement"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Gallstones", "Asking Generally", "Primary Sclerosing Cholangitis"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Bowel Cancer", "<PERSON><PERSON><PERSON>'s Disease", "Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Diuretics", "Antibiotics", "Cephalosporins", "Asking Generally"]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Abdominal Surgery", "Endoscopic Retrograde Cholangiopancreatography"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Abdominal Surgery", "Endoscopic Retrograde Cholangiopancreatography"]}], "FMHx": [{"title": "Family History", "score": 2, "items": ["Cancer", "Carcinoma", "Pancreatitis", "Asking Generally"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently", "Amount at Baseline"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount in the Past"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Built Rapport", "score": 1, "items": ["Built Rapport"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}