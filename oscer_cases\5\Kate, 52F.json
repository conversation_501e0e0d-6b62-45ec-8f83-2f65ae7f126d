{"metadata": {"name": "Kate, 52F", "scraped_at": "2025-09-03T00:27:41.070585", "script_url": "https://www.oscer.ai/pwf/script/m5ORLlDEpE5g"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 52 year old female, who has been brought to the emergency department by ambulance, complaining of a headache. You are the first doctor to see <PERSON>. Please take a history with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I'm a 52 year old woman and I work as an accountant.", "I've been brought to the Emergency Department today following an excruciating headache that I had at home."], "Persona": ["I'm a fairly anxious person.", "I am really worried that this could be something serious.", "I am concerned that if something happens to me that my kids will grow up without a mum."], "HOPC": ["I have been experiencing an excruciating headache for the past 1 hour.", "I was cooking dinner when it came on very suddenly.", "Within a few minutes it had reached a maximum level of pain.", "The pain has gotten a bit better since then but it is still a 10/10.", "It is the worst pain I have ever felt in my life.", "The pain feels like it is throbbing in nature.", "The pain feels generalised in location but feels particularly bad at the back of my head and neck.", "I have also noticed some neck stiffness.", "In the last 20 minutes I have been feeling very nauseous, but have not vomited.", "I have not noticed any visual changes but I have noticed that I am sensitive to light and it makes my headache worse.", "I don't have any other neurological symptoms such as weakness or sensory changes.", "I have not been unwell recently."], "PMHx": ["I was diagnosed with hypertension a few years ago.", "I take <PERSON><PERSON><PERSON><PERSON> to try and control my high blood pressure.", "I haven't had any surgeries or other procedures.", "The last time I was in hospital was when my youngest child was born.", "I don't have any allergies that I'm aware of.", "I don't have a diagnosis of any neurological conditions."], "FMHx": ["My mother gets really bad migraines.", "Apart from mum there isn't really other significant history in our family that I can think of.", "No one in my family has had anything like this.", "No one in my family has had an aneurysm.", "I have two children who are healthy and well."], "SHx": ["I have been working as an accountant for the past 30 years.", "I am happily married to my husband with who I have two kids.", "I currently smoke a pack of cigarettes every week and have been doing this for the last 5 years.", "5 years ago I would smoke a pack a day and had been smoking 1 pack a day for 25 years.", "I drink one glass of wine every weekend.", "I have never taken any recreational or illicit drugs.", "My diet is healthy and mostly centred around vegetables and fruits.", "Every few days or so I go for a 20 minute walk around the block.", "I am not overweight or obese.", "I have no pets at home."]}, "marking_rubric": {"HOPC": [{"title": "Headache", "score": 15, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Character", "score": 2, "items": ["Tension", "Throbbing", "Thunderclap", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Sided", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Darkness", "Quietness", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 4, "items": ["Injuries", "Palpating", "Patient Ideas", "Light Exposure", "Noise Exposure", "Context at Onset", "Lifestyle Changes", "Position or Movements", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Motor Changes", "Asking Generally"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Seizures", "score": 1, "items": ["Seizures"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Neck Stiffness", "score": 1, "items": ["Neck Stiffness"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Blurry Vision", "Asking Generally"]}, {"title": "Sensory Changes", "score": 1, "items": ["Sensory Changes"]}, {"title": "Speech Difficulty", "score": 1, "items": ["Speech Difficulty"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Agitation", "Confusion", "Drowsiness", "Irritability"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Stroke", "Migraine", "Asking Generally", "Cluster Headaches"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally"]}, {"title": "Haematological Disease History", "score": 1, "items": ["Bleeding Disorders"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally", "Marfan's Syndrome"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Stroke", "Headache", "Asking Generally", "Bleeding Disorders", "Connective Tissue Disorders"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Alcohol History", "score": 1, "items": ["Drinking Status"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["IV Drug Use", "User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}