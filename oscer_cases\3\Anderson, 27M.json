{"metadata": {"name": "<PERSON>, 27M", "scraped_at": "2025-09-03T00:13:35.679317", "script_url": "https://www.oscer.ai/pwf/script/IACfzA2ENxdr"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 27 year old male presenting to the general practice clinic complaining of chest pain. Please take a history from <PERSON> with an aim to establish a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "I’m a 27 year old man and I work as a financial consultant in the city. My pronouns are he/him."], "Persona": ["I'm very worried something is wrong with my heart.", "I've never had pain like this in my life."], "HOPC": ["I am currently having very bad chest pain.", "I first noticed the chest pain when I was brushing my teeth last night.", "I can't think of anything that would have caused it.", "The pain has been very constant, it's been about 12 hours or so now.", "The chest pain feels sharp, it feels like someone is stabbing me.", "The chest pain is in the centre of my chest.", "I am also getting some pain in my jaw and my shoulder.", "It’s the worst pain I’ve ever experienced, I would rate the chest pain as an 8 out of 10.", "I barely slept last night as the pain was so bad, I had to sleep sitting up.", "Lying down makes the pain worse and sitting up and leaning forward makes the pain better.", "Taking a deep breath also makes the pain worse.", "The chest pain doesn’t seem to be related to exercise.", "I think I might have a bit of a fever on and off over the past day or so.", "I haven't had any weight loss.", "I've been feeling a bit off the past day or so, I haven't had the best appetite but I'm still eating. I have not had any nausea or vomiting.", "I've had a few aches and pains the past day or so, just feeling pretty run down.", "I don’t have any other symptoms.", "I have not injured myself recently or had any chest trauma.", "I don't have a cough.", "I do not have any palpitations.", "I do not have a fever", "I have not experienced any night sweats."], "PMHx": ["I don't have any medical conditions, I'm usually healthy.", "I've never had to go to hospital and I’ve never had any surgery.", "I don’t take any prescription medications.", "I occasionally take over the counter pain relief such as panadol and nurofen when I have a headache.", "I don’t have any allergies that I am aware of.", "I am up to date on all of my vaccinations."], "FMHx": ["Dad has type 2 diabetes, he got diagnosed at 57 years old. Mum's got osteoarthritis, it was diagnosed when she was 60 years old.", "I don’t know of anyone in the family who had any heart problems.", "The rest of my family are very healthy."], "SHx": ["I don't smoke, and I can't stand the smell of cigarettes.", "1 or 2 afternoons a week I'll have maybe 1 or 2 drinks after work, and 6 or 7 drinks on a night every weekend.", "I've experimented with a few recreational drugs.", "I often take cocaine on a night out, and occasionally I take MDMA.", "I've never injected drugs.", "I was born in China, but I moved here when I was 14.", "I travel to China every year to see family, my last trip was about 6 months ago.", "I work in a big office in the city.", "I think one of my colleagues at work had a mild cold the other week, it's been going around the office.", "I have a pretty balanced diet.", "I usually like to exercise every day for an hour, but this chest pain is making that impossible."]}, "marking_rubric": {"HOPC": [{"title": "Chest Pain", "score": 12, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Asking Generally", "Heavy, Tight or Pressure"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Asking Generally", "Central or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Neck or Jaw", "Arm or Shoulder", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Ability to Sleep", "Asking Generally", "Affecting Daily Life", "Ability to do Physical Activity"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 3, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Positional", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 2, "items": ["Ideas", "Injuries", "Context at Onset", "Physical Activity", "Positional or Movements", "Laughing, Coughing or Deep Breathing", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Wheeze", "score": 1, "items": ["Wheeze"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "Leg <PERSON>", "score": 1, "items": ["Leg <PERSON>"]}, {"title": "Swelling", "score": 1}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Regurgitation", "score": 1, "items": ["Regurgitation"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Swallowing Difficulty", "score": 1, "items": ["Swallowing Difficulty"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally", "Previous Hospitalisations"]}, {"title": "Thrombophilia History", "score": 1, "items": ["Clotting Disorders", "Pulmonary Embolism"]}, {"title": "Respiratory Disease History", "score": 1, "items": ["COPD", "Asthma", "Asking Generally"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "<PERSON><PERSON><PERSON><PERSON>", "Asking Generally", "Coronary Artery Disease"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Antibiotics", "Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Heart Disease", "Asking Generally", "Pulmonary Embolism", "Gastro-Oesophageal Reflux Disease"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Travel History", "score": 1, "items": ["Recent Travel"]}, {"title": "Alcohol History", "score": 2, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Infectious Contacts", "score": 1, "items": ["Asking Generally"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}