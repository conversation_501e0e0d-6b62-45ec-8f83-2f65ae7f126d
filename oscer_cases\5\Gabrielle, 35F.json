{"metadata": {"name": "<PERSON>, 35F", "scraped_at": "2025-09-03T00:26:28.960162", "script_url": "https://www.oscer.ai/pwf/script/RKnhZpe0lvZW"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 35 year old mother of two, who has presented to general practice today with a headache. Please take a history from <PERSON> with the aim of developing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON> and I’m 35 years old. My pronouns are she/her.", "I feel like I’ve got two jobs. I’m a teacher, but I’m also a mother to my two boys <PERSON> and <PERSON>, they’re 7 and 5 years old.", "I’ve come in today because I’ve got this headache that’s been going on for the past 2 days."], "Persona": ["This headache is really the last thing I need, I’ve got so much else on my plate.", "I’m a single mother and a teacher for year 11 and 12 students, I just can’t concentrate properly with this headache, and I’m getting irritable with the boys and my students."], "HOPC": ["I’ve got this headache through this region of my head, right across my forehead on both sides.", "The headache doesn’t radiate anywhere else.", "If I had to describe it, it feels like a constant pressure, as if my head were being squeezed in a vice.", "The headache has been a 5 out of 10.", "Moving about has been making it worse, which is why I really need to get on top of it. I’m a teacher and a mother, so I’m constantly on the move.", "I’ve had episodes of this same headache before, it usually happens in the afternoon when I’ve had a big, stressful period at work.", "I normally get them maybe once or twice a month, but recently it’s been happening every week.", "I’ve used paracetamol for the headache, and it’s been helping a little bit.", "It’s coming up to parent teacher interviews, so I’ve been working non-stop to get the reports for all the students done. In between, I’m juggling looking after <PERSON> and <PERSON>, it’s just a mess at the moment.", "I’m not sleeping very well at all because of the headache.", "I’ve been looking at the computer so much doing these reports, I feel like I’m going to need glasses soon the way I’m straining my eyes!", "I’ve noticed that if I miss one of my coffee fixes a headache can start up, I’m living off the stuff at the moment, I’m probably having 4 a day.", "The headache is making me feel a little bit nauseous, but it’s just the pain and the stress causing it I think.", "I haven’t felt feverish, or any neck stiffness.", "I’m not extra sensitive to lights or sounds at the moment.", "I haven’t had any head trauma.", "I’ve had no problems with sensation and movement anywhere, I wish I did that way I could slow down a little."], "PMHx": ["I had my appendix out when I was 14.", "I’m not taking any prescription medications.", "I take a women’s daily multivitamin, and paracetamol whenever I get a headache flare but that’s it.", "I don’t have any allergies.", "All my vaccinations are up to date, it’s part of the requirement for teaching at our school."], "FMHx": ["My Dad has COPD, but there’s no migraine or headache history in my family.", "I also do not believe there are any other medical problems in the family."], "SHx": ["It’s just the boys and me at home, their Father left just after <PERSON> was born and he’s never around.", "Our diet is fine. It’s a struggle to get the boys to eat properly sometimes but we do our best.", "I have a group of girlfriends I go running with, but apart from that I don’t have much time by myself, let alone to exercise.", "I know it’s terrible but I do smoke. It’s my little guilty pleasure and stress release, I keep it a secret from the boys.", "I smoke maybe 5 or 10 cigarettes a day. I picked it up probably around 5 years ago after the boy’s father left.", "I tried a couple of cigarettes when I was a teenager, but didn’t get into it back then.", "I have a glass or two of chardonnay at night, it’s another way I find I can unwind.", "I’ve never touched any recreational drugs.", "I work as a Media and English teacher for students in their final years, that’s why I find it can be so stressful.", "I'm finding work really stressful at the moment. The students are coming up to their final exams, so they're all wanting extra help. And in between I'm snowed under with report writing. Amongst all of this, my youngest <PERSON> is having some behavioural issues at kindergarten as well.", "I haven’t travelled anywhere recently, not with the boys. Although I did just get back from a big essay marking forum in the city, that was a 5 year round trip to get there in the car."]}, "marking_rubric": {"HOPC": [{"title": "Headache", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["Tension", "Throbbing", "Thunderclap", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Sided", "Asking Generally", "Generalised or Localised"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Worst Ever", "Asking Generally"]}, {"title": "Quantitative", "score": 1, "items": ["Scale out of 10"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 5, "subcategories": [{"title": "Relieving Factors", "score": 2, "items": ["Darkness", "Quietness", "Medications", "Asking Generally", "Position or Movements"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Alcohol", "Caffeine", "Injuries", "Patient Ideas", "Light Exposure", "Noise Exposure", "Context at Onset", "Lifestyle Changes", "Morning or Night-time", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON>ra", "score": 1, "items": ["Asking Generally"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Crying", "score": 1, "items": ["Crying"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Fatigue"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Eye Pain", "score": 1, "items": ["Eye Pain"]}, {"title": "Jaw Pain", "score": 1, "items": ["Jaw Pain"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Vision Changes", "score": 1, "items": ["Vision Loss", "Double Vision", "Asking Generally"]}, {"title": "Nasal <PERSON>harge", "score": 1}, {"title": "Nasal Congestion", "score": 1, "items": ["Nasal Congestion"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Agitation", "Irritability"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Asking Generally"]}, {"title": "Neurological Disease History", "score": 1, "items": ["Migraine", "Cluster Headaches"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Hypertension", "Asking Generally"]}, {"title": "Connective Tissue Disorder History", "score": 1, "items": ["Asking Generally"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Headache", "Migraine", "Asking Generally", "Cluster Headaches"]}], "Social": [{"title": "Social History", "score": 4, "subcategories": [{"title": "Life Stressors", "score": 1, "items": ["Current Stress", "Life Stressors", "Financial Stability"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "COVID-19 History", "score": 1, "items": ["COVID Exposure", "COVID Vaccination"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Conclusion", "score": 1, "items": ["Conclusion"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Patient Identity"]}]}}}