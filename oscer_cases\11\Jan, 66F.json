{"metadata": {"name": "Jan, 66F", "scraped_at": "2025-09-05T12:57:45.875413", "script_url": "https://www.oscer.ai/pwf/script/0jyvfQJId759"}, "tabs": {"doctor_information": {"Intro": "You are a student on placement at a GP clinic. Your supervising doctor has asked you to take a history from <PERSON>. <PERSON> is a 66 year-old female that presents with pale stools. Take a history to determine the most likely diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["I've come to the GP clinic because I've noticed my poos are quite pale lately.", "My name is <PERSON>. I'm 66 years young!"], "Persona": ["I'm fiercly independent and lead a very fulfilled life.", "I am a quirky personality and love to be surrounded by good people."], "HOPC": ["I've noticed that my poos are a bit pale, and probably a bit sloppier nowadays. They have been like this for about 3 weeks.", "I'm needing to poo twice a day now. Previously, I would only go once.", "At first I thought it was just something I ate! However, it isn't getting any better and I seem to have pale poos constantly.", "My poos look as if they are a bit oily too!", "Interestingly, I've also noticed that my wee is a lot darker, no matter how much water I seem to drink...", "My wee has been darker for about 3 weeks too. Otherwise, my weeing is normal. I'm going the same amount of times, and have no pain when I wee. I have no issues with my stream either.", "<PERSON> has noticed that my skin and eyes have become yellow. We first thought it was a bit of extra tan. But it seems to be getting worse now too. <PERSON> first mentioned me looking a bit yellow about 2 weeks ago.", "I have lost about 4kg over the past 3 months or so, which is odd! I haven't been trying to lose weight and my weight is usually very stable. My appetite, eating and exercise haven't changed.", "Nothing seems to be helping my symptoms... I've tried getting some rest and eating normally. That hasn't helped!", "I don't have any tummy pain, fevers or abdominal distension.", "I haven't noticed any lumps, bumps or rashes anywhere.", "My remaining review of symptoms is normal."], "PMHx": ["I have not been unwell recently, nor been around unwell people.", "I don't have any ongoing medical conditions.", "I don't take any medications.", "I have not had any previous surgery.", "I have been through menopause - thank goodness that's over!", "I have never been diagnosed with cancer before.", "I don't have inflammatory bowel disease either!"], "FMHx": ["My parents are now in their late 80s, and still well! They both have foggy memories at times, but are still healthy.", "I have two sisters who are as fierce as me! My sisters, <PERSON> and <PERSON>, are both well! I'm the eldest."], "SHx": ["I've recently retired from working as architect, specialising in retro design.", "I'm happily married to <PERSON>. He is my second husband of 16 years. We are so good together! He is my world.", "Aside from <PERSON>, I have no other sexual parters.", "<PERSON> and I have all kinds of sex. We no longer use protection as we have been together so long, and I've been through menopause.", "I don't smoke or take drugs at all. I've never used needles or intravenous drugs!", "I drink a glass of red wine everynight, preferably an Italian pinot noir! I rarely drink more than that - maybe only on special occaisons!", "My drinking hasn't changed - I've never been a heavy drinker previously.", "I haven't travelled anywhere recently.", "In our spare time, <PERSON> and I like to get in the garden and grow our own fruit and vege.", "I'd say my diet is relatively healthy. I have mostly fresh food, a little bit of red meat, and a bit of dairy throughout the day.", "I have two daughters with my previous husband, <PERSON>. Our adult daughters are <PERSON> and <PERSON><PERSON><PERSON>.", "<PERSON> and I have two dogs. Two greying daschunds, named <PERSON><PERSON><PERSON> and <PERSON>.", "We walk our daschunds daily as exercise. My exercise hasn't changed recently."]}, "marking_rubric": {"HOPC": [{"title": "<PERSON>le Stools", "score": 7, "subcategories": [{"title": "Severity", "score": 1, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantiative", "score": 1, "items": ["Amount"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Comparison", "Asking Generally"]}]}, {"title": "Contributing Factors", "score": 2, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 1, "items": ["Travel", "Patient Ideas", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "Symptoms", "score": 11, "subcategories": [{"title": "Itch", "score": 1, "items": ["Itch"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Fatigue", "score": 1, "items": ["Lethargy"]}, {"title": "Bruising", "score": 1, "items": ["Bruising", "<PERSON>'s Sign", "Grey-<PERSON>'s Sign"]}, {"title": "Jaundice", "score": 1, "items": ["Yellow Eyes", "Yellow Skin"]}, {"title": "Swelling", "score": 1, "items": ["Legs"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Bone Pain", "score": 1, "items": ["Bone Pain"]}, {"title": "Dizziness", "score": 1, "items": ["Dizziness"]}, {"title": "Weight Loss", "score": 1, "items": ["Weight Loss"]}, {"title": "Palpitations", "score": 1, "items": ["Palpitations"]}, {"title": "Shoulder Pain", "score": 1, "items": ["Shoulder Pain"]}, {"title": "Abdominal Pain", "score": 1, "items": ["Abdominal Pain"]}, {"title": "Coughing Blood", "score": 1, "items": ["Coughing Blood"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urinary Symptoms", "score": 1, "items": ["<PERSON> Urine", "Asking Generally"]}, {"title": "Shortness of Breath", "score": 1, "items": ["Shortness of Breath"]}, {"title": "Abdominal Distension", "score": 1, "items": ["Abdominal Distension"]}, {"title": "Altered Bowel Habits", "score": 1, "items": ["Diarr<PERSON>a", "Malodorous Stools"]}, {"title": "Altered Mental Status", "score": 1, "items": ["Confusion", "Memory Loss"]}, {"title": "Loss of Consciousness", "score": 1, "items": ["Loss of Consciousness"]}]}], "PMHx": [{"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Metabolic Disease History", "score": 1, "items": ["Diabetes"]}, {"title": "Autoimmune Disease History", "score": 1, "items": ["Asking Generally", "<PERSON><PERSON><PERSON>'s Syndrome", "Systemic Lupus E<PERSON>thematosus"]}, {"title": "Infectious Disease History", "score": 1, "items": ["HIV", "Asking Generally"]}, {"title": "Hepatobiliary Disease History", "score": 1, "items": ["Cirr<PERSON>is", "Hepatitis", "Gallstones", "<PERSON><PERSON><PERSON>", "Cholecystitis", "Liver Failure", "Asking Generally", "Pancreatic Cancer", "Cholangiocarcinoma", "Fatty Liver Disease", "Alcoholic Liver Disease", "Primary Sclerosing Cholangitis"]}, {"title": "Cardiovascular Disease History", "score": 1, "items": ["Dyslipidaemia"]}, {"title": "Gastrointestinal Disease History", "score": 1, "items": ["Pancreatitis", "Asking Generally", "Ulcerative Colitis", "Inflammatory Bowel Disease"]}]}, {"title": "Medications", "score": 1, "subcategories": [{"title": "Risk and Protective Factors", "score": 1, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Steroids", "Diuretics", "Antibiotics", "Antiepileptics", "Antiglycaemics", "Asking Generally"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Non-Prescription Medications"]}]}]}, {"title": "Past Surgical History", "score": 1, "subcategories": [{"title": "Past Surgical History", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Abdominal Surgery", "Endoscopic Retrograde Cholangiopancreatography"]}]}, {"title": "Past Surgical History (2)", "score": 1, "items": ["Cholecystectomy", "Asking Generally", "Abdominal Surgery", "Endoscopic Retrograde Cholangiopancreatography"]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Cancer", "Gallstones", "Liver Cancer", "Pancreatitis", "Liver Disease", "Asking Generally", "Primary Sclerosing Cholangitis"]}], "Social": [{"title": "Social History", "score": 5, "subcategories": [{"title": "Diet", "score": 1, "items": ["Fatty Foods", "Asking Generally", "Diet at Baseline"]}, {"title": "Travel History", "score": 1, "items": ["Emigration", "Recent Travel"]}, {"title": "Alcohol History", "score": 1, "items": ["Drink of Choice", "Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Patient Demographics", "score": 1, "items": ["Weight", "Ethnicity"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexually Active", "Number of Sexual Partners", "Frequency of Unprotected Sex", "Frequency of Sexual Intercourse"]}, {"title": "Contraceptive Use", "score": 1, "items": ["Condom Use", "Asking Generally"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent", "<PERSON><PERSON><PERSON>"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Concerns", "score": 1, "items": ["Why Presenting Today"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Gender", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}