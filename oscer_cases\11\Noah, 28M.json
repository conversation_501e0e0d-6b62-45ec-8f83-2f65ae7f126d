{"metadata": {"name": "Noah, 28M", "scraped_at": "2025-09-05T12:58:32.849738", "script_url": "https://www.oscer.ai/pwf/script/1gqDjnJVXB4C"}, "tabs": {"doctor_information": {"Intro": "You are seeing <PERSON>, a 28 year old barber. <PERSON> prefers the pronouns they/them. They have presented to the emergency department with a 2 day history of haematuria. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>.", "My pronouns are they/them.", "I’m a 28 year old man and I work as a Barber.", "I’ve come to the doctor today because I have been seeing some blood in my urine in the past two days."], "Persona": ["I am a relaxed person. I care a lot about my health.", "I have never seen blood in my urine and it has gotten me worried seeing my urine red as that’s not normal for me. Could this be a kidney stone?"], "HOPC": ["My urine has become bloody in the past 2 days.", "I first saw my urine was bloody yesterday morning.", "I thought to wait and see if it gets better but it’s been tea-coloured since then.", "The last time I urinated today, my urine colour was less red and more bown.", "My urine looks a bit frothy.", "I don’t have any pain when I pee.", "My urine volume has been normal. I’m not thirsty and have not drunk a lot.", "I don’t have any abdominal pain or joint pain.", "I’m currently experiencing symptoms of a cold. I have a runny nose and cough.", "I was feverish yesterday. I don’t have a fever at the moment.", "I haven’t noticed any leg swelling.", "I’m not experiencing any back or flank pain."], "PMHx": ["I’ve been generally okay. I was circumcised when I was 8 months old.", "My blood pressure is on the upper normal range but I don’t have high blood pressure per se.", "I haven’t had any UTIs.", "I’m not on any medications other than the occasional painkiller."], "FMHx": ["My aunt has passed renal stones a few times in the past few years.", "Everyone else in my family is healthy."], "SHx": ["I’ve been a barber since graduating high school.", "I’m on a vegan diet. I always try to incorporate protein and iron in my diet.", "I occasionally smoke with my mates in social gatherings but never alone.", "I drink socially. Usually I have 2-4 beers on a night out.", "I live in a share house."]}, "marking_rubric": {"HOPC": [{"title": "Haematuria", "score": 12, "subcategories": [{"title": "Quality", "score": 2, "subcategories": [{"title": "Colour", "score": 1, "items": ["Red or Pink", "Asking Generally", "Dark Red or Brown"]}, {"title": "Character", "score": 1, "items": ["Timing of Blood", "Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Amount of Blood", "Scale out of 10", "Presence of Clots"]}]}, {"title": "Time Course", "score": 4, "subcategories": [{"title": "Time Course", "score": 3, "items": ["Onset", "Offset", "Duration", "Episodic", "Change Over Time"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Injuries", "Context at Onset", "Lifestyle Changes"]}]}]}], "Assoc.": [{"title": "Urinary Symptoms", "score": 2, "items": ["Frothy Urine", "Urinary Urgency", "Asking Generally", "Malodorous Urine", "Painful Urination", "Urinating at Night"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "<PERSON><PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON><PERSON>"]}, {"title": "Swelling", "score": 1, "items": ["Asking Generally"]}, {"title": "Vomiting", "score": 1, "items": ["Vomiting"]}, {"title": "Flank Pain", "score": 1, "items": ["Flank Pain"]}, {"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Appetite Change", "score": 1, "items": ["Loss of Appetite"]}, {"title": "Urethral Discharge", "score": 1}, {"title": "Shortness of Breath when Laying Down", "score": 1, "items": ["Shortness of Breath when Laying Down"]}], "PMHx": [{"title": "Medications", "score": 2, "subcategories": [{"title": "Prescription Medications", "score": 1, "items": ["Asking Generally", "Past Medications"]}, {"title": "Non-Prescription Medications", "score": 1, "items": ["Past Medications", "Non-Prescription Medications"]}]}, {"title": "Past Medical History", "score": 2, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness", "Asking Generally"]}, {"title": "Infectious Disease History", "score": 1, "items": ["Urinary Tract Infection", "Respiratory Tract Infection"]}, {"title": "Genitourinary Disease History", "score": 1, "items": ["Bladder Cancer", "Nephrolithiasis", "Asking Generally"]}]}, {"title": "General History", "score": 1, "subcategories": [{"title": "Allergies", "score": 1, "items": ["Asking Generally"]}, {"title": "Vaccinations", "score": 1, "items": ["Current Vaccination Status"]}, {"title": "Tests and Procedures", "score": 1, "items": ["Cancer Screening"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["IgA Nephropathy", "Asking Generally", "Autoimmune Disease"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Diet", "score": 1, "items": ["Salt-Rich Diet", "Asking Generally", "Red Meat-Rich Diet"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status", "Amount Currently"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupational Exposure", "score": 1, "items": ["General <PERSON><PERSON>s", "Chemicals or Toxins"]}]}, {"title": "Detailed Sexual History", "score": 1, "subcategories": [{"title": "Sexual History", "score": 1, "items": ["Sexually Active", "Frequency of Unprotected Sex"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}, {"title": "Patient Identity", "score": 1, "items": ["Age", "Preferred Name", "Patient Identity"]}, {"title": "Presenting <PERSON><PERSON><PERSON><PERSON>", "score": 1, "items": ["Presenting <PERSON><PERSON><PERSON><PERSON>"]}]}}}