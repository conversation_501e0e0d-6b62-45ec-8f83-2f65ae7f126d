{"cells": [{"cell_type": "code", "execution_count": null, "id": "9a8affee-648b-467f-b7cb-658414bb52e5", "metadata": {}, "outputs": [], "source": ["import time\n", "from selenium import webdriver\n", "from selenium.webdriver.common.by import By\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.action_chains import ActionChains\n", "from webdriver_manager.chrome import ChromeDriverManager\n", "\n", "# Setup Chrome options\n", "options = Options()\n", "options.add_argument(\"--start-maximized\")\n", "driver = webdriver.Chrome(service=Service(\n", "    ChromeDriverManager().install()), options=options)\n", "\n", "try:\n", "    # 1. Open the sign-in page directly\n", "    driver.get(\"https://www.oscer.ai/signin\")\n", "\n", "    # 2. Fill in credentials\n", "    email_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"email\"))\n", "    )\n", "    password_input = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located((By.ID, \"password\"))\n", "    )\n", "\n", "    email_input.clear()\n", "    email_input.send_keys(\"<EMAIL>\")\n", "    password_input.clear()\n", "    password_input.send_keys(\"techrise01badoscer\")\n", "\n", "    # 3. Click the \"Sign in\" button\n", "    login_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"//button[.//span[text()='Sign in']]\"))\n", "    )\n", "    login_button.click()\n", "\n", "    # 4. After login, go directly to Scripts page\n", "    WebDriverWait(driver, 20).until(\n", "        EC.url_contains(\"/dashboard\")\n", "    )\n", "    driver.get(\"https://www.oscer.ai/dashboard/scripts\")\n", "\n", "    # 5. Wait until script cards load\n", "    first_card = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_element_located(\n", "            (By.XPATH, \"(//div[contains(@class, 'jss308')])[1]\"))\n", "    )\n", "\n", "    # 6. <PERSON><PERSON> over the first card to reveal \"Open Script\"\n", "    actions = <PERSON><PERSON><PERSON><PERSON>(driver)\n", "    actions.move_to_element(first_card).perform()\n", "\n", "    # 7. Click \"Open Script\" button that appears\n", "    open_button = WebDriverWait(driver, 10).until(\n", "        EC.element_to_be_clickable(\n", "            (By.XPATH, \"(//span[text()='Open Script'])[1]\"))\n", "    )\n", "    open_button.click()\n", "\n", "    # 8. Wait for tab list to load (using tour-id which is stable)\n", "    tabs = WebDriverWait(driver, 10).until(\n", "        EC.presence_of_all_elements_located(\n", "            (By.XPATH, \"//ul[@role='tablist']//div[@tour-id]\"))\n", "    )\n", "\n", "    # 9. Iterate through each tab\n", "    for tab in tabs:\n", "        # e.g. \"tab-Doctor Information\"\n", "        tab_name = tab.get_attribute(\"tour-id\")\n", "        tab.click()\n", "        time.sleep(2)  # allow content to load\n", "\n", "        print(f\"\\n===== {tab_name} =====\")\n", "\n", "        if \"Doctor Information\" in tab_name:\n", "            active_panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((\n", "                    By.XPATH,\n", "                    \"//div[@role='tabpanel' and not(contains(@style,'display: none'))]\"\n", "                ))\n", "            )\n", "            raw_text = active_panel.text\n", "            print(\"\\n===== Doctor Information (raw) =====\\n\", raw_text)\n", "\n", "            # Structure by headings\n", "            sections = {}\n", "            lines = raw_text.split(\"\\n\")\n", "            current = \"Intro\"\n", "            sections[current] = \"\"\n", "            for line in lines:\n", "                if line in [\"Requirements\", \"Finished?\", \"Ready to see your results?\"]:\n", "                    current = line\n", "                    sections[current] = \"\"\n", "                else:\n", "                    sections[current] += line + \" \"\n", "            print(\"\\n===== Doctor Information (structured) =====\")\n", "            for k, v in sections.items():\n", "                print(f\"\\n{k}:\\n{v.strip()}\")\n", "\n", "        elif \"<PERSON>rip<PERSON>\" in tab_name:\n", "            # Find the associated panel id from the selected tab's parent <li role=\"tab\">\n", "            tab_li = tab.find_element(By.XPATH, \"ancestor::li[@role='tab']\")\n", "            panel_id = tab_li.get_attribute(\"aria-controls\")\n", "\n", "            # Wait for the correct tabpanel by id (e.g., react-tabs-3)\n", "            panel_xpath = f\"//div[@role='tabpanel' and @id='{panel_id}']\"\n", "            panel = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.XPATH, panel_xpath))\n", "            )\n", "\n", "            # Inside that panel, wait for the content container\n", "            container = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']\"))\n", "            )\n", "\n", "            # Now extract sections: each <p id=\"...\"> followed by its <ul>\n", "            sections = {}\n", "            headings = WebDriverWait(driver, 10).until(\n", "                EC.presence_of_all_elements_located(\n", "                    (By.XPATH, f\"{panel_xpath}//div[@id='containerElement']//p[@id]\"))\n", "            )\n", "\n", "            for heading in headings:\n", "                title = heading.text.strip()\n", "                try:\n", "                    ul = heading.find_element(\n", "                        By.XPATH, \"following-sibling::ul[1]\")\n", "                    items = [li.text.strip()\n", "                             for li in ul.find_elements(By.TAG_NAME, \"li\")]\n", "                    sections[title] = items\n", "                except:\n", "                    sections[title] = []\n", "\n", "            # Print nicely (keeps your original output style)\n", "            print(\"\\n===== Script (structured) =====\")\n", "            for section, items in sections.items():\n", "                print(f\"\\n{section}:\")\n", "                for item in items:\n", "                    print(f\" - {item}\")\n", "\n", "        elif \"Marking Rubric\" in tab_name:\n", "finally:\n", "    pass  # keep browser open for debugging\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}