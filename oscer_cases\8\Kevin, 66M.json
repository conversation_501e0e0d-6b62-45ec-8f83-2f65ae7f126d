{"metadata": {"name": "<PERSON>, 66M", "scraped_at": "2025-09-05T12:13:41.759827", "script_url": "https://www.oscer.ai/pwf/script/X4xXrZbGHmHw"}, "tabs": {"doctor_information": {"Intro": "<PERSON> is a 66-year-old man presenting to the ED with elbow pain. Please take a history from <PERSON> with the aim of establishing a diagnosis.", "Requirements": "Please interview <PERSON> with a view of establishing a diagnosis. You are not expected to examine the patient.", "Finished?": "Once you’re satisfied that you’ve finished taking a history from the patient the Marker will ask you some questions relevant to the diagnosis.", "Ready to see your results?": "Only the <PERSON>er can submit the case, so let them know you’re ready to see how you went..."}, "script": {"Setting": ["My name is <PERSON>, I am 66 years old and my pronouns are he/him", "I have come to the ED because I am worried about a pain in my elbow"], "Persona": ["I am a fit and well 66-year-old, I don't need to see the doctor very often", "I am a bit concerned about the pain in my elbow, it has been slowly getting worse over the last 5 years"], "HOPC": ["I have some pain in my right elbow", "The pain first began over 5 years ago, recently it has been getting much worse", "The pain is always worse at the end of the day, particularly if I have done a lot of work with my arms", "I get pain when I move my elbow or when I put any load through my arm, for instances when I try to lift something up", "In the past I was pain free most of the time, but slowly over the last five years this has progressed to having elbow pain most of the time", "The severity of the pain seems to be increasing too", "The pain is relieved by rest and when I take anti-inflammatories", "My elbow is quite stiff by the end of the day too", "My elbow is often stiff in the morning when I first wake up, this only last about 20 minutes", "I have not recently injured my elbow", "My elbow does not lock", "My elbow seems a bit swollen sometimes", "My elbow is not red or hot", "I haven't noticed any sensation changes in my elbow or arm", "My elbow does not feel weak", "My elbow does not feel unstable", "I don't have any lumps, bumps or deformities on my hands", "I do not have pain in any of my other joints", "I am not short of breath", "I do not get chest pain", "I have not noticed any strange bruising", "I have not lost any weight recently", "My appetite is fine", "I have not had any fevers", "I do not get night sweats"], "PMHx": ["I have hypertension controlled with perindopril, 2.5mg every day", "I take over the counter non-steroidal anti-inflammatories when my pain is bothering me, almost 5 times a week now", "I have never had any surgery", "I have no allergies", "I am up to date with all of my vaccinations"], "FMHx": ["My brothers both have some hypertension, my parents died from old age a couple of years ago"], "SHx": ["I am a retired secondary school teacher", "I live at home with my wife", "Nothing in my life is particularly stressful at the moment", "I am a healthy weight (75kg and 185cm tall)", "I sleep well", "My diet is very good since I was diagnosed with hypertension 5 years ago", "I used to play competitive tennis, I played for 40 years but my elbow would often become sore afterwards so I stopped", "I still walk at least 5 kilometres a day to keep fit", "I have never smoked cigarettes", "I enjoy a glass of red wine with dinner every night", "I never drink more than a glass", "I have never used recreational drugs"]}, "marking_rubric": {"HOPC": [{"title": "Elbow Pain", "score": 14, "subcategories": [{"title": "Quality", "score": 1, "subcategories": [{"title": "Character", "score": 1, "items": ["<PERSON>", "<PERSON><PERSON><PERSON>", "Asking Generally"]}]}, {"title": "Location", "score": 2, "subcategories": [{"title": "Location", "score": 1, "items": ["Side", "Other Joints", "Asking Generally"]}, {"title": "Radiation", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Severity", "score": 2, "subcategories": [{"title": "Qualitative", "score": 1, "items": ["Asking Generally", "Ability to Exercise", "Affecting Daily Life"]}, {"title": "Quantitative", "score": 1, "items": ["Scale"]}]}, {"title": "Time Course", "score": 5, "subcategories": [{"title": "Time Course", "score": 4, "items": ["Onset", "Duration", "Change Over Time", "Diurnal Variation", "Duration of Episodes", "Frequency of Episodes"]}, {"title": "Previous Episodes", "score": 1, "items": ["Asking Generally"]}]}, {"title": "Contributing Factors", "score": 4, "subcategories": [{"title": "Relieving Factors", "score": 1, "items": ["Morning", "Medications", "Asking Generally"]}, {"title": "Context and Aggravating Factors", "score": 3, "items": ["Morning", "Exercise", "Injuries", "Night-Time", "Context at Onset", "Lifestyle Changes", "Asking Generally about Aggravating Factors"]}]}]}], "Assoc.": [{"title": "<PERSON><PERSON>", "score": 1, "items": ["<PERSON><PERSON>"]}, {"title": "Fever", "score": 1, "items": ["Fever"]}, {"title": "Wounds", "score": 1, "items": ["Wounds"]}, {"title": "Red Eye", "score": 1, "items": ["Red Eye"]}, {"title": "Bruising", "score": 1, "items": ["Bruising"]}, {"title": "Swelling", "score": 1, "items": ["Swelling"]}, {"title": "Weakness", "score": 1, "items": ["Weakness"]}, {"title": "Skin Heat", "score": 1, "items": ["Skin Heat"]}, {"title": "Elbow Locking", "score": 1, "items": ["Elbow Locking"]}, {"title": "<PERSON><PERSON> Redness", "score": 1, "items": ["<PERSON><PERSON> Redness"]}], "PMHx": [{"title": "Past Medical History", "score": 3, "subcategories": [{"title": "Past Medical History", "score": 1, "items": ["Recent Illness"]}, {"title": "Musculoskeletal History", "score": 1, "items": ["Asking Generally"]}, {"title": "Rheumatological Disease History", "score": 1, "items": ["Joint Disease", "Rheumatoid Arthritis"]}]}], "FMHx": [{"title": "Family History", "score": 1, "items": ["Gout", "Oste<PERSON>th<PERSON>is", "Asking Generally", "Rheumatoid Arthritis"]}], "Social": [{"title": "Social History", "score": 3, "subcategories": [{"title": "Exercise", "score": 1, "items": ["Heavy Lifting", "Asking Generally"]}, {"title": "Alcohol History", "score": 1, "items": ["Drinking Status", "Amount Currently"]}, {"title": "Smoking History", "score": 1, "items": ["Smoking Status"]}, {"title": "Recreational Drug History", "score": 1, "items": ["User Status"]}]}, {"title": "Functional History", "score": 1, "subcategories": [{"title": "Domestic Activities Of Daily Living", "score": 1, "items": ["Cooking", "Cleaning", "Domestic Activities"]}, {"title": "Personal Activities Of Daily Living", "score": 1, "items": ["Feeding", "Dressing", "Toileting", "Asking Generally", "Personal Hygiene"]}]}, {"title": "Occupational History", "score": 1, "subcategories": [{"title": "Occupation", "score": 1, "items": ["Occupation", "Construction", "Specific Role", "Previous Occupation"]}]}], "Basics": [{"title": "Consent", "score": 1, "items": ["Consent"]}, {"title": "Introduction", "score": 1, "items": ["Introduction"]}]}}}